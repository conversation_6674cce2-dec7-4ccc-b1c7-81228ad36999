#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
有道云笔记.note文件转Markdown工具
作者: Claude 4.0 sonnet
功能: 将有道云笔记的XML格式文件转换为Markdown格式
"""

import html
import logging
import os
import re
import xml.etree.ElementTree as ET
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class NoteToMarkdownConverter:
    """有道云笔记转Markdown转换器"""
    
    def __init__(self):
        self.namespace = {'note': 'http://note.youdao.com'}
    
    def parse_note_file(self, file_path):
        """解析.note文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析XML
            root = ET.fromstring(content)
            return root
        except Exception as e:
            logging.error(f"解析文件失败: {e}")
            return None
    
    def extract_text_from_element(self, element):
        """从元素中提取文本内容"""
        text_elem = element.find('.//text')
        if text_elem is not None and text_elem.text:
            # 解码HTML实体
            return html.unescape(text_elem.text)
        return ""
    
    def get_heading_level(self, element):
        """获取标题级别"""
        level_attr = element.get('level')
        if level_attr:
            return int(level_attr)
        return 1
    
    def process_image(self, element):
        """处理图片元素"""
        source_elem = element.find('.//source')
        if source_elem is not None and source_elem.text:
            image_url = source_elem.text
            return f"![图片]({image_url})\n\n"
        return ""
    
    def convert_to_markdown(self, root):
        """将XML转换为Markdown"""
        markdown_lines = []
        
        # 查找body元素
        body = root.find('.//body')
        if body is None:
            logging.error("未找到body元素")
            return ""
        
        for element in body:
            if element.tag == 'heading':
                # 处理标题
                text = self.extract_text_from_element(element)
                if text.strip():
                    level = self.get_heading_level(element)
                    markdown_lines.append(f"{'#' * level} {text.strip()}\n")
                else:
                    # 空标题，添加空行
                    markdown_lines.append("\n")
            
            elif element.tag == 'para':
                # 处理段落
                text = self.extract_text_from_element(element)
                if text.strip():
                    markdown_lines.append(f"{text.strip()}\n\n")
                else:
                    # 空段落，添加空行
                    markdown_lines.append("\n")
            
            elif element.tag == 'image':
                # 处理图片
                image_md = self.process_image(element)
                if image_md:
                    markdown_lines.append(image_md)
        
        return ''.join(markdown_lines)
    
    def add_timestamp_header(self, markdown_content, original_filename):
        """添加时间戳头部"""
        # 从文件名中提取时间信息
        time_match = re.search(r'\((\d{4}-\d{2}-\d{2})\s+(\d{2})(\d{2})\)', original_filename)
        if time_match:
            date_str = time_match.group(1)
            hour_str = time_match.group(2)
            minute_str = time_match.group(3)
            
            # 构造完整的时间字符串
            full_time_str = f"{date_str} {hour_str}:{minute_str}:00"
            
            header = f"""<!--
📝 笔记信息
创建时间: {full_time_str}
修改时间: {full_time_str}
原始文件: {original_filename}
数据来源: 有道云笔记导出
-->

"""
        else:
            # 如果无法从文件名提取时间，使用当前时间
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            header = f"""<!--
📝 笔记信息
创建时间: {current_time}
修改时间: {current_time}
原始文件: {original_filename}
数据来源: 有道云笔记导出
-->

"""
        
        return header + markdown_content
    
    def convert_file(self, input_path, output_path=None):
        """转换单个文件"""
        if not os.path.exists(input_path):
            logging.error(f"输入文件不存在: {input_path}")
            return False
        
        # 解析文件
        root = self.parse_note_file(input_path)
        if root is None:
            return False
        
        # 转换为Markdown
        markdown_content = self.convert_to_markdown(root)
        
        # 添加时间戳头部
        original_filename = os.path.basename(input_path)
        markdown_content = self.add_timestamp_header(markdown_content, original_filename)
        
        # 确定输出路径
        if output_path is None:
            base_name = os.path.splitext(input_path)[0]
            output_path = f"{base_name}.md"
        
        # 写入Markdown文件
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            logging.info(f"转换成功: {input_path} -> {output_path}")
            return True
            
        except Exception as e:
            logging.error(f"写入文件失败: {e}")
            return False

def main():
    """主函数"""
    converter = NoteToMarkdownConverter()
    
    # 转换指定文件
    input_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).note"
    output_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).md"
    
    if converter.convert_file(input_file, output_file):
        print(f"✅ 转换成功: {output_file}")
    else:
        print("❌ 转换失败")

if __name__ == "__main__":
    main()
