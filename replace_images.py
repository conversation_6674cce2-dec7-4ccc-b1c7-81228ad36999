#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片链接替换工具
作者: Claude 4.0 sonnet
功能: 将Markdown中的网络图片链接替换为本地图片路径
"""

import os
import re
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ImageReplacer:
    """图片链接替换器"""

    def __init__(self):
        self.images_dir = "youdaonote/我的资源/笔记总结/images"
        self.local_image_map = {}
        self.load_local_images()

    def load_local_images(self):
        """加载本地图片文件映射"""
        if not os.path.exists(self.images_dir):
            logging.error(f"图片目录不存在: {self.images_dir}")
            return

        # 获取所有本地图片文件
        for filename in os.listdir(self.images_dir):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
                # 提取WEBRESOURCE ID
                if filename.startswith('WEBRESOURCE'):
                    # 提取ID部分 (去掉WEBRESOURCE前缀和后缀)
                    # 例如: WEBRESOURCEb7b529a394d8b1ab707b791f718f7349截图.png -> b7b529a394d8b1ab707b791f718f7349
                    resource_id = filename[11:]  # 去掉WEBRESOURCE前缀
                    resource_id = resource_id.split('image')[0].split('截图')[0].split('.')[0]
                    if resource_id:
                        self.local_image_map[resource_id] = filename

        logging.info(f"加载了 {len(self.local_image_map)} 个本地图片文件")
        # 显示前几个映射用于调试
        for i, (resource_id, filename) in enumerate(list(self.local_image_map.items())[:5]):
            logging.info(f"  示例 {i+1}: {resource_id} -> {filename}")

    def extract_resource_id(self, url):
        """从网络URL中提取资源ID"""
        # 匹配模式: https://note.youdao.com/yws/res/数字/WEBRESOURCE资源ID
        pattern = r'https://note\.youdao\.com/yws/res/\d+/WEBRESOURCE([a-f0-9]+)'
        match = re.search(pattern, url)
        if match:
            return match.group(1)
        return None

    def replace_image_urls(self, markdown_content):
        """替换Markdown中的图片URL"""
        # 匹配Markdown图片语法: ![alt](url)
        image_pattern = r'!\[([^\]]*)\]\((https://note\.youdao\.com/yws/res/[^)]+)\)'

        def replace_func(match):
            alt_text = match.group(1)
            original_url = match.group(2)

            # 提取资源ID
            resource_id = self.extract_resource_id(original_url)
            if resource_id and resource_id in self.local_image_map:
                # 使用本地图片路径
                local_filename = self.local_image_map[resource_id]
                local_path = f"images/{local_filename}"

                # 如果alt_text为空，使用更友好的描述
                if not alt_text or alt_text == "图片":
                    if "截图" in local_filename:
                        alt_text = "截图"
                    elif "image" in local_filename:
                        alt_text = "示例图"
                    else:
                        alt_text = "图片"

                logging.info(f"替换图片: {resource_id} -> {local_filename}")
                return f"![{alt_text}]({local_path})"
            else:
                # 保持原始URL，但添加提示
                if not alt_text or alt_text == "图片":
                    alt_text = "网络图片(可能无法显示)"
                logging.warning(f"未找到本地图片: {resource_id}")
                return f"![{alt_text}]({original_url})"

        # 执行替换
        new_content = re.sub(image_pattern, replace_func, markdown_content)
        return new_content

    def add_image_display_note(self, markdown_content):
        """添加图片显示说明"""
        note = """
> 📸 **图片显示说明**
> - 本文档中的图片已尽可能替换为本地路径
> - 如果图片无法显示，请确保images文件夹在正确位置
> - 部分图片可能仍使用网络链接，需要网络连接才能查看

---

"""
        # 在第一个标题后插入说明
        lines = markdown_content.split('\n')
        insert_index = 0

        # 找到第一个内容行（跳过注释和空行）
        for i, line in enumerate(lines):
            if line.strip() and not line.strip().startswith('<!--') and not line.strip().startswith('>'):
                insert_index = i + 1
                break

        lines.insert(insert_index, note)
        return '\n'.join(lines)

    def process_file(self, input_file, output_file=None):
        """处理Markdown文件"""
        if not os.path.exists(input_file):
            logging.error(f"输入文件不存在: {input_file}")
            return False

        try:
            # 读取原始文件
            with open(input_file, 'r', encoding='utf-8') as f:
                content = f.read()

            logging.info(f"开始处理文件: {input_file}")

            # 统计原始图片数量
            original_images = len(re.findall(r'!\[([^\]]*)\]\((https://note\.youdao\.com/yws/res/[^)]+)\)', content))
            logging.info(f"原始文件包含 {original_images} 张网络图片")

            # 替换图片URL
            new_content = self.replace_image_urls(content)

            # 添加图片显示说明
            new_content = self.add_image_display_note(new_content)

            # 统计替换后的本地图片数量
            local_images = len(re.findall(r'!\[[^\]]*\]\(images/[^)]+\)', new_content))
            network_images = len(re.findall(r'!\[([^\]]*)\]\((https://note\.youdao\.com/yws/res/[^)]+)\)', new_content))

            logging.info(f"替换完成: {local_images} 张本地图片, {network_images} 张网络图片")

            # 确定输出文件
            if output_file is None:
                base_name = os.path.splitext(input_file)[0]
                output_file = f"{base_name}_本地图片版.md"

            # 写入新文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(new_content)

            logging.info(f"处理完成: {output_file}")
            return True

        except Exception as e:
            logging.error(f"处理文件失败: {e}")
            return False

def main():
    """主函数"""
    replacer = ImageReplacer()

    # 处理转换后的Markdown文件
    input_file = "youdaonote/我的资源/笔记总结/JavaScript知识点大杂烩_简单提取版.md"
    output_file = "youdaonote/我的资源/笔记总结/JavaScript知识点大杂烩_本地图片版.md"

    print(f"🖼️ 开始替换图片链接...")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")

    if replacer.process_file(input_file, output_file):
        print(f"✅ 图片替换成功!")

        # 显示统计信息
        if os.path.exists(output_file):
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()

            local_count = len(re.findall(r'!\[[^\]]*\]\(images/[^)]+\)', content))
            network_count = len(re.findall(r'!\[([^\]]*)\]\((https://note\.youdao\.com/yws/res/[^)]+)\)', content))

            print(f"\n📊 替换统计:")
            print(f"   - 本地图片: {local_count} 张")
            print(f"   - 网络图片: {network_count} 张")
            print(f"   - 替换率: {local_count/(local_count+network_count)*100:.1f}%")

            print(f"\n📁 使用说明:")
            print(f"   1. 确保images文件夹与Markdown文件在同一目录")
            print(f"   2. 使用支持相对路径的Markdown预览器查看")
            print(f"   3. 本地图片路径格式: images/WEBRESOURCE*.png")
    else:
        print("❌ 图片替换失败!")

if __name__ == "__main__":
    main()
