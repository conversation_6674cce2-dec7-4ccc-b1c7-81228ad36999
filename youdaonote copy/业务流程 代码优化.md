<!--
📝 笔记信息
创建时间: 2021-06-22 10:09:36
修改时间: 2021-07-04 21:42:22
原始文件: 业务流程 代码优化.note
数据来源: 有道云笔记API
-->

一、设置登录页请求头

utils中的request.ts

if (token) {                                                                                                                                                                                                                                                                              

  if (enterpriseInfo !== undefined) {  

   // enterpriseInfo !== undefined 表示没有enterpriseInfo

    config.header.enterpriseInfo = enterpriseInfo                                              

  }

}



// 优化后



 if (token && enterpriseInfo) {

  config.header.enterpriseInfo = enterpriseInfo  

}



![](images/WEBRESOURCE0f474edf1dedc3673a5d9719ac87aaa4截图.png)

request.headers 是前端请求加的， response.headers 是后端返回的









新企业账号 





记事企 ： 14563201023 

14785211130  四一点三



13497568821 创建企业



13654789012  我是企业



已取消/已失效    灰色

已到达  橙色













登录成功后就存了这个企业信息到 localstorage 中，



创建企业的话要在创建接口返回成功值后将接口返回的数据存到 localstorage 中



import { UserModule } from '@/store/modules/user'

UserModule.login(data)



 /**

   * 用户登录Action

   * @param userInfo

   */

  @Action

  public async login(userInfo: { enterpriseDto: any; tokenDto: any }) {

    const {

      enterpriseDto,

      tokenDto: {

        accesstoken,

        subjectModel: { authority: { role = [], resource = [] } = {}, userInfoDto = {} } = {}

      }

    } = userInfo

    if (enterpriseDto) {

      const { id, name, code, assAttribute, enterpriseConfigDto: { maWxAppId ,enterpriseId} } = enterpriseDto

      userInfoDto.enterpriseId = enterpriseId

      const enterpriseDtoInfo = `id=${id}&name=${name}&code=${code}&assAttribute=${assAttribute}&wxAppId=${maWxAppId}&roleCode=0`

      setEnterPriseDto(Base64.encode(enterpriseDtoInfo))

    }

    // 保存token

    setToken(accesstoken)

    // 保存用户信息

    setUserInfo(userInfoDto)

    // 保存角色信息

    const roles = role.map((val: any) => val.roleCode)

    setRoles(roles)

    // 保存资源信息

    setResources(resource)

    // 进行权限过滤，重新生成路由

    const generatedRoutes = await PermissionModule.generateRoutes()

    router.addRoutes(generatedRoutes)

  }



$nextTick的使用





二、type涉及数字  或者     图片  前半段    相同的路径     的地方要用常量来定义，清晰明了，直接用数字的话类型一多又没有详细的注释不方便维护和修改



v-if="formItem.type === $consts.get('FORM_TYPE').TEXT_AREA"



:src="$consts.get('STATIC_IMG_URL') + 'logo_tiantian_bottom.png'"



常量一般都定义在consts.js文件中







三、登录注册页的二维码扫描

点击扫一扫登录注册，请求服务端返回登录注册的二维码地址显示在前端页面上；手机端使用微信进行扫一扫操作，手机点击授权登录注册，传用户账号，调根据账号获取用户信息接口，服务器获取微信用户的相关信息，web端轮询查看是否已经获取到用户信息，是的话传改二维码的url请求刷新二维码授权刷新登录接口：（有用户信息，重定向到首页，没有用户信息，进入创建企业页面）正常走流程。设置定时2s左右后跳转页面





四、数组根据字段名进行分组

```javascript
 // 预约信息按分组排序
  private accessInfoSortByGroup() {
    const map: any = {} // 临时记录根据 group 字段分组的临时键值对变量
    // this.sortByGroupArr = []  最终输出数组
    this.formInfo?.forEach((formItem: any) => {
      // 遍历 formInfo 数据
      if (!map[formItem.group]) {
        // 临时键值对变量中不存在此键
        this.sortByGroupArr.push({
          // 将此数据以json对象格式放到数组中{key1:value1,key2:value2},key1是分组的字段,key2是formInfo中的某一条数据
          group: formItem.group,
          data: [formItem]
        })
        map[formItem.group] = formItem //将未加入map的key放入
      } else {
        // 临时键值对中有此键
        this.sortByGroupArr.forEach((sortItem: any) => {
          // 遍历 this.sortByGroupArr 结果数组
          if (sortItem.group === formItem.group) {
            // 结果数据中此 this.sortByGroupArr[key1] 的值和该 formItem.group 的值相等时
            sortItem.data.push(formItem)
            return
          }
        })
      }
    })
    console.info(this.sortByGroupArr)
  }
```



```javascript
map对象相当于中间桥梁，
```



```javascript
// 结果
```



![](images/WEBRESOURCE26bc9add93b959dfb6d353a0f21493ed截图.png)



![](images/WEBRESOURCEcddb94ecea3bd508930840444b0e6a2c截图.png)



![](images/WEBRESOURCEdbb56e0f59413b632cf959855493cb1a截图.png)

