<!--
📝 笔记信息
创建时间: 2022-05-21 23:24:11
修改时间: 2022-05-22 12:41:31
原始文件: 使用 ECharts.note
数据来源: 有道云笔记API
-->

响应式布局

flexible.js  淘宝做适配的文件（兼容各浏览器的适配方案）

flexible.js + rem           rem的本质就是根据 根元素（html元素）的font-size 的大小进行改变的

百分比布局                 字体大小不变

rem + 媒体查询           字体大小跟着变 

数据可视化 -----     数字转化为图形

h5新增语义化标签： 

![](images/WEBRESOURCEaecead2c8805eaee96afe3afb5321255截图.png)

                                             