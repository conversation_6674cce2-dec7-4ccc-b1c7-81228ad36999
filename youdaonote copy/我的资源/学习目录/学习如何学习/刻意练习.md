<!--
📝 笔记信息
创建时间: 2022-01-06 23:58:59
修改时间: 2022-04-03 12:01:12
原始文件: 刻意练习.note
数据来源: 有道云笔记API
-->

1、分析 网站/APP 等 的页面数据结构 （利用抓包工具  拦截请求  查看前端的数据结构      找纯前端的项目  需要自己构造数据结构的  进行demo 练习）



2、解决问题的能力  /   “被动的安排”



3、关注博客/论坛 



4、模拟项目整个过程，从接需求讨论，开会 产品背景 / 目的 等  到技术选型 前端工作划分 项目初始化 开发计划 等系列流程 标准化 开发



5、多写 demo， 刻意练习 （同类型问题总结）   



6、制定具体可执行的落实计划，不要一下子太多，大的方向不变，按照清晰具体化的流程实施，每天的执行进度进行微调，从而指定适合的计划，严格执行。



量化的短期计划 和 长期计划 。



7、每天早上复习下昨天晚上/之前学习的知识。



8、晚上一般是9.30 - 11.30  两个小时     早上 6.30 - 8.00 一个半小时  



9、每天一道 leetcode ，锻炼逻辑思维能力，摸清业务流程，功能实现的每一个细节，能清晰的表述给他人听



10、遇到交互稍微复杂的，要先自己画草图，画流程图，先自己搞懂逻辑，搞懂每一步的过程，再写代码啊！



11、前面还是要多花点时间设计，写好代码的，不然后面很多bug都是一开始写不好产生的