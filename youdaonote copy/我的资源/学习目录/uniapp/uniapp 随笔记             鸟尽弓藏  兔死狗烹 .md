<!--
📝 笔记信息
创建时间: 2021-12-19 14:53:34
修改时间: 2022-04-02 23:16:34
原始文件: uniapp 随笔记             鸟尽弓藏  兔死狗烹 .note
数据来源: 有道云笔记API
-->

1、 跳转组件 naviagtor     ======>    等同于 view + this.$pi.navigateTo()   的功能

 

<navigator url="/src/pages/common/index">点击跳转</navigator>



2、   <block></block>   标签 相当于 vue 中的 template 标签 ，起临时的作用，最终是不会被渲染到html中的.

仅仅是一个包装元素，不会在页面中做任何渲染，只接受控制属性



![](images/WEBRESOURCE64ab58786953e714da302c8efc60d207截图.png)



3、 js 代码中 不识别 @/img/xxx.jpg 中的 @ 标识符  （而 template 中是可以识别的）  补充一下： require('@/assets/img/xxx.png') 这种写法是不支持动态属性的，打包的时候webpack 会报错的。    require('@/' + IMG_URL)  直接错误！



4、 uni 常用 方法  ：



1). uni.chooseImage ： 本地相册选择图片 / 相机拍照  sizeType = ['original ', 'compressed ' ],   sourceType=[ 'album', 'camera ' ],



2). uni.pageScrollTo ： 页面滚动到目标位置 ， scrollTop 直接设置为 999999  可以确保一直在最底部 （是最底的位置）， duration 设置 滚动动画的时长



3). uni.setStorageSync  / uni.getStorageSync()（保存到 localStorage 中， 同步接口）



![](images/WEBRESOURCEbe96f822fa43fbe6d728b2ac9fb1c4f6截图.png)

key 只能是字符串， 所以不能保存对象格式， 那就使用 JSON.stringfy() 进行转换



e.g.   uni.setStorageSync(''chartList', JSON,stringfy(this.chartList) )



4). uni.setStorage / uni.getStorage()（保存到 localStorage 中， 同步接口）  removeStorage / clearStorage  



同 localStorage 相关 API 



![](images/WEBRESOURCEfa595ed53a86cdf42b1098b9c197ea4a截图.png)

还有很多很多，体验一下 ...



![](images/WEBRESOURCE12253166da719d2b1abe8749fc3e3a9c截图.png)

![](images/WEBRESOURCE0e78a7c5b751b9e65ff247101d250f2a截图.png)

慢慢看，用到了就去查叭~，，





5、  Action 中 只能 commit Mutation  中的

![](images/WEBRESOURCE0b2d6de920195c040ffb6949f75382d2截图.png)



6、process.env.NODE_ENV    判断当前运行环境  



![](images/WEBRESOURCE6639bdcc69d26c1cb17c36365909cf53截图.png)



 7、判断平台 （是 h5 还是 微信/百度/支付宝/字节 小程序 还是 公众号 / 还是 APP / 还是 其他...）



1）、编译期判断



 使用条件编译    判断

![](images/WEBRESOURCE8a48fe90b31431a8273a8b792ad40695截图.png)

tempalte模板中 ：    

          <!-- #ifdef MP-WEIXIN -->
                <official-account></official-account>
            <!-- #endif -->

js代码中 ：     

  

![](images/WEBRESOURCE09cc7bcfc4b369c5ada83a33c2dab972截图.png)

style样式中 ：   

![](images/WEBRESOURCE526687eccd83ac06fe49eb52c1ae74a4截图.png)



2）、运行期判断 



### uni.getSystemInfoSync().platform

相当于 this.$core.system.getPlatform()



小程序环境：

   ·  this.$core.system.getPlatform()  ：

![](images/WEBRESOURCEb37b7412c1a940ed7a9ab273753b3302截图.png)

（运行在微信小程序环境中）



    ·  uni.getSystemInfoSync.platform  ：  

![](images/WEBRESOURCE6b003e7b0cab14f2eba68eca9b854ea3截图.png)

（运行在微信开发者工具中）



8、 使用  await 优化 uni  的 回调方法（success...），减少嵌套，便于阅读



一般的uni方法的写法： 

const that = this



      uni.scanCode({

        success: function(res) {

          console.log(11111)

          const result = that.$utils.common.qrCode2Router(res.result)

          if (result) {

            that.$pi.navi.navigateTo(result.path, result.params)

          } else {

            that.$toast('无效二维码')

          }

        }

      })



await  写法

      let [, res] = await uni.scanCode()

      if (res?.errMsg !== 'scanCode:ok') return // 非二维码

      const result = this.$utils.common.qrCode2Router(res.result)

      if (!result) return this.$toast('无效二维码')

      this.$pi.navi.navigateTo(result.path, result.params)





9、  piui 挂载的 全局方法



1）、 this.$pi     piui  tools 的相关常用的方法   （基本需要用的都在这里有）

![](images/WEBRESOURCEcb73691f9e6d8651eec74a606df84bde截图.png)



  this.toast('xxxxx')  === this.$pi.toast.info('xxxxx')    约等于  uni.showToast({ title: 'xxxxx' })



2）、 this.$core    获取一些项目的全局信息（登录授权信息/token信息/系统运行平台信息/接口请求信息 ...）

![](images/WEBRESOURCE157fc8bc5500f002ef27664574145e6f截图.png)

3）、 this.$utils    项目  @/utils 文件夹下的 工具库文件 , 一般存储用于公共调用的方法

![](images/WEBRESOURCEbd1892430b74db5c94ea25c45e1eac12截图.png)

![](images/WEBRESOURCE2ad0cb3c065c3cf36030aeecca2f4277截图.png)

4）、 this.$filters   全局注册好的过滤器 （直接拿来就用）



![](images/WEBRESOURCEc4cdb964730cb038be6139a9f930fccf截图.png)



![](images/WEBRESOURCE40928179b7787c162d5c2f539df41917截图.png)



5）、 this.$consts    项目定义的全局常量信息    （包括一些配置信息）

![](images/WEBRESOURCEc6da5906e56626424163cc9fbcc1e294截图.png)



10、@font-face  可以 自定义字体  

@font-face 不仅可以放在在CSS的最顶层, 也可以放在 @规则 的 条件规则组 中。



App.vue 中：

对应的字体包放到 oss 上面：

![](images/WEBRESOURCEd177404cc15e94fed5bed017c485330e截图.png)

// 需要用到该字体的地方直接使用 .pi-font-dab 该 class 类名就好了

![](images/WEBRESOURCE83fb7436d98f938de33a654db0f3f0dd截图.png)



11、引入组件库的步骤

（1）,  按需引入组件库 piui



![](images/WEBRESOURCEc1cde5c2c21cb5866a218cc0608fd76e截图.png)



（2）, uni.scss文件中全局覆盖piui组件库的样式为自定义需要的样式

![](images/WEBRESOURCE0bbe677342b053083c2ea3b481a21d15截图.png)

（3）, App.vue 文件中导入 index.scss 定义页面的样式

![](images/WEBRESOURCE74eca65b689d10532ab53b9987d3f7bd截图.png)







12、page.json 配置文件 一些 常用的配置属性：

![](images/WEBRESOURCEd35b35d4861f28c8b44ddd97b4868302截图.png)



![](images/WEBRESOURCEf2a2c32264a5295fb911f2a7f2a7fc71截图.png)



![](images/WEBRESOURCE8eb665dda5fa8d84a34f2895879a1443截图.png)

1)、 globalStyle 配置项：

navigationBarBackgroundColor: 导航栏（状态栏）背景颜色

navigationBarTextStyle: 导航栏标题颜色 （black / white）

navigationBarTitleText: 导航栏标题文字内容

navigationStyle: 导航栏样式  ( 默认都使用 custom 模式 )

backgroundColor: 下拉显示的窗口背景色

backgroundTextStyle: 下拉 loading 样式 （ dark / light ）

enablePullDownRefresh: 是否开启下拉刷新 



2)、tabBar 配置项：



![](images/WEBRESOURCE9f2444cf0322f7bd4c5b842e32620cfb截图.png)

color: tab 文字默认颜色

selectedColor: tab选中时的文字颜色

backgroundColor: tab 的背景色

borderStyle: tabbar 的上边框颜色 （ black / white ）

list: tab 列表



![](images/WEBRESOURCE5cc070e2d3fabfe51ed2796b9ad51c91截图.png)



![](images/WEBRESOURCEea4fbd22065a4a1e4119f2857bcaa463截图.png)



3)、subPackages 配置项：

root: 子包的根目录

pages:  子包由哪些页面组成



![](images/WEBRESOURCE416ee4bdc6be314003deede2d8ea1270截图.png)



![](images/WEBRESOURCE65bd7796f41c9131e6892879fb0c7a5e截图.png)

4)、 pages 配置项：

path: 配置页面路径

style: 配置页面的窗口表现   （同 globalStyle 中 的key值）



![](images/WEBRESOURCEc1c88b75c4a50f4f074cf6f60727c913截图.png)



13、 路由传参携带地址的 一般都要 进行 转码 （encodeURIComponent('xxx')）和 解码（decodeURIComponent('xxx')）



14、web-view 标签 显示 页面跳转

![](images/WEBRESOURCE824f2e32331b4bed073276120b39bc62截图.png)



![](images/WEBRESOURCEa68f6fbbda26b8f0aa4c7bda5151e642截图.png)



![](images/WEBRESOURCE31da245c195125749058b3807ffbfa82截图.png)



15、 小程序中的点击时间如果是 view、text 等原生标签（而非组件库的组件名标签）的话，都是用 @tap 事件 来 代替 click事件

![](images/WEBRESOURCE16e0d023b0d01ba9e84e705ff8211b58截图.png)

16、

![](images/WEBRESOURCEb01ee8f651817830792a700497579269截图.png)

17、移动端点击穿透问题：‘



![](images/WEBRESOURCE37043afb2ffe5efadd852e7da57505ee截图.png)

详细见 移动端页面点击穿透问题



18、  动态class 新写法 ：  const menu_active = true   :class = { 'pi-mg-top-10': menu_active }



19、用下 tailwind.css yyds



20、