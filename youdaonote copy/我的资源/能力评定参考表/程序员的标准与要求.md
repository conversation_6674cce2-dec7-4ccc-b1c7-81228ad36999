<!--
📝 笔记信息
创建时间: 2021-11-07 21:56:40
修改时间: 2021-11-07 21:56:49
原始文件: 程序员的标准与要求.note
数据来源: 有道云笔记API
-->




## 程序员的标准与要求


 


### 初级程序员


 仅能完成简单模块和项目的开发工作，难以胜任复杂模块的开发。通常是入行不久， 1 年及以下工作经验的同学。


 


#### 能力要求


 


1. 熟悉前端基础知识如 HTML、JS、CSS 。


1. 能够使用一门 MVVM 框架进行简单的业务开发。


1. 遇到复杂的组件和模块，会找现有的轮子使用。


1. 会使用百度、google 等检索工具搜索问题。


 


### 中级程序员（阿里 p5）


 在必要的辅导或标准流程支持下，能独立负责一个子模块或者一个项目的具体任务，对及时性和准确性负责。通常是 2-3 年工作经验的同学。


 


### 能力要求


 


1. 除了前端基础知识外，熟悉计算机、网络等专业基础知识。


1. 熟练掌握工作中使用的技术栈开发业务。（除了 MVVM 框架外，还能了解 webpack 的配置）。


1. 能了解一个需求从开发-上线整个生命周期，并对各个环节负责。


1. 具备基本的逻辑分析、问题分解、归纳总结等能力。


1. 了解基本的数据结构和算法，写代码较熟练。


1. 知道从靠谱的渠道去查找问题，在找不到合适轮子的时候，会造一些简单的轮子辅助业务开发。


 


### 高级程序员（阿里 p6）


 具有独挡一面的能力，能够高质量完成工作，能把握一个系统/团队的整体实现，在推行过程中能提炼新的方法或方案，或对现有方案提出改进建议并被证明有效。通常是 3 年+ 工作经验的同学。


 


#### 能力要求


 


1. 具有独挡一面的能力，能高质量的完成模块级的工作。


1. 熟练掌握工作中使用的技术栈，并能了解它们的实现原理。


1. 能够在一个需求从开发-上线整个生命周期中找到痛点，并能使用技术的手段解决，提升效率。


1. 能在模块维度对问题或需求做出分析和拆解，并做出相对合理的实现方案。


1. 熟练掌握基础的数据结构和算法，代码符合规范，逻辑清晰。


1. 有较好的技术选型能力。


 


## 程序员的进化之路


 


### 初级 -> 中级


 通常在初级这个阶段，是进步空间最大的阶段，这个阶段是不存在天花板的。


 


#### 编码


 编码是初级阶段最需要经常做的事情，古话说孰能生巧，写代码也是同样的道理。


 


-  写什么的样代码


 在公司要多做业务，尝试不同的业务，目标是让自己先成为一个熟练工。 下班后也可以折腾点小项目做，可以选择自己感兴趣的内容，尝试自己实现一遍。


 


-  如何写代码


 学会模仿，参考大牛的编码风格，照葫芦画瓢。 如果公司有代码规范，跟随着公司的代码规范走，如果没有，参考业界规范，并用工具（如 eslint）约束自己。 遇到一些不会写的，哪怕找到可以参考的代码，跟着抄一遍也行。


 


 


#### 工作


 大部分时间都在工作，那么如何合理的利用好 8 小时工作时间呢。


 


-  熟悉业务 不要仅仅埋头写代码，也需要对业务了解，认真参与需求评审环节，明确自己所做的任务。


 


-  熟悉工作流程


 要熟悉从需求-开发-联调-测试-上线的每个工作环节，认真按照规范来执行，对上线要有敬畏心。


 


-  提升效率


 工作的时候，尽量不要被打断，可以安排一个免打扰时段，比如下午的 2点-4点，只编码而不去处理其它事情。减少在群里 灌水和斗图的时间。


 


-  加班


 可以接受合理的加班（比如为了赶某一个需求加班一阵子），不接受没有任何补偿和调休的日常加班（996），因为日常 加班会压缩你学习和生活的时间。


 


 


#### 学习


 初级阶段是需要通过大量的学习来提升自己的能力。


 


-  明确自己的学习目标 学习是为了提升能力，能让你胜任更复杂和有挑战的工作。


 


-  应该学习什么内容 前端基础，JS、CSS、HTML 反复学习。 工作中用到的一门前端 MVVM 框架。 了解一些广度知识，比如 HTTP、正则表达式、Web安全、性能优化、设计模式，数据结构和算法。


 


-  找到合适的学习方式


 看书+编码：购买经典的书籍阅读，并作为平时写码的参考工具，看书和编码是一个反复的过程，基本就是看书->编码->看书->编码循环个 3-4 次，这个过程就是在不断修炼内功。


 视频：慕课网的入门实战视频，根据自己的需求购买，要充分学习自己购买的每一个视频，认真的跟着老师学习一遍写一遍，遇到不懂的要积极提问，学习在于深入而不在于多。视频比书籍的好处在于更贴合实践，也更贴合公司业务，另外，课程的升级和更新也更加灵活。


 官网文档：学习每一个新的技术栈，入门最好的方式是通过它的文档学习，可以系统的过一遍文档，手敲它每一个示例。


 社区：慕课网手记、掘金、CSDN、InfoQ 等中文社区，利用碎片化时间看一些高质量文章学习。


 GitHub：学会 GitHub 的使用，知道 clone、fork、pull、push 等基本操作。


 


-  花更多的学习时间 如果想比别人成长的快，就得花比别人更多的时间用来学习（这也是为什么不鼓励日常加班），尤其是初级这个阶段，成长曲线是非常快的。


 


-  知识沉淀 俗话说好记性不如烂笔头，可以养成写博客的习惯，把学习的内容用文字的形式记录下来，整理成文，并按学习的技术方向做归类。


 


 


#### 提问


 在初级阶段，免不了经常遇到问题，会提问也是一门技术活，好的提问姿势能让你事半功倍。


 


-  不好的提问方式


 “这个报错是什么意思？”；缺乏思考，没有体现自己关于问题的思考过程。


 “为什么我的代码和你的一模一样，就是运行不了？”；缺乏上下文，应告知代码是编译不过还是运行不起来、错误提示信息、相关代码、可能出错的代码分析。没有足够的上下文，神仙都不知道如何解决相关的问题。


 “为什么 xxx 能（不能）yyy”；问的问题本身就是一个错误的结论，通常是只是看到了现象，而没有分析问题的本质。


 


-  好的提问方式


 首先你在提问之前，自己应该经过充分的思考，并学会把问题拆解，在拆解的过程中就会把问题的复杂度降低。 其次要善用搜索引擎，把关键字提取出来去搜索引擎上去搜索，先尝试自己去解决问题。


 如果仍然不能解决，可以将问题的相关上下文、截图及必要标识及自己的思考过程、查找到的相关资料，以及自己的分析发送给想要帮助你的人，并能提供最小化复现的问题。


 最后，请将你的问题及解决方案重新整理，并告知帮助你的人问题已经解决，重新整理解决问题的过程会帮助你理清思路、从而对问题有着更深的理解。


 


 


### 中级 -> 高级


 到了中级阶段，基本上意味着你可以在大公司缺人的时候进入大公司工作了，如果想成为大公司不可拒绝的人才， 那你还需要朝着高级阶段努力。


 


#### 编码


 没错，到了中级阶段，你想要提升最重要的途径还是要编码。


 


-  写什么的样代码


 和初级不同，除了做业务之外，你也参与开发做一些偏技术类型的项目，比如参与组内组件库的开发，一些通用 JSSDK 的开发，这些会对你的技术要求会更高。


 有机会去参与平时使用到的一些开源技术栈的共建，比如想实现每个 feature 但现有开源库不满足，实现难度不大的情况下可以去提一个 pull request。


 


-  如何写代码


 学会模仿，除了参考大牛的编码风格，还要琢磨他们的编码思路，多思考他们为什么这么做。


 学会组件化、模块化的开发方式，学会复用和封装，减少 CV 的操作。


 对自己的代码质量负责，可以经常组织 code review。


 


 


#### 工作


 想要进阶，需要在工作方面有些产出。


 


-  业务思考


 要明白业务和公司的目标，参与到需求的早期阶段中，同时也要多思考业务，想着有没有通过技术手段来提升业务价值，比如移动端的白屏时间减少，可以带来很好的用户体验，提升用户的留存率。


 


-  技术思考


 思考业务开发中的一些痛点，如何用技术手段去优化业务的开发流程，提升开发效率，比如为业务量身定制一款脚手架工具。


 


-  优化工作流程


 不仅要熟悉从需求-开发-联调-测试-上线的每个工作环节，还要多思考每个环节有没有可以提升的点，特别是上线过程，比如能不能做到自动化，能不能做到小流量上线，能不能及时回滚代码。


 


-  技术分享


 把工作中遇到的一些问题的解决方案、学习的一些新技术，产出的一些技术项目总结下来，作为组内技术分享和输出，这样一是可以总结沉淀你的技术，二是可以让大家能知道你做的东西，并活跃组内的技术氛围，三是锻炼自己的表述能力，这个能力在大公司的晋升述职中非常重要。


 


 


#### 学习


 中级阶段仍然需要通过大量的学习来提升自己的能力。


 


-  明确自己的学习目标


 学习是为了提升自己的硬实力，不仅仅能够提升工作效率，还能让自己更好的服务于业务，升职加薪。


 


-  应该学习什么内容


 研究工作中所用到的工具链，不仅仅是运行时的 MVVM 框架，还可以是编译时的 webpack，必要的时候去研究其中的源码实现。


 深入学习一些广度知识，比如 HTTP、正则表达式、Web安全、性能优化、设计模式，数据结构和算法，并把它们应用到你的工作中。


 


-  找到合适的学习方式


 看书：找到所学习方向的经典书籍阅读，适当做一些学习笔记。


 视频：如慕课网的进阶实战视频，根据自己的需求购买。


 官网文档：可以尝试去看一下英文文档。


 社区：StackOverflow、HackerNews，慕课网手记、掘金等，除了利用碎片化时间看一些高质量文章学习，也可以往上面发布一些优质文章。


 GitHub：关注 trending，参与开源社区的共建，并尝试自己造一些轮子。


 


-  学习时间与深度


 在中级阶段，你仍然需要花很多时间去学习，这个时候要注重自己学习的深度了。


 


-  知识沉淀


 俗话说好记性不如烂笔头，可以养成写博客的习惯，把工作中深入学习到的一些非敏感知识记录下来并发布，而不仅仅去发布一些基础知识的学习了。


 


-  技术视野


 除了自己工作中的使用的技术栈之外，也要了解同类其它优秀的开源技术栈，从多个维度（feature 完整性、文档、上手难度、维护力度、生态等）去做对比。


 要保持对新技术的敏感的关注，并时刻思考这些技术能否为自己的业务带来价值，在需要的时候做合理的技术选型。


 


 


## 总结


 所谓技术能力其实就是解决问题的能力和学习能力，所以无论你在哪个阶段，甚至是更高的阶段，不断提升这两个能力都是你应该一直要做的事情。





作者：黄轶


链接：https://juejin.cn/post/6844903897593544718


来源：稀土掘金


著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。
