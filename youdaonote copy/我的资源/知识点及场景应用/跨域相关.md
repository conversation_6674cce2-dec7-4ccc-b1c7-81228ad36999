<!--
📝 笔记信息
创建时间: 2022-01-05 16:02:08
修改时间: 2022-01-06 10:42:43
原始文件: 跨域相关.note
数据来源: 有道云笔记API
-->

一、同源策略： 同源地址  之间进行的信息交互 



http://  note.youdao.com   (:80) /   old-web

 协议     域名（主机）     端口号        路径

                            （默认为80端口，可省略）

protocol   domain（host）  port          path



a.taobao.com 与 b.taobao.com 不是同源， 因为主机（域名）不同



二、由于浏览器的同源策略导致了跨域，同源策略是源于浏览器的 安全机制  （针对  接口请求 和 DOM查询），











三、跨域： 在不同源的网络地址之间进行信息交互   。



四、





CSRF  cross-site request forgery   跨站请求伪造



# XSS Cross-site scripting  跨站脚本攻击



