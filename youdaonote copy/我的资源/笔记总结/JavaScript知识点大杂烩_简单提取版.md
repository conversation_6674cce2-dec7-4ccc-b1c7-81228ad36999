# JavaScript 知识点大杂烩

> 📅 创建时间: 2021-08-06 13:13:00
> 📝 数据来源: 有道云笔记导出
> 🎯 内容: JavaScript 前端开发技巧和知识点整理
> 🔧 转换工具: Claude 4.0 sonnet 简单文本提取器

---

JavaScript 是 基于 原型 （对象）的语言 ？ 面向对象的JvaScript （OOJS）

JavaScript 中的面向对象 数据属性 + 访问器（getter/setter）属性

**!! 强制转为 布尔值**

让 (a == 1 && a == 2 && a == 3) 返回 true （== 存在 隐式类型转换）

## 一、js 中的 toString 方法

![图片](https://note.youdao.com/yws/res/1949/WEBRESOURCEb7b529a394d8b1ab707b791f718f7349)

(1)、返回 [表示对象] 的 [字符串]

(2)、检测对象的类型

(3)、返回 数字 对应进制 的 字符串

## 二、== 的 隐式类型转换 （类型不同的前提下才会转， 像同类型的 '' == '0' 结果就是 false）

### 有 NaN 的，返回 false （NaN 表示一个范围 不是具体的值/数字）A不是一个数字 B不是一个数字 但 A不一定就等于B涅 所以 NaN == NaN 返回值是 false

![图片](https://note.youdao.com/yws/res/1951/WEBRESOURCE4c7e915f0bcef6898790bf61497314ba)

### 有 字符串/布尔， 字符串/布尔转数字 （false转0，true转1）

### null == undefined 返回 true

### 有对象 （数组/对象/函数...），先调用 valueOf()方法 (转数字) 比较（如果不等）， 再调用 toString() 方法(转字符) 进行比较 （Date 对象 相反）

[Symbol.toPrimitive] （内置的 [[ToPrimitive]] 函数） > valueOf > toString

通过重写 对象的 valueOf / toString 方法 ，就可以比较了 a==1 && a == 2 && a== 3 .....

![图片](https://note.youdao.com/yws/res/833/WEBRESOURCEaff3cbca0f30210fdbdbead770692dae)

++i ： 变量自加1，表达式返回变量值

+在前，就先加1

i++ ： 表达式返回原值，变量自加1

### 考验js基础

nodeList 类数组

![图片](https://note.youdao.com/yws/res/7457/WEBRESOURCE8fac1c44318dfa45df0ee4b8845168f8)

打开淘宝网，怎么在console中获取当前网页最多的3个标签是啥，并且打印出来

思路： 先统计所有的标签， 把类数组转数组， 再遍历对象中的3个出现次数最多的标签

优化版：

常见的类数组（有数组索引从0开始 / 有 length 属性 / 无数组内置方法 stash、pop 等）：

字符串、arguments、NodeList、类数组对象( { length: 2 } )

![图片](https://note.youdao.com/yws/res/7459/WEBRESOURCE9e83d780684671546e4df193af403929)

### 多个 await 异步请求 可能不会按顺序执行，要在触发时进行调用，防止获取不到数据

插入： 数值方法

### 、 toFixed( n ) 方法 ： 将 数字 转为 字符串

### 数组转对象

`(1) Object.assign( { }, [ 1, 2, 3 ] )`

![图片](https://note.youdao.com/yws/res/7461/WEBRESOURCE8549b40555d148dc7f03756f6c3008ce)

(2) { ...[ 'a' , 'b', 'c' ] }

### 对象转数组

(1) 对象 for...in + 数组 push for...of... 遍历数组 ！！

两数之和优化：

var a = []

![图片](https://note.youdao.com/yws/res/7471/WEBRESOURCE136c0726c450534201021a2ee614b288)

`var o = {1: '淦', 2: '鈤'}`

`for (var i in o) {`

a.push(o[i])

}

console.log(a) // [ '淦', '鈤' ]

![图片](https://note.youdao.com/yws/res/7485/WEBRESOURCEae8ea438c5230a04f3d16d063db1545c)

```javascript
(2) Object.keys() / Object.values() / Object.entries() / Object.getOwnPropertyNames(obj) 包括 enumerable 对象 => 返回数组
```

`(3) Array.from() 浅拷贝 伪数组 （有length属性，索引） / 可迭代 （Set / Map 等） 为 数组`

(1) 转 String 为 数组 // String.split(' , ')

Array.from('foo')

// ['f', 'o', 'o']

![图片](https://note.youdao.com/yws/res/9179/WEBRESOURCE021797d6720f4f03833a61f07afa4250)

(2) + Set 去重

const set = new Set( ['foo', 'bar', 'baz', 'foo'] )

Array.from(set)

// ["foo", "bar", "baz"]

`Array.from() 非数组对象转数组`

![图片](https://note.youdao.com/yws/res/9190/WEBRESOURCE4f6d333c3be95159ea8067914d056de9)

构造 100 个 0 的 数组

`Array.from({length: 100}, () => 0)`

new Array(100).fill(0)

`Array.from({length: 100}).fill(0)`

`(4) Array.of( ) 用中括号 [ ] 将目标对象（可以是{} / [] / ' ' / number / boolean .... 很多都可 ）包裹起来 转为 数组`

![图片](https://note.youdao.com/yws/res/1726/WEBRESOURCE51a38737e0eef2ab993f00454e34323c)

```javascript
Array.of() 主要是 填补 new Array( ) 创建数组的缺陷（ new Array(3) 创建的是一个 [empty, empty, empty]， 不能创建长度为1的数组 [3]，Array.of(3) 创建的就是 [3] ）
```

`所以 创建数组 就可以有 4种 方法了： 字面量( [ ] )、new 关键字 ( new Array() )、Array.of()、Array.from()`

### 数组外面只能拿循环内数组最后一项的解决：

直接声明成数组，然后push到数组中，再将数组转字符串即可

var arr = [1, 2, 3]

![图片](https://note.youdao.com/yws/res/1730/WEBRESOURCE9c30252cc6ce6fdeecfa9172a70d0055)

var newArr = [ ]

`arr.forEach(item => {`

newArr.push(item)

})

newArr = newArr.join(' ')

![图片](https://note.youdao.com/yws/res/2965/WEBRESOURCE956003abf0f4161ee537063bb8ed8958)

console.log(newArr) / / 1 2 3

数组转字符串： join() / toString() / toLocalString()

### TS中使用lodash中的防抖函数debounce （知道原理，自己实现）

`小程序中使用防抖函数，注意不要用箭头函数形式，不然会报错 this指向问题找不到this 防抖节流函数 不使用 箭头函数 使用 function() { }`

### 查表法：

![图片](https://note.youdao.com/yws/res/2969/WEBRESOURCE52a9bed26099509312cd0367853258fe)

### 关于 Date() 内置对象

### 、默认是UTC格式的时间

对象形式：

字符串形式：

### 、转换为ISO 8601格式

![图片](https://note.youdao.com/yws/res/9196/WEBRESOURCE06f7399d69baeaa2d1d0a0a14fdc2dde)

Date.prototype.toISOString()

UTC 是时间标准；ISO-8601 是表示时间的一种标准格式

### 、常见方法：

getTime() // 返回时间按戳

setTime() // 返回时间戳

![图片](https://note.youdao.com/yws/res/5940/WEBRESOURCE9c1f799b14ac1a15945f2ea8fc9c2c27)

// UTC格式

UTC 转 ISO-8601 存在一定的时间差 ！

### delete（js方法）：删除对象的某个元素，被删除元素变成 empty / undefined，其余元素键值不变， 数组的长度不变

Vue.delete（vue方法）： 直接删除数组元素，改变数组的键值， 改变数组的长度

### 删数组

![图片](https://note.youdao.com/yws/res/5945/WEBRESOURCE558674ca8bb549bb299998040988585e)

### 删对象

Vue.delete(target, key)

target: 要删除的对象/数组/字符串..

key: 被删除对象的键/数组的下标/字符串的index

// 页面创建时绑定 监听页面刷新的事件

![图片](https://note.youdao.com/yws/res/5203/WEBRESOURCE594b252fd76d60457cef29edd41d5c99)

`window.addEventListener('beforeunload', this.beforeunloadFunc)`

设置addEventListener监听 beforeunload 事件不生效

// 页面实例销毁 在 destroyed 钩子中卸载 监听页面刷新的事件

private destroyed() {

window.removeEventListener('beforeunload', this.beforeunloadFunc)

![图片](https://note.youdao.com/yws/res/1665/WEBRESOURCEca321f3513ef14a17c4763b4a7c28940)

}

// 页面刷新触发 beforeunload 事件，对应的方法

private beforeunloadFunc() {

delete this.form.date

}

![图片](https://note.youdao.com/yws/res/996/WEBRESOURCE874dc87b98477d1f2f6ecd758d135fcf)

`window.addEventListener(ele, event, boolean)`

`document.addEventListener(ele, event, boolean)`

EventTarget.addEventListener( )

`const img = document.querySelector("img");`

`img.addEventListener("mousedown", start);`

![图片](https://note.youdao.com/yws/res/1501/WEBRESOURCE952a913550d8a4abb20160be319583ed)

`function start(e) {`

consoel.log(121212)

}

最后一个参数表示是 捕获阶段（true） 还是 冒泡阶段（false），默认是冒泡阶段（false）

捕获阶段（从上到下）：window 的先触发

![图片](https://note.youdao.com/yws/res/1495/WEBRESOURCE2feadd79f3526b0394aebb2fa3b0f62d)

冒泡阶段（从下到上）： document 的先触发

默认是先出发document，再触发window的

‘优雅’的删除对象中的元素：

### 

```javascript
用JSON.stringify，看着还算优雅。 var d=JSON.parse(JSON.stringify(data,function(key,value){ if(key=='created_at'||key=='deleted_at'||key=='updated_at'){ return undefined; }else{ return value; } }))
```

![图片](https://note.youdao.com/yws/res/1497/WEBRESOURCE8c7379ccfc083fc06f76efacfc65485f)

### 

要优雅的话，使用 Lodash 的 omit 方法移除不要的属性：

```javascript
const object = { 'a': 1, 'b': '2', 'c': 3 }; const result = _.omit(object, ['a', 'c']); // => { 'b': '2' }
```

或者用 pick 方法只留下需要的属性：

```javascript
const object = { 'a': 1, 'b': '2', 'c': 3 }; const result = _.pick(object, ['a', 'c']); // => { 'a': 1, 'c': 3 }
```

![图片](https://note.youdao.com/yws/res/1499/WEBRESOURCE504b7911abf7eb7362b5a058387e2f96)

### 

自己实现一个 omit 也是可以的

```javascript
// 投机取巧式 const omit = (obj, uselessKeys) => uselessKeys.reduce((acc, key) => { return {...acc, [key]: undefined} }, obj)
```

### 

特别粗暴的方法：

![图片](https://note.youdao.com/yws/res/999/WEBRESOURCEa303facb0b464c91301fa86105486d79)

delete obj.created_at delete obj.deleted_at delete obj.updated_at

### 

Vue.delete 方法

### params 和 query 路由传参

params传参要跟 name 参数不会显示在url上，即跳转页面或刷新页面参数会丢失（放在请求体中）

![图片](https://note.youdao.com/yws/res/1001/WEBRESOURCEf8775d00d56eae86d77143e1b6b933db)

参数接收：

query传参要跟 path （也可以跟 name） 参数直接拼接在url上，刷新页面参数依然存在

参数接收：

url上显示拼接过来的id

### 数组每次push后要清除，不然每次循环都会push一次，每次点击都会循环显示多一次

![图片](https://note.youdao.com/yws/res/1009/WEBRESOURCEc72e7f939143ac336d6fd5b8f98a106d)

### 调接口刷新数据，不用也不能强制刷新浏览器来实现刷新。如果中间有啥卡壳的，那就是方法或者其他地方的问题，（不是刷新数据就可以解决的问题），把关注点放在其他地方。

### 兼容小程序： 用$consts.get('AUDIT_STATUS').REJECT 不用 $consts.AUDIT_STATUS.REJECT

### 点击取消按钮报错 Error in v-on handler (Promise/async): "cancel" found in

解决： 使用 async 和 await 异步请求接口数据，需要进行错误捕获 使用.catch 方法即可

直接return err 代表删除

![图片](https://note.youdao.com/yws/res/1011/WEBRESOURCEa4a8f9bc4d892871679edd528e11f656)

`console.log()试讲错误信息打印输出`

### template中使用插件的方法

ts中：

### vuex刷新页面数据会丢失（可以存sessionStorage或者localStorage中），通过组件通信父传子的话，子组件会拿不到父组件传递过去的数据，所以做法是要在子组件里面监听该props值的变化，而不能只是单纯做判断

### 子组件中单纯的打印this.details会取不到值

![图片](https://note.youdao.com/yws/res/1014/WEBRESOURCEcded8e5f358568eb6338b908b6ec0413)

### 父组件中：

### 子组件中：

### async/await 方法是异步的，无法保证在异步之后/之前 就 能够 按照想法 按顺序执行代码 所以要使用监听或者设置定时器进行延时。

### 页面和组件是不一样的 ！

### html元素宽度不具有继承特性。块级元素(block)的宽度会占据一整行，所以看似继承了，实则不是。

![图片](https://note.youdao.com/yws/res/1016/WEBRESOURCE0ecdfcd50b2d0386670e72befd76c666)

让一个元素的宽度根据内容撑开，只要设置其display不为块级元素，不设置宽度就可以了，比如float,inline,position为absolute,fixed等等等等(很多，不是块元素，不要设置宽度)

### 箭头函数的this指向问题

指的是 往上的第一个普通 function 的上下文 （往上的箭头函数都不算）

优先级： new 构造函数 > call / bind / apply 绑定(this, ...) > obj.foo > foo

### 要么通过赋值 shareformid = ,要么通过 onLoad 参数传递

![图片](https://note.youdao.com/yws/res/4299/WEBRESOURCE3286a2cb935f35666e20b99feb0a786d)

### 小程序获取当前页面路由 不支持vue的 $route.path 方式

### 小程序中用 piui 组件的话 样式都用 custom-style 来写，用 style / :style 的话小程序模拟器和真机会包多一层， 用view标签的话可以用style写样式，用 :style 的话 用 对象的形式来写方便些，或者可以用数组来写

### [1, 2, 3, 6].includes(2) === [1, 2, 3, 6].indexOf(2) > -1

### 计算属性 / data 中的数据 都可以 :属性名 传给子组件， 子组件props接收

组件里定义的 props，都是单向数据流，也就是只能通过父级修改，组件自己不能修改 props的值，只能修改定义在 data 里的数据。

![图片](https://note.youdao.com/yws/res/9079/WEBRESOURCE0a18659c8c2816c911b4dac85c6c64a0)

修改只能通过自定义事件通知父级，由父级来修改。

方法 / 计算属性 / watch 等 中定义的东西， 不分先后顺序，也就是说第一个定义的计算属性里的的变量是在第二个中才定义的，这时候放第二个上面或者下面都一样，不影响。。。 vue 会 处理，， 做好相互之间的依赖的。

### 绑定取值

### 小程序用 :value + @change 代替 v-model

动态校验：

![图片](https://note.youdao.com/yws/res/1023/WEBRESOURCEe232d081f7e381e7e62c9e70f021abf6)

方法一： this.$set 或者

方法二： 直接赋值

### this.$set （Vue.set）方法：

描述 ： 给 对象 （是对象 或 数组）添加一个属性，但是没有更新到视图上，this.$set方法 确保 新添加的属性是响应式的，并且可以触发视图更新

// 响应式更新数组 （[ { name: '', phone: '', sort: '', userId: '' } ]）

![图片](https://note.youdao.com/yws/res/1026/WEBRESOURCEf9ea60bbdf8ee6f64c3761c77566f747)

用法： this.$set(target, key, value)

target：要更改的数据源 （可以是对象或者数组）

key：要更改的数据

value：重新赋的值

（一） input 输入框

![图片](https://note.youdao.com/yws/res/1028/WEBRESOURCE75350954de8dd44cc40fb5d465f09ccd)

### 、

### 、

### 、

有效：

直接赋值 方法：（input输入框无效）， 得用 this.$set（上面写的方法）

![图片](https://note.youdao.com/yws/res/1808/WEBRESOURCE2a7aefd2de516b8b270b66df58d4fb2d)

无效：

（二）、 picker 选择器

this.$set （Vue.set）方法：

选择点中有效：

直接赋值 方法：（picker选择器有效）

![图片](https://note.youdao.com/yws/res/2903/WEBRESOURCE06a4f770eaae508910c9e4f7ed2a1415)

选择点中有效：

### 

使用 flex 布局时，会没有宽度 和 高度，内容自动撑开的宽度 和 高度 为默认宽度

结果就显示：

把框的样式去掉就好了：

![图片](https://note.youdao.com/yws/res/2906/WEBRESOURCE7e2f2b97850e342d501001b3a733ea77)

<pi-card

v-for="cardItem in cardInfo"

:key="cardItem.id"

margin="0 16rpx 16rpx"

:custom-style="{

![图片](https://note.youdao.com/yws/res/2909/WEBRESOURCEabb249e626e212effcd700439598fae0)

height: '180rpx',

borderRadius: '24rpx'

}"

>

<template slot="body" class="pi-mg-top-12">

![图片](https://note.youdao.com/yws/res/1279/WEBRESOURCE6554a50486e2ae30b0163c5149bd915b)

<pi-list :border="false" hover-class="none">

<pi-list-item :title="cardItem.text">

<pi-img

slot="left"

width="100"

![图片](https://note.youdao.com/yws/res/1045/WEBRESOURCE09298da1806cc5b2680f2841c7ec2e4f)

height="100"

:src="

$consts.get('STATIC_IMG_URL') +

(cardItem.id === 'parent' ? 'account_ic_parents.png' : 'face_ic_teacher.png')

"

![图片](https://note.youdao.com/yws/res/1047/WEBRESOURCE9fc35d2553ac6f6eafb235ed4380ce63)

/>

</pi-list-item>

</pi-list>

</template>

</pi-card>

![图片](https://note.youdao.com/yws/res/1049/WEBRESOURCEb27927ff44284b182713a8c5776071c0)

### uniapp的几个路由跳转

### 、普通跳转 uni.navigateTo( ) : 保留当前页面，跳转到应用内的某个页面，使用 uni.navigateBack 可以返回原页面

### 、无记录跳转 uni.reLaunch( ) : 关闭所有页面，打开到应用内的某个页面

### 、跳转 tabBar 页面 （首页） uni.switchTab( ) : 跳转到tabbar 页面，并关闭其他所有非 tabBar 页面

### 、返回上一页面或多级页面 uni.navigateBack( ) : 关闭当前页面，返回上一页面或多级页面。可通过 getCurrentPaes( ) 获取当前的页面栈，决定需要返回几层。

![图片](https://note.youdao.com/yws/res/1937/WEBRESOURCE056a25f4b5d138c444d540b64da58878)

判断每次进入该页面设置的路由跳转都生效，要在 onShow ( ) 页面生命周期使用， 在 onLoad( ) 使用只有在第一次的时候生效 （相当于vue的导航守卫功能）

### 、关闭当前页，跳转指定页面 uni.redirectTo ( ) ：

### reduce 的 几种用法

### 浏览器控制台cdn引入外部js文件

<script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>

![图片](https://note.youdao.com/yws/res/2913/WEBRESOURCE93e3011e0535811cb49bcc5973a68612)

(1)、 DOM 方法：

在console中输入：

`var script = document.createElement('script');`

script.src = "https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js";

`document.getElementsByTagName('head')[0].appendChild(script);`

![图片](https://note.youdao.com/yws/res/1061/WEBRESOURCE6e4bf977c80c24f16722755b994e98ad)

则在控制台中引入sockjs.min.js文件成功

（2）、console控制台插件方法：

groupBy 只是 分组， 排序不准确的

肯定就是你写的代码的位置那里有问题，不用怀疑，就硬摁着那几行代码看和注释调试就好!

### uni-app修改本地存储中key的单个值

![图片](https://note.youdao.com/yws/res/1064/WEBRESOURCEbab944538c414c575480648ca7f13f36)

```javascript
let _userInfo = uni.getStorageSync('userInfo'); _userInfo.nickName = res.data.nickName; uni.setStorageSync('userInfo',_userInfo);
```

### Watch 只会监听一次 ！

### find 找到（返回）满足条件的第一项 循环中的该项是什么数据结构，就返回什么

findIndex 找到（返回）满足条件的第一项的下标 返回一个索引值

some 只要有一个存在（成立），那就返回 true，其余情况都返回fasle

![图片](https://note.youdao.com/yws/res/1075/WEBRESOURCE843148bd14b0a1762e382d1bf98384be)

every 所有条件都满足（每一种都为true）时返回 true，其余情况都范湖false

### .map 映射 将一种数据类型转换为另外一种数据类型 （举例： 将字符串数组转为对象数组）

### .forEach 遍历 无返回值 遍历数组，对元素做操作，改变元素的一些东西 （举例： 遍历对象数组，当满足条件时改变每一项对象元素的value值）

### .filter 过滤出满足条件的元素，最终组合成一个数组返回 （举例： 筛选出数组中满足选中条件的元素的个数）

.filter 最终无论怎么变化 都是不可能会改变原数组的结构的，返回值都是在原数组中进行筛选符合条件的选项， 不可能会变成另外一种数据格式的

![图片](https://note.youdao.com/yws/res/1079/WEBRESOURCE4c2d8bcb87b7754170999d374d5a2c33)

.map 就是改变数据结构 用的

join / Array.from

### .reduce((pre, cur)) 自定义返回的数据类型 （很灵活， pre值可以有 [ [ ], [ ], [ ] ] , [ 0, 0, 0 ] , 0, { } , [ ] ........ ）

pre的默认值为空的话不会加入计算的，默认当没有

坏代码

![图片](https://note.youdao.com/yws/res/1082/WEBRESOURCEe42fc32da5769f9f929371872bbc96da)

优化 1 reduce 大法

优化 2 先塞个排序字段（要什么就自己往里塞什么） ，再排序

优化 3 可以直接在排序里面判断 （这个最简化 牛逼）

### uniapp 页面跳转传参

### 、传单个 / 多个 参数

![图片](https://note.youdao.com/yws/res/1084/WEBRESOURCEa0853542ced3091ae3d840327f03cde5)

### 、传对象 / 数组

使用时 需要二次解密

### 接口数据处理尽量都写在js（<script>标签）中，html（template模板）中一般只做显示，这样便于阅读和维护

### lodash 的 几种排序方法

groupBy 会转数组为对象

![图片](https://note.youdao.com/yws/res/1086/WEBRESOURCE90ff5e5ff395e5ab8ee105ae5d663023)

groupBy 之前 ：

groupBy 之后 ：

orderBy 保留原来的数据结构

其他方法

### vue父子组件渲染过程 和 uniapp 的 页面声明周期比较：

![图片](https://note.youdao.com/yws/res/1109/WEBRESOURCE319df4210d930c1fbaf2ee19a2037744)

加载渲染： 子组件的生命周期在 父组件的 beforeMount 和 mounted 之间

### pi-upload-img 的 img-field

不支持这样写

只能这样写

### 字符串传参要解码

![图片](https://note.youdao.com/yws/res/1112/WEBRESOURCE3d5a9f6cd84590ab544a2452b1a88c35)

### 循环 ？ 理解 ？ findIndex 返回值是该项的索引值

find 返回值是 该项的数据结构

### 不要用这种路由写死的写法去判断，没有普适性（增加路由或者路由有所改动就会出问题）且 ios 不适用

接口没返回标识的，在页面跳转时自己直接加个状态值，去判断 就好了 ！

### 读代码

![图片](https://note.youdao.com/yws/res/1117/WEBRESOURCEac6d74c9566c3cc9aa4ad2089e8d5595)

看代码的时候一定不要光看，要首先结合接口文档，根据返回的数据（格式），对着返回值参照代码写法，理解需求，知道其中的含义，

再去日志打印，再debugger调试，将组件/代码块 拆分开来看，首先把能看懂的先理解好，一定不要慌，都是那些东西，业务需求理解好了，就那样..

### 做功能前先把数据结构设计好，然后再按照需求，接口想要的格式进行开发

### 有时候样式不生效 调起来 比 写 逻辑 还 费劲 giao !!!!

### 遇到问题，先不要慌（无论见没见过，不管什么报错，）， 试着去分析， 将问题拆解开来， 一小步一小步得来，慢不要紧，能做出来就行。 要分析和拆解，然后问题拆开后就很简单了，最关键还是心态要好！ 问题不大，不要慌。

![图片](https://note.youdao.com/yws/res/1120/WEBRESOURCEb6d04750966d7ecb01239bf7c9817c1d)

### 排查问题（注释代码，自己增加的代码后出现的问题，就注释掉嫌疑最大的部分，把所有增加的代码都注释到排查完，就找到问题了，暴力循环）

### 过滤假值 arr.filter(Boolean)

Boolean() 返回一个true/false 的布尔值 ！

### 不要在data中赋值computed的值，因为computed在初始化

的时候就会执行，而data是在mounted之后才执行，所以第一次会拿不到值

![图片](https://note.youdao.com/yws/res/1127/WEBRESOURCE0ad00e9d58d8f5fa117cc6922b2145f9)

直接 watch 监测下就好了

### 写代码要注意兼容性 h5和小程序和移动端的

### 只有小程序才会有shadow-dom(shadow-root类似) 所以要加条件判断兼容不同平台

https://uniapp.dcloud.io/platform （ 条件编译 ）

### 修改列表项（表单项）的最后一个元素的样式的写法：

![图片](https://note.youdao.com/yws/res/1125/WEBRESOURCE5a47f372890c57a9e6044a7409e450c6)

用 标签名 拿到 该列表的最后一项， 然后该项中有个同标签名的类名，选中该项， 再将该项的伪元素选择器选中，然后对其样式进行处理！！！

### 

### 数组扁平化？ 就不用.map再push了

Array.prototype.flat( )

### npm install 报错

![图片](https://note.youdao.com/yws/res/1181/WEBRESOURCE251385aa31b71fad328eb2e2da47d93d)

### 、

### 、

### 、

yarn add / cnpm install / npm install 各种切换 少的包一个一个装

yarn install 装全部 / yarn add 装单独

![图片](https://note.youdao.com/yws/res/1129/WEBRESOURCE92f51fba97750063460f37932619739a)

添加（升级/降级）依赖 yarn add xxx@latest（最新版本） / yarn add vue-router@3.2.0 （降低/指定版本） / yarn (global) upgrade xxx （ (全局)升级 xxx ）

移除（全局）依赖 yarn (global) remove xxx

### 子传父再传子， 要用 watch 不能直接在 mounted 里打印

### get 传参 （ post 换 get ）

没 JSON.stringfy() 之前

![图片](https://note.youdao.com/yws/res/1162/WEBRESOURCEa128b8ccc2e33db84c8586e5f8f50f75)

不符合格式：

JSON.stringfy() 之后：

符合格式：

或者可以这么写 （不用JSON.stringify的话）

同样可以i请求成功 :

![图片](https://note.youdao.com/yws/res/1164/WEBRESOURCEb0a9ccb3638e3df7bf8dc7997cc30f08)

### 改变对象的key - value（往对象添加字段）可以先转数组再使用 reduce

将下面该对象改编成 {

date': '2021-09-12,

menuList: {a: '你好啊'}

}

![图片](https://note.youdao.com/yws/res/1158/WEBRESOURCE99cb85307a716403c4790245e876aee1)

`var o = {'2021-09-12': {a: '你好啊'}}`

(1)、先转数组再拍平

（2）、使用 reduce 进行对象格式转换

### 数据格式转换 ：

### v-for 循环

![图片](https://note.youdao.com/yws/res/1166/WEBRESOURCEcaa52ca947854c90b11ea64d8f7e96ef)

v-for in 数字

<div v-for="i in 5" :key="(i + 9).toString(36) + i"> 表示 i 为不为0的正整数开始 取值（1、2、3、4、5）共循环出 5 个值

### 管理后台vue + ts 子组件绑定 v-model 值

### 对象数组排序

### v-for undefined 会有问题，在请求接口拿到数据做处理的时候记得给默认值 [ ]

![图片](https://note.youdao.com/yws/res/1147/WEBRESOURCE9e42ca252e4438b4524ff97d93106b16)

不然会报错 In order to be iterable, non-array objects must have a [Symbol.iterator]() method报错

### git commit 规范

### vw/vh 与 100% 的区别

（1）、 vw/vh 是 相对于 视口 宽度/高度 （只与可视化窗口的宽高有关、与父级元素无关）

100vh 在 移动端 出现的问题 （移动端地址栏有时可见，有时隐藏。导致视口大小因此而变化）

![图片](https://note.youdao.com/yws/res/1145/WEBRESOURCE03c495b966dd18616c5f26f265c89d6b)

解决： 使用 window.innerHeight 将高度正确设置为窗口的可见部分 。显示内容高度不受地址栏的影响

（2）、100% 是 相对于 最近一级的父元素的宽高

### 高度自适应引起的问题： 页面首次加载的时候会窄（缩一下）？

CDN 有缓存，图片没法及时更新

### el-form里面如果有且只有一个el-form-item里面是el-input的话

![图片](https://note.youdao.com/yws/res/1178/WEBRESOURCE1f81c66b6eabfaaf513bd38ab0adb7e7)

你在input里面输入完毕 按回车默认会自动提交 导致整个页面刷新

解决： 在 el-form 中加 @submit.native.prevent

<el-form :inline="true" :model="form" @submit.native.prevent>

### el-radio 单选可取消 自己写个组件呗。

使用：

![图片](https://note.youdao.com/yws/res/9454/WEBRESOURCE2525d7502832f35b8f882ea7268c111d)

<radio-checkbox>

<el-checkbox-button>

//

</el-checkbox-button>

</radio-checkbox>

![图片](https://note.youdao.com/yws/res/9455/WEBRESOURCEdff2c3a918a9c3f985ae494cbbff264e)

### 、单选

### 、可取消选中

### 更改正式/测试环境域名（包括静态服务器域名和api域名，小程序发版还涉及更换appid），管理后台和小程序

### 、管理后台发布命令：一般都是 npm run build + gulp upload:dev （测试环境）/ npm run build + gulp upload:release （正式环境）

或者直接在package.json中配置了发布命令 ，那就 直接 npm run fabu 就行了

![图片](https://note.youdao.com/yws/res/1194/WEBRESOURCE31edec4509164781100e14f1f9574417)

### 、小程序发布命令

2.1）、发布h5：跟管理后台一样，npm run build + gulp upload:release (upload:static)

2.2）、发布微信小程序（上传微信公众平台）npm run dev:mp-weixin / npm run build:mp-weixin ，分别会生成两个静态文件，如下

一般发版的话会选择用 build:mp-weixin 这个来 run， 然后再在开发工具中点上传

然后就能看到体验版和正式版了：

![图片](https://note.youdao.com/yws/res/1205/WEBRESOURCE8b29e7b77380dec457030b37083104c9)

小程序改了api域名的话，在开发者工具中运行，相对应的appid也要对应的上，要改回去

（1）、sadaiscloud环境： VUE_APP_BASE_API = 'https://api-canteen.sadaiscloud.com' WX_APPID: 'wxb31658291f32d474', // 微信APPID

（2）、ipon-group环境： VUE_APP_BASE_API = 'https://api-canteen.ipon-group.com' WX_APPID: 'wx6d830d0c4b543083', // 微信APPID

使用 HBuilder 打包 公众号 H5 步骤：

### 可选链 ?. (ES2020)代替 && 进行判空处理

![图片](https://note.youdao.com/yws/res/1219/WEBRESOURCEc785bd278fa1e6eda680f98b74c36274)

使用规则：?. 只检查左边部分是否为 null/undefined，如果不是则继续运算

不会检查右边

javascript info 可选链

### 使用 Vue v-model number 修饰符可以实现让输入框输入的内容，自动转换为 number 类型。

### 

![图片](https://note.youdao.com/yws/res/1227/WEBRESOURCEa90cc36e1c32c05586af081e931a5fdb)

### 记一下数组的 splice 的用法： 返回被删除的元素数组

### 、删除

从索引 2 的位置开始删除所有元素

```javascript
var myFish = ['angel', 'clown', 'mandarin', 'sturgeon']; var removed = myFish.splice(2); // 运算后的 myFish: ["angel", "clown"] // 被删除的元素: ["mandarin", "sturgeon"]
```

从索引 3 的位置开始删除 1 个元素

![图片](https://note.youdao.com/yws/res/5969/WEBRESOURCEa4e629992b343776c7f8f85f1d2d32e0)

```javascript
var myFish = ['angel', 'clown', 'drum', 'mandarin', 'sturgeon']; var removed = myFish.splice(3, 1); // 运算后的 myFish: ["angel", "clown", "drum", "sturgeon"] // 被删除的元素: ["mandarin"]
```

### 、替换

从索引 2 的位置开始删除 1 个元素，插入“trumpet”

```javascript
var myFish = ['angel', 'clown', 'drum', 'sturgeon']; var removed = myFish.splice(2, 1, "trumpet"); // 运算后的 myFish: ["angel", "clown", "trumpet", "sturgeon"] // 被删除的元素: ["drum"]
```

### 、插入

![图片](https://note.youdao.com/yws/res/5971/WEBRESOURCEfcdc12b1167154d14a4b6f23a82411f0)

从索引 2 的位置开始删除 0 个元素，插入“drum” 和 "guitar"

```javascript
var myFish = ['angel', 'clown', 'mandarin', 'sturgeon']; var removed = myFish.splice(2, 0, 'drum', 'guitar'); // 运算后的 myFish: ["angel", "clown", "drum", "guitar", "mandarin", "sturgeon"] // 被删除的元素: [], 没有元素被删除
```

### （1）判断对象是否为空：

`Object.keys / JSON.stringify() === '{}' /`

（2）判断对象是否相等： 最好使用工具库，比较可靠和边界情况考虑比较完全

![图片](https://note.youdao.com/yws/res/5974/WEBRESOURCE20a0479834c55ec914d5ae78377b5a7e)

简单比较 ： 引用数据类型指向不同的内存地址，不可直接 == 或者 ===

浅拷贝可以试一下

`const a = { name: 'Hbin' } const b = { name: 'Hbin' }`

`（1），a == b / a === b ===> 结果都为 false`

`（2），const c = a c === a true c === b false`

![图片](https://note.youdao.com/yws/res/1256/WEBRESOURCE64bb5af5433e23baf4fca4be51ab659b)

（3），循环遍历简单实现下（对属性值为复杂类型，属性值为null 或者 一个属性是undefined一个没有该属性）

浅比较

只做第一层数据的查询，跳过数组、对象、方法

利用es6的every函数做最优处理

```javascript
// 2. shallow compare function isObjShallowEqual(obj1, obj2) { const keys1 = Object.getOwnPropertyNames(obj1); const keys2 = Object.getOwnPropertyNames(obj2); if (keys1.length !== keys2.length) { return false; } const flag = keys1.every(key => { const type = typeof obj1[key]; // do not check function, array, object if (['function', 'array', 'object'].includes(type)) { return type === typeof obj2[key]; } // if unequal, return true if (obj1[key] !== obj2[key]) { return false; } return true; }); // if found unequal, then return false, which means unequal return flag; }
```

![图片](https://note.youdao.com/yws/res/1231/WEBRESOURCEde8b54365fed5dabad906df7e562545b)

### （1）判断数组是否为空：

### array.length === 0 / 2、 var found = array.find(i => i), if (!found) console.log('数组为空!') / 3、 var foundIndex = array.findIndex((i, idx) => idx > -1) , if (foundIndex === -1) console.log('数组为空!')

（2）判断数组是否相等： 最好使用工具库，比较可靠和边界情况考虑比较完全

简单比较 ：

（3）拷贝数组：

![图片](https://note.youdao.com/yws/res/1225/WEBRESOURCE58462e72bc0f846d54cd26cd03b0c29b)

### var a = []

### Array.prototype.sort() 用法

### 、arr.sort() : 数字 => 从小到大排序 字符串数字 => 按照unicode顺序排（字符大到小排序）

### 、 Function:

arr.sort((a,b) => a>b ? -1 : 1) 从小到大 -1: a在b前 （从小到大排） 1: b在a前 （从大到小排）

![图片](https://note.youdao.com/yws/res/1223/WEBRESOURCE86f29c1fceb3257acc0e53a76eea5fbd)

arr.sort((a,b) => a>b ? 1 : -1) 从大到小

或者：

arr.sort((a,b) => (a.auditStatus - b.auditStatus) ) 正序 从小到大 排

arr.sort((a,b) => (b.auditStatus - a.auditStatus) ) 倒序 从大到小 排

### 、eg: var a = [

![图片](https://note.youdao.com/yws/res/1229/WEBRESOURCEf43edf19cc621b8159cee4772531acdb)

{date: '2021-10-15', visitor: 'zs'},

{date: '2021-08-25', visitor: 'ls'},

{date: '2021-12-24', score: 'ww'}

]

按照时间降序排（利用dayjs工具库）： a.sort((a,b) => ( this.$dayjs(a.date).isBefore( this.$dayjs( b.date)) ? -1 : 1 ) )

![图片](https://note.youdao.com/yws/res/1269/WEBRESOURCE53a47cc18c41baf06d3a6bf8c124f6d0)

dayjs 获取时间戳 （+dayjs() / dayjs().valueOf()）

### Array数组去重 12种方法 ：

Array.from(new Set(arr))

[...new Set(arr)]

### vue 中 操作 DOM . 须在页面数据加载完成后对DOM进行操作

![图片](https://note.youdao.com/yws/res/1261/WEBRESOURCEb2825c5b7646aaba271c04e23cd025f6)

！！！！！！！！！！！！ 须在 this.$nextTick() 回调函数中执行

如何在页面渲染后操作dom, 而且只执行一次 ？

在接口请求成功的回调中使用！

可以在mounted中$nextTick, 也可以在计算函数中$nextTick.

### 

![图片](https://note.youdao.com/yws/res/1259/WEBRESOURCEcafb18518e30a249c5eed05bb462d317)

饭堂小程序开发总结：

周期： 7.30-8.25 3周时间

完成功能：学生绑定/管理员模块/订餐模块/我的模块

### 如果是 html 中 不会用到的属性， 可以不放到 data 或者 computed 中， 直接在 created 中 this.xxx 就可以了， 性能考虑？ （字符串 --> 对象 ）

data 监听 每个 {{ }} 对应一个watcher 监听器

![图片](https://note.youdao.com/yws/res/1266/WEBRESOURCE6abca1efca6d3c4621528186dcdd0e3a)

一个组件 =》 一个 watcher

### git submodule

### 移动端（小程序/ APP）滚动容器选择

（1）、纵向滚动 scroll-view (pi-scroll scroll-y)

（2）、 swiper + 纵向滚动 swiper (pi-scroll)

![图片](https://note.youdao.com/yws/res/1277/WEBRESOURCE9dce3f00ef4393243f08966e91e6dae7)

（3）、 mescroll-uni + 纵向滚动 (pi-scroll-container + pi-scroll + mescroll-uni 上拉刷新/下拉加载)

### js 清除浏览器缓存方法

### ，用随机数 URL 参数后加上 "?ran=" + Math.random(); ?ran=Math.random()

### ，用随机时间 在 URL 参数后加上 "?timestamp=" + new Date().getTime();

### dom 获取 labei for属性名对应的标签

![图片](https://note.youdao.com/yws/res/1271/WEBRESOURCE5c1223ecb80d5c0ae9f630176cfdb86b)

### git 变基

### 写代码不仅要考虑可读性， 还要考虑兼顾性能优化方面的问题 !!!! 减少重复代码的使用和出现

性能优化 ：

### 懒加载： 在需要的时候加载，随载随用（路由、图片、滑动触发、虚拟列表）

`异步组件、组件 import () => {} 动态引入、webpack splitChunk`

![图片](https://note.youdao.com/yws/res/1274/WEBRESOURCE042d4e36c9246672e4f251fc339f9cd5)

### 按需加载： 根据需要去加载资源（常用 UI 组件库）

### 不生成.map文件：配置里productionSourceMap设置成为false，能差不多减少一半的体积。

### 通过cdn方式引入：

### 图片压缩：

利用一些网站对大体积图片进行压缩，例如：tinypng

![图片](https://note.youdao.com/yws/res/1271/WEBRESOURCE5c1223ecb80d5c0ae9f630176cfdb86b)

### pi-checkbox 的 :value + @input @change 都没有 .stop 阻止冒泡事件 ，得 包多 一层 view 标签 ，用 tap 事件来 代替

### 导出不调接口 分页数据 / 按照查询条件筛选的数据 无法查询 只能拿当前页面数据

### 父组件传个boolean控制 子组件dialog弹窗 是否显示 用 v-model !!!!!!

父组件：

子组件:

![图片](https://note.youdao.com/yws/res/1291/WEBRESOURCE89a24905728c4644468ebccfc7a36058)

常规的js - vue 是 定义在 model 上 的

model: {

prop: value,

event: change

}

![图片](https://note.youdao.com/yws/res/1293/WEBRESOURCEa9f88425994b821f6578682224c0b44e)

引用 60、

**注意 ：**

小程序中没有 v-model ，所以只能在管理后台使用，小程序 还是使用 :visible="visible" 和 this.$emit('val', val) 来代替了 ！

### 踩坑： el-input 同时设置 type 和 maxlength ， maxlength 不生效 ， 需自己改成 oninput

### el-form-item 不可以离开 el-form 单独使用 ！

![图片](https://note.youdao.com/yws/res/1297/WEBRESOURCE2ea6b03995daf8c0851e606a254ffe93)

### 

### reduce 用法 ： 慢慢积累 可以返回你想要的东西 （自己来定义要返回什么东西）

删除数组对象中的指定对象 reduce（该对象元素的prop字段是 'tag_bodyAge'的时候不添加到该数组中， 也就是删除该元素的意思了）

### 数组的 重组 / 去重 / 排序 / 分组

### @Watch($route) 只能在外面（全局使用），在单独的页面内监听不生效 （页面内当前路由不会变）

![图片](https://note.youdao.com/yws/res/1329/WEBRESOURCE9ac579f7b61dbcb469297d05e0369db6)

### 拉起app 地图 deeplink 文档

### 管理后台 创建 和 保存 区别 ： 创建没传id， 保存需要传id （没 id 会新增一条 ------ 相当于创建了）

### 顺序执行异步代码

### 每个公司都有自己的规范，熟悉完公共模块有哪些，项目蓝湖设计图看下来大概确定哪些组件是大概率会封装的，去项目里面components 里面找，现成的组件拿来就用，避免重复造轮子，和自己写的不全；

**涉及到vuex的数据管理的，要清楚流程，确定好修改不会影响到其他的地方时才进行更改（一般新增不会大影响，都是修改和删除就要很注意）;**

![图片](https://note.youdao.com/yws/res/1335/WEBRESOURCE5e15bbf0db1318837f6f7aa4a25bf321)

可以尝试自己从0搭建后台系统的框架（初始化项目），但是要按照规范来，避免出现本来就是模板化的东西的bug

### 后台管理系统 upload 组件 包括很多的类型，现在是 uploadImage 和 uploadFile 图片归一种，其他的文件格式都归 uploadFile,传对应的参数进行不同格式的类型的显示

### @1、空值合并运算符 ?? 只有 左边为 undefined 或 null 时，返回右边 0 ?? 24 =====> 0 null ?? 'youngG' ======> 'youngG'

```javascript
@2、逻辑或 | | 是左边为假（ false / null / undefined / 0 / ' ' / NaN ）时，返回右边 0 || 24 =====> 24 null ?? 'youngG' ======> 'youngG'
```

```javascript
@3、逻辑空赋值 ??= 当 左边为 undefined 或 null 时， 为右边赋值 const a = { duration: 300 }, a.duration ??= 50 =====> 300
```

![图片](https://note.youdao.com/yws/res/1342/WEBRESOURCE60002aa8cdaf54f5be740660c517e648)

`a.speed ??= 600 =====> 600`

### 

### vue 中的 .sync 语法

vue 中的 .sync 语法 相当于不用在子组件中 this.$emit 就可以直接修改 子组件的值了， 相当于一个自动更新父组件属性的监听器

直接在子组件中直接修改 @PropSync 的值， 会自动更新到父组件中

![图片](https://note.youdao.com/yws/res/1344/WEBRESOURCE6b4171b0800734744baaa563bab579ae)

父组件：

子组件：

‘show’ 是在父组件定义的传过来的要这么写， syncedShow 是子组件接收到'show'这个值后重新命名的（相当于syncedShow = show 赋值操作）

直接在子组件中直接修改 @PropSync 的值， 会自动更新到父组件中

相当于：

![图片](https://note.youdao.com/yws/res/1361/WEBRESOURCE3ae4d544f18215ae452c6eed58a4e2f3)

父组件：

子组件：

节省绕来绕去的代码 。。。

@Prop传过来的值 不可直接修改

@PropSync 可以

![图片](https://note.youdao.com/yws/res/1353/WEBRESOURCEa13c6fae9884d2ecebf02460fbeeb120)

@Model 不可以直接修改

@ModelSync 同理 可以

‘selectedNodes’ 是在父组件定义的传过来的要这么写， syncedSelectedNodes 是子组件接收到'show'这个值后重新命名的（相当于syncedSelectedNodes = show 赋值操作）, 'change' 是 @Model装饰器 (绑定父组件的 v-model 值) 在子组件的写法，相当于值发生改变同步接收的意思

## 一、@Model装饰器

父组件：

![图片](https://note.youdao.com/yws/res/1355/WEBRESOURCEfe98fd6cf556e31be052f7275069d9d0)

子组件：

### @Model 装饰器

### 监听值的变化：

### 最后还需要 $emit

## 二、@ModelSync 装饰器 （父组件 v-model + 子组件 ModelSync）

![图片](https://note.youdao.com/yws/res/1357/WEBRESOURCEe827b232f484a30c4952f3e4f3dd5f7e)

父组件：

子组件：

直接modelSync， 少去了 $emit 这一个步骤 （直接这样就行了）

### 随机获取10位无序字符串 （可做 id 值 使用） guid

Math.random()

![图片](https://note.youdao.com/yws/res/1369/WEBRESOURCE40e09df146908f32a94686b2e1438d00)

.toString(36)

.substring(2, 12)

Math.random() 返回 0-1（大于0 小于1） 的随机小数，一般是小数点后16位（偶尔17）

Number.prototype.toString() （ eg: (12).toString() ）参数 radix 代表进制数，没写默认是0，参数 2-36 可选， 36进制 则是 数字 0-9（10个） 和 字母 a-z（26个）组成 同理 32进制 则是 0-9 + a-v 共 32个组成

String.prototype.substring() （ eg: Math.random().toString(36).substring(2, 12) ）下标为2（从0开始算）起，取到下标为11 ，共 10个数

![图片](https://note.youdao.com/yws/res/1367/WEBRESOURCE2c5b3da7a302362d318461757dbd515b)

### setTimeout 定时器 clearTimeout()

### 数据模型转换

最终想要的格式 ： [ { id1: '', text: '' }, { id2: '', text: '' }, { id3: '', text: '' } ]

```javascript
已知：var knowList = [ { package: {}, partitionDto: { name: 'aaa', id: '111a' } }, { package: {} }, { package: {}, partitionDto: {} }, { package: {}, partitionDto: { name: 'bbb', id: '222b' } }, { package: {}, partitionDto: { name: 'ccc', id: '333c' } } ]
```

求解如何得到想要的结果？

![图片](https://note.youdao.com/yws/res/1581/WEBRESOURCE0a1ec9fafa32e9acab080ffe9d2a0d31)

过程： 先 过滤掉没有 partitionDto 的字段，然后自己组想要的格式，拿id作为唯一值

```javascript
var ids = [ 'id1', 'id2', 'id3' ] =====> [ { 'id1': { name: 111 } }, { 'id2': { name: 222 } }, { 'id3': { name: 333 } } ] （拿个变量接收 var a = [ { 'id1': { name: 111 } }, { 'id2': { name: 222 } }, { 'id3': { name: 333 } } ] ）
```

`先构造个临时对象变量 var tempObj = { }`

`knowList.forEach( i => {`

tempObj[i.partitionDto.id] = i.partitionDto

![图片](https://note.youdao.com/yws/res/1386/WEBRESOURCEea2c9481f8c6d39f59654d4ef7fe7f2c)

} )

`Object.keys(ids).map(i => {`

`return {`

id: i,

text: tempObj[i].name

![图片](https://note.youdao.com/yws/res/2201/WEBRESOURCE811ce72bda11e09a80831250b165df72)

}

})

`自己 转换下数据格式 (构造出想要的格式) 就好了 =====> 要什么，就定义什么。`

### el-popover el-tooltip el-popconfirm 防止父元素冒泡： 直接在外层嵌套个 div， 然后 直接加 @click.stop

### 参数作用域： 当函数的参数有默认值时，会形成一个新的作用域，这个作用域用于保存参数的值。

![图片](https://note.youdao.com/yws/res/2199/WEBRESOURCEea6d496c462c2fd8ce5f23de6b09a5f9)

参数就是为函数服务的，首先会找到参数的值，没有参数再回去找函数中有没有声明

### 

### 

### 懒加载 （动态加载） ： 点选的时候才调用接口加载数据，而不是 用 循环调用接口（递归 / 套娃）的方式 去 一次性调多次接口把数据一次性显示出来 ！！ （不然的话都可以直接让后端一次性把所有的数据都返回给你好了）

每点一次的时候才会 调用一次接口 获取（该层级level）的信息， 不会 一次性给你返回 ！！！！ （点击的时候才发起的请求 ！）

![图片](https://note.youdao.com/yws/res/2207/WEBRESOURCE43e9dcd7427e1290f2490077034006dc)

如 Tree 树形组件 Cascader 级联选择组件 ... 都有动态加载的模式 （传 node 和 data， 不用 按照默认模式的数据格式去展示 参数有 lazyLoad (node, resolve) ）...

目前我找到的对递归最恰当的比喻，就是查词典。

我们使用的词典，本身就是递归，为了解释一个词，需要使用更多的词。

当你查一个词，发现这个词的解释中某个词仍然不懂，于是你开始查这第二个词，可惜，第二个词里仍然有不懂的词，于是查第三个词，这样查下去，直到有一个词的解释是你完全能看懂的，那么递归走到了尽头，

然后你开始后退，逐个明白之前查过的每一个词，最终，你明白了最开始那个词的意思。

![图片](https://note.youdao.com/yws/res/1394/WEBRESOURCE321f6663d2f2f6619936f39939768595)

### 关于 slice splice substr substring split replace concat

spilce:

slice:

(substr （即将废弃的属性） / substring )

`slice 下标为0开始， 到下标为几， 最后一个不取 var str = "abcdefghij"; str.slice(1,4) // 从下标1开始，取3个 bcd`

![图片](https://note.youdao.com/yws/res/1398/WEBRESOURCE56da0f1a62ece6215c23bb8391247aea)

```javascript
substr （即将废弃的属性） begin 起始位置 也是从 0 开始 算起 第一个位置为0，取几个 var str = "abcdefghij"; str.substr(1,4) // 从位置1开始，取4个 bcde
```

```javascript
substring strat开始下标 也是从 0 开始 算起 第一个下标为0， 到下标为几的前一个 var str = "abcdefghij"; str.substring(1,4) // 从下标1开始，到下标3（下标4的前一个） bcd
```

### 父组件请求接口返回数据后，赋值给data中的属性然后传给子组件，子组件 刷新页面，在 created 生命周期中打印就会获取不到，而在template模板中却可以获取的到！

与 父子组件生命周期的触发顺序 有关 又是要 watch ？

### 既有 key ， 又有 value ， 只要有出现map/对象/{} 的低昂，都要有敏感的反应用 map去映射 ！ data: [ { name: '', label: { key: ' Back', val: '后退 ' } } ]

![图片](https://note.youdao.com/yws/res/1400/WEBRESOURCEbed7222757c593724afdcf882c85fd81)

{ key: ' Back', val: '后退 ' } 页面只要 val, 而接口只要 key， 这部很明显 直接定义一个map 完事了吗

KEY_VAL_MAP() {

`return {`

Back: '后退'

}

![图片](https://note.youdao.com/yws/res/1402/WEBRESOURCEd4d4673c027b585e837ba6f91f7d661e)

}

**页面取的时候直接 KEY_VAL_MAP [ xxx ] ， xxx 就是接口对应的变量了 !!!**

### 状态值 boolean - true - false

当 事件/内容 有冲突/需要按条件显示 时，当找不到已知的存在的判断对象的时候，记得想起来 自己定义一个 状态值变量 进行判断 ！！！！

### 灵活

![图片](https://note.youdao.com/yws/res/1626/WEBRESOURCE94300a4dbefab199cc9ed10ed23dc58b)

### Array.prototype.fill()

### Vue.$set （ this.$set ） 的 使用场景 ？？？

（1）、 在 data 中 没有定义的初始化的字段，然后后面在 template 中直接用到 xxx.该字段 / 页面初始化 对该字段操作 / 页面保存对该字段操作 的时候，

响应式系统监测不到，这时候就要用 this.$set(obj(object), targetProperty(string), value) 将该字段加入响应式系统中。

（2）、 在 data 中 定义了该字段， 但是初始化的时候将包含该字段的对象在请求接口后做了重新赋值，但是接口又没有该字段， 等于重新操作后原先包含该字段的对象现在又没有了该字段，

![图片](https://note.youdao.com/yws/res/1629/WEBRESOURCEc52df363dfe684a8f496b34adf0658a3)

所以这个时候又要重新赋值给该字段对应的值， 所以这时候就要用 this.$set(obj(object), targetProperty(string), value) 将该字段加入响应式系统中。

### toLocaleString() 返回特定语言环境下的字符串格式

e.g. 保留千分符位数：

e.g. new Date()使用 toLocaleString()

直接 toString()

![图片](https://note.youdao.com/yws/res/1631/WEBRESOURCE14317bbf07139f47f6d156f970e2ce42)

### OSS 对象存储服务 Object Storage Service 海量/可靠/安全/低成本 的 云存储服务 。 适合存放任意类型的文件。

### 深拷贝 JSON.stringfy() 的 缺点

（1）、JSON.stringify 的 三个参数 （数据，过滤，缩进）

（object, Array | function, number | string）

第二个参数，过滤用 ：

![图片](https://note.youdao.com/yws/res/1644/WEBRESOURCE68c514b84f21e6cf22f9e538e2be6430)

`是 function 时， 接收两个参数 function (key, value) { }`

第三个参数，用于缩进（默认是4）。字符串会以该字符向前填充，数值则按照tab键个数填充

（2）、JSON.parse（text, [reviver]）

第二个参数可以是函数，修改原数据。

JSON.parse + JSON.stringify 实现深拷贝

![图片](https://note.youdao.com/yws/res/1656/WEBRESOURCE47e1794a2d09fd41cfee4a72765b037c)

`const origin = { name: 'MDN' }`

const deepCopy = JSON.parse(JSON.stringify(origin))

deepCopy // { name: 'MDN' }

最好就是自己实现一个深拷贝（开销最小最安全） 深克隆 数组 / 对象 是 一样的 （相同的功能）

### 用 computed 而不是 watch ？

![图片](https://note.youdao.com/yws/res/1416/WEBRESOURCE463c9b1e7351093660c2b00d59519008)

父组件传给子组件的props，子组件中显示该 props 变化前后的值 （变化前即是 默认传过来的 default 值， 变化后即是 父组件中改变了该值），

如果直接 watch （newVal, OldVal） 该 props 然后 子组件中直接赋值 data 中的 xxx 为 OldVal ，则无法达到预期效果 （因为watch无缓存，

当父组件传的值变化时， 子组件中直接watch到的该props中的newVal 是 === OldVal的），所以要在 computed 中 先缓存默认传过来的props，

然后 watch 的是 对应的 computed 值，这样的话 OldVal、newVal 就都拿得到了。

子组件 ：

![图片](https://note.youdao.com/yws/res/2387/WEBRESOURCE5102cad92257ed7cfc8844f65f9cb6b1)

watch 的 deep 属性 在 对象 嵌套 的层级 很深 的情况下 就需要 开启 （深度监听）了

### 过滤器不能直接用 ||

这样不行的话 ：

那就换种写法：

### HTTP 常用状态码 ： 200 300 400 500

![图片](https://note.youdao.com/yws/res/2420/WEBRESOURCEb869bb0158a25dc9153ddec9d496b3d8)

### 标准盒模型 和 怪异盒模型 （IE盒模型）border-box

怪异的 content 包括 border + padding

标准的话 content 就是 content （不会包括其他的）

box-sizing: border-box; 怪异盒模型

box-sizing: content-box; 标准盒模型

![图片](https://note.youdao.com/yws/res/2416/WEBRESOURCE75c4863f84bbce64e5a5fb4d36010d42)

box-sizing: inherit; 继承父元素的 box-sizing

### 越简单，改动越小，越是正确改bug的方式

越迷惑（感到难以解决）的问题，解决方式往往是 越简单 的！

是不需要去想那么复杂的。

### el-dialog 中 使用 iframe （内联标签）会因没有 设置固定的宽高而 整个iframe 元素变大 要有固定（写死）的宽高

![图片](https://note.youdao.com/yws/res/2414/WEBRESOURCEa44f60dbc233266dbf3aa5f0af522ff4)

正常使用 iframe 标签 ，直接 整个 src 和 frameborder="0" 就行了，然后就要么 iframe width 和 height 都 100% 继承 来自父元素（固定宽高）的宽和高。

跨域报错：

X-Frame-Options （HTTP 响应头）用来告知浏览器该网页（iframe的src）是否可以放在 iframe 中

常见属性：

deny --- 不允许

![图片](https://note.youdao.com/yws/res/1422/WEBRESOURCE2adb2c5ad196cd32f6b354ab9994c076)

sameorigin --- 可以在相同域名页面的 frame 中展示

allow-from xxx ( 例如 https://www.baidu.com ) --- iframe 只能放在 www.baidu.com 这个域名下

allowall --- 允许所有站点内嵌

常见的就是在 nginx 配置

没有给 el-dialog 设置宽高前 ：

![图片](https://note.youdao.com/yws/res/1424/WEBRESOURCEf0dd067ec5a0bbfa1d0655e5654d39a4)

更改前：

根本原因是

把 el-dialog 单独放到父元素上面， 不要 抽成一个组件（页面），让其父元素的宽高固定， 就不会有那样的问题了 !

更改后：

### 用户体验友好

![图片](https://note.youdao.com/yws/res/1426/WEBRESOURCE996687d615deec08acff6591ffda2d38)

### CSS 设置文字溢出省略号显示的固定搭配 ：

条件： 需要包含文字的盒子的宽高是已知（固定）的

overflow: overlay 和 overflow: scroll 区别：

overflow: hidden;

text-overflow: ellipsis; // 文字溢出省略号

![图片](https://note.youdao.com/yws/res/1444/WEBRESOURCEa7109ffb64d9912bae8baeeae5a32aef)

display: -webkit-box; // 必要搭配

-webkit-line-clamp: 1; // 显示的文本行数

-webkit-box-orient: vertical; // 必要搭配 盒对象子元素排列方式

### 连续调两次接口，第一次达不到数据怎么解决？

用 async / await 异步处理

![图片](https://note.youdao.com/yws/res/1438/WEBRESOURCE3ed57e729ceac9eea8929f3db4653450)

### CONCAT concat 数组连接数组的方法 别给我拼错了 ！！！

### 数据格式转换 2

### 、已知数据格式：

const gradeList = [

{ grade1: '一年级' },

![图片](https://note.youdao.com/yws/res/1440/WEBRESOURCE4acf2e8bb56be506cd6a66e18b19c007)

{ grade2: '二年级' },

{ grade3: '三年级' }

]

const studentList = [

{ grade1: ['小明', '小张', '小赵'] },

![图片](https://note.youdao.com/yws/res/1448/WEBRESOURCE47812f3301b92ccb7178748b1183ed8a)

{ grade2: ['张三', '李四', '王五'] },

{ grade3: ['tom', 'lilei', 'mary'] }

]

### 、目标数据格式：

students = [

![图片](https://note.youdao.com/yws/res/1453/WEBRESOURCE864205e199948f6e5582234d20a525d6)

{ name: '小明', grade: '一年级' },

{ name: '小张', grade: '一年级' },

...

]

### 、格式处理 ：

![图片](https://note.youdao.com/yws/res/1519/WEBRESOURCE055a9d7b9a7aa0413d6b8b25498bd4ec)

封装个函数： 用到三层循环

`const students = (gradeInfos, studentInfos) => {`

let targets = []

`for (let i = 0; i < studentInfos.length; i++) {`

`for (const j in gradeInfos[i]) { // 这里是重点 （找得到这点规律，基本就破解了）`

![图片](https://note.youdao.com/yws/res/1528/WEBRESOURCE8f0fb7970eefc377927281fb22e77ea1)

`const mapList = studentInfos[i][j].forEach(item => {`

`targets.push({`

name: item,

grade: gradeInfos[i][j]

})

![图片](https://note.youdao.com/yws/res/1537/WEBRESOURCE78c8cfb4bc0e92a6b2e12611570457a4)

})

}

}

return targets

}

![图片](https://note.youdao.com/yws/res/1539/WEBRESOURCE1ce15180b755316cee1c46ea40fc4cad)

students(gradeList, studentList)

查找字符串中每个字符出现的次数 ：

### el-date-picker 只支持 指定格式的字符串， 不在指定范围的话 是显示不出来 （或者显示错误的）

### 无后缀名 图片 查 后缀格式 ：

### 更新视图方法

![图片](https://note.youdao.com/yws/res/1522/WEBRESOURCE8e50850006410bb4a67bb5efb19a8563)

this.$set()

this.$update()

this.$nextTick()

setTimeout()

### 两个数组中删除相同项目：

![图片](https://note.youdao.com/yws/res/1524/WEBRESOURCE089e57a7aa0f16d5bc3106fcd59431c7)

### 饿了么 的 rules 属性 有 required 、 message 、 trigger 、 type ... 等

### 常见布局： 百分比（流式）布局 rem flex 响应式 双栏/三栏布局 双飞翼/圣杯 布局

百分比布局 + flex 可以很常见的适配 登录页 的布局 （px都用%进行替换 ！）

### 以下 ：： 基础 ：

### 真相大白了！！！！！！！ 原因： 初始化的时候赋值有问题导致的报错

![图片](https://note.youdao.com/yws/res/1531/WEBRESOURCEacc34ed1bf7374789c981ffdc48347b1)

### 套娃函数 自身调用不会执行 ！！

### 版本回退，但是不能提交 (git push)

### keep-alive 组件

<keep-alive> （该组件主要使用 LRU 算法的缓存机制） 包裹动态组件时，会缓存不活动的组件实例，而不是销毁它们，防止重复渲染DOM。和 <transition> 相似，<keep-alive> 是一个抽象组件：它自身不会渲染一个 DOM 元素，也不会出现在组件的父组件链中。

当组件在 <keep-alive> 内被切换，它的 activated 和 deactivated 这两个生命周期钩子函数将会被对应执行。

![图片](https://note.youdao.com/yws/res/1533/WEBRESOURCE28647ab5cc6b65e92aa8d96a45e0b0c6)

在 2.2.0 及其更高版本中，activated 和 deactivated 将会在 <keep-alive> 树内的所有嵌套组件中触发。

主要用于保留组件状态或避免重新渲染。

当组件第一次创建的时候

activated 方法是在

mounted 方法之后执行。

![图片](https://note.youdao.com/yws/res/1568/WEBRESOURCEf7ae34ef858ea19069796041aa4d2b91)

当页面被隐藏的时候会触发当前页面的

deactivated 方法

当前vnode 节点被销毁的时候，会判断当前节点是不是有

keepAlive 标记，有的话就不会直接调用组件的

destroyed 了，而是直接调用组件的

![图片](https://note.youdao.com/yws/res/1803/WEBRESOURCE397878de7d85d854c363d9b9afa6c8bc)

deactivated 方法。

LRU （ least recently used ） 最近最久未使用 --- 最近最少使用

是 常用的 页面置换算法 中的一种

keep-alive 包裹下的 router-view 中的 key 属性 （取值： $route.path ---- 只有 path 路径 / $route.fullPath ---- 带query内容）

（一般都是 transition ---> keep-alive ---> router-view 这三个组件连着 使用的）

![图片](https://note.youdao.com/yws/res/1585/WEBRESOURCE0ec16e92149ec441484dfa710f34b59d)

router-view 设置了 key 的缺点:

加了路由的key值，Vue就会认为这不是同一个组件，update的时候会删除这个组件再重新加载一个新的组件，有严重的性能问题。

##### Vue 的 key 属性： key 的 type 是 String | Number

### 完整的后台模板，自己按照命令整一套

### 、引入piui不香吗，按规范来

![图片](https://note.youdao.com/yws/res/1588/WEBRESOURCE4408fa9805c70d61ed97fff2c839e279)

vue create -p sadais-org/uni-preset-vue my-project

### 、管理后台模板 工程创建指令

vue create -p sadais-org/sadais-admin-preset-vue my-project

### 管理后台权限管理（系统设置）：

一般都是有 角色、用户（账号）、资源 这三部分组成

![图片](https://note.youdao.com/yws/res/1594/WEBRESOURCEc9192c32fc3548b49ddd3fdefb8b9524)

然后是 先给角色分配资源（权限），然后再给用户分配角色，资源控制路由菜单的显示内容及顺序

### beforeRouter 导航守卫中的 next() 回调函数属性

next() 直接放行，跳到下一个路由

**next('/login') 或 next({ path: '/login' }) 强制跳转到 login路由的页面**

### el-table-column 的 sortable 属性 （根据prop设置的字段进行排序的）

![图片](https://note.youdao.com/yws/res/1600/WEBRESOURCE4c9d71f2e466c4f5542f6808c4217b45)

### git 代码回滚/撤销

revert ： 恢复/还原 refolg

git reflog 查看 commit 操作历史 ： reflog 查看所有分支 （包括被删除的commit 和 reset的操作）

fast-forward（快进）合并

git log: log 查看已有的提交记录 （被删除的查不出） 更详细

![图片](https://note.youdao.com/yws/res/1598/WEBRESOURCE12df5a40ae2670cf7fcc432c6040c112)

手动修改提交记录的信息和时间， git log 认你修改的那个记录， 但是远程仓库里应该还是你之前的那个提交。

### 图片地址 src地址 的 require 可 动态 .

用 原生 img 标签 :

### 浅拷贝 ？？？ 操作 params 对象 不会影响 this.form 对象 ！ （改变的是对象里面的对象里面的值，就有影响了）

### 、改变了第二层（及以上）就会有影响

![图片](https://note.youdao.com/yws/res/1688/WEBRESOURCEdbd9e86c133113f90aef5830cececa54)

### 、

### 、

### 关于 el-cascader 懒加载（动态加载） 编辑页 回显时有时候 会 回显不出来数据 的处理方式 （即时更新的问题） （el-tree el-cascader el-table el-select ）

方法还是之前动态加载的方法，

补充： 看帖子说这种方式也能实现，但是点击下拉框出现再消失的时候 就会内容跟着消失了 . 所以 还是 采取了 一开始的 方法 直接placeholder 显示 完事，简单好用，不花里胡哨 .

![图片](https://note.youdao.com/yws/res/1715/WEBRESOURCE1e507d301cec70a399f95bd176daaa02)

input placeholder 的样式 一般来说，直接 类名/标签名 + ::placeholder 就可以了 ， 上面的写法只是兼容不同浏览器的内核， -webkit-input- 标识 webkit 内核的浏览器（常见 谷歌浏览器），其他前缀标识同理

比较奇怪 可能需要 单独 给 input 加类名 ，才能作用到placeholder input.arco-select-view-input.region::placeholder

哎，是要直接这样写才行， deep 的位置不对啊 ！！！！！ 这样子就可以了 深度选择器的位置写的有问题

### 只定义没有给默认值 ， 新增的时候就没有该字段 （有赋值的时候才会有）

### es6 中 export 和 export default 的区别

![图片](https://note.youdao.com/yws/res/1602/WEBRESOURCE47287cf04f343c0db6ecfba82609cf9b)

1. export与export default均可用于导出常量 const、函数 function、文件 file、模块 module 等

2. 一个文件中可以 export 多个， 只能 export defalut 一个

3. export 在 import 导入的时候需要加 大括号 { } ，export default 不用

4. export default xxx (变量名) ，在 import 的时候可以给 xxx 重新进行任意命名

### el-upload 自定义 上传文件的请求方法 将默认的上传行为 action 属性的 上传地址 置为空 再自定义 http-request 方法

![图片](https://note.youdao.com/yws/res/1660/WEBRESOURCEb9a98ea239766572f69dd66e2918041f)

### 文件上传改用 sdk 形式 ， sdk 内部封装了上传附件的方法，所以不用自己进行上传 （前端实现 ---- sadais-upload）

（1）、会发两个接口：

（2）、第一个是请求的OSS服务器（包括 华为云/腾讯云/阿里云， 参数传 标识），返回的是对应的OSS的信息 （当然请求头要携带token信息和其他标识，不然直接访问就 403 forbbiden 了）

**这个请求是自己项目的后端出的接口（后端集成了几种云存储的相关信息，用于请求OSS文件上传的）这种重要信息存在后端比较安全，前端明文显示不安全（所以就请求接口获取）**

接口返回的信息：（包含远程请求OSS的关键信息）

![图片](https://note.youdao.com/yws/res/1699/WEBRESOURCE413050c858e7ba37059d5042cddc2958)

（3）、请求成功后，sdk内部再自己发一个请求（通过 XMLHttpRequest ，小程序环境的话通过 uni.uploadFile 方法）这个方法就是调用OSS上传文件了

对应到的是这个方法了：

这是核心方法

接口响应（没有任何东西 就是正常的）

header请求头就是成功的

![图片](https://note.youdao.com/yws/res/1741/WEBRESOURCE767dfce6863378bde073efad81d57c5a)

上面代码那个 uploadFilePath 的路径是传到 oss 后前端自己将路径返回给页面显示的，主要组成是

https://file.antibao.cn/file/20230402/xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx.jpg

### beforeDestory 不生效

### 使用到 JSON.parse() / JSON.stringify() 地方 记得 try { } catch(e) { console.log(e) } 一下 如果是 JSON.parse() 报错的话，try 和 catch 两个块（两边） 都会走的 ！

throw

![图片](https://note.youdao.com/yws/res/1749/WEBRESOURCE7860a209c69365d25b636c7440c01af8)

### filter+ indexOf filter + some filter + includes 筛选出想要的item组成的数组 循环中若该 item 是 true 则会返回该 item

### html2canvas 插件 下载图片遇到 跨域时的解决：

**注意 ： 下载的对象必须是原生的 html 标签**

或者 不用原生标签的话呢 要包多一个 div ，然后 ref 绑到 div 上面，这样也可以

方法的代码：

![图片](https://note.youdao.com/yws/res/1747/WEBRESOURCEd4199be036f9b1e474dbaf9f67494025)

设置 crossorigin 为 anomymous （配合服务端也需要进行 跨域请求 设置）

要改一下参数，这样 img 标签不用设置 crossorigin 属性，不会有跨域报错，也能正常下载了

使用到 v-for循环 拿 每一个的 唯一值 动态绑定到 ref 上的话，有问题 ！！！！

要换成 document.getElementById 才行 ！！

列表页 template 中 这样写 ！！！

![图片](https://note.youdao.com/yws/res/1751/WEBRESOURCEda3b0a278be64f29ceeb7144e9d2bb49)

顺便用 async / await 优化下 promiase.then() 方法：

多个 await 如何精确 获取/定位 到每一个的错误（如果接口报错）

多个 try...catch 块（ 不优雅） 可以用 .then() 包一下 await 后的 promise ， 组合成 [ err, data ] 格式的函数返回值。

关于 html2canvas 使用还有几个问题：

1. 长截图不完整的问题（包含滚动条）： 主要是设置 height, windowHeight, width, windowWidth 这几个参数。

![图片](https://note.youdao.com/yws/res/1754/WEBRESOURCE9d9645b9fd2a4df2925441a6b77ac7b9)

最后恢复到滚动位置：

2. 转换包含 input框页面时， input 框内文字不居中显示： 锁死 1.0.0 版本即可。

3. html2canvas 本身是通过模拟浏览器渲染来工作的。这种模拟很难 100% 覆盖所有 CSS 细节、字体渲染怪癖以及浏览器自身的复杂行为。

4. 如果想要高还原保真的话，推荐后端实现，使用无头浏览器（Puppeteer 等）模拟真实浏览器的实现进行pdf生成方案会更具备高保证效果的！

### el-input 的 type = "number" 时 去除右边的图表显示

![图片](https://note.youdao.com/yws/res/1760/WEBRESOURCEfd566b6cd5dd9b6d8cc45d62f5a1da20)

<style lang="scss" scoped>

::v-deep input::-webkit-outer-spin-button,

::v-deep input::-webkit-inner-spin-button {

-webkit-appearance: none !important;

-moz-appearance: none !important;

![图片](https://note.youdao.com/yws/res/1762/WEBRESOURCE45e37973899a81d04f23bdd1eb574994)

-o-appearance: none !important;

-ms-appearance: none !important;

appearance: none !important;

margin: 0;

}

![图片](https://note.youdao.com/yws/res/1765/WEBRESOURCE715feb370f7148df8ebfee57e3ab7177)

::v-deep input[type='number'] {

-webkit-appearance: textfield;

-moz-appearance: textfield;

-o-appearance: textfield;

-ms-appearance: textfield;

![图片](https://note.youdao.com/yws/res/1799/WEBRESOURCEac4a00b0598a152e24ffbc44b8ffdd70)

appearance: textfield;

}

</style>

### js 正则校验是否 包含中文字符

const reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g')

![图片](https://note.youdao.com/yws/res/1796/WEBRESOURCE3babc6fcc184fe32f9611fb16ce84894)

### toFixed(0) 和 parseInt() 保留整数

toFixed() 是 四舍五入 parseInt() 是 只保留整数 部分

### 防抖节流的区别 ：：

### 防抖： 规定时间内只执行最后一次 （规定时间值执行一次，不是需要连续请求的场景，只需要最后一次）

### 节流： 规定时间内只触发一次 （每隔相同时间请求相同资源）

![图片](https://note.youdao.com/yws/res/1782/WEBRESOURCE5b0a047ad7bb1c0ae6a86097f8c0ba86)

### 关于 el-form 的 validate() 回调函数的问题

进不去 validate 回调 导致 不能保存的 问题 ！！！！！

### el-menu 的 collapse 属性 默认会使用 false

### 、ture 的情况：

### 、 false 的情况：

![图片](https://note.youdao.com/yws/res/1794/WEBRESOURCEe1088a6b09b615801353e45dd52e3a25)

### 关于静态路由的 重定向

### 绝对定位 + 弹性盒子

### 数组 map 函数的 使用 ：

（ 1、）

（ 2、）

![图片](https://note.youdao.com/yws/res/1801/WEBRESOURCE6abce6315ae870e290d67f86de3b1080)

### 几个常见的名词缩写 ：

CSR 客户端渲染 client-side-rendering

SSR 服务端渲染 server-side-rendering

SSG 静态站点生成 static site generation

XSS 跨站脚本攻击 cross-site scripting ( iframe 会引起 xss )

![图片](https://note.youdao.com/yws/res/1810/WEBRESOURCE0cb010446260509029e779cc0a21fc17)

CORS 跨域资源共享 cross-origin resource sharing

CSRF 跨站请求伪造 cross-site request forgery

CORB 跨域读阻塞 cross-origin read blocking

### node-sass sass-loader node 版本问题 项目运行报错 多半与这两个插件有关

( 就是 node 14.18.0 / sass 1.55.0 / sass-loader 8.0.2 版本问题 ，然后就是 vue.config.js 文件的配置, 然后 /deep/ 换 ::v-deep)

![图片](https://note.youdao.com/yws/res/1821/WEBRESOURCE9fef19cdf29e33f280f8dea5b27e1d84)

sass: {

prependData: `@import "~@/styles/variables.scss";`,

sassOptions: { outputStyle: 'expanded' },

},

element ui 打包后 在chrome浏览器下偶尔存在 icon乱码 解决方案：

![图片](https://note.youdao.com/yws/res/1815/WEBRESOURCE39a4e07abb47128a2e8d306c655b69b9)

（1）、常见的方式 下载 node-sass， 卸载 sass （ sass + sass-loader 变成 node-sass + sass-loader ）， 安装后还要记得替换语法。找到项目的::v-deep替换成/deep/。

node 版本 和 node-sass 版本 对应表： （node14 的话 node-sass 下 4.14.0 / 4.14.1 版本）

**node-sass 下载不了： 开代理 / 设置 sass-binary-siste 源 / -f npm 强制下载**

外网安装 node-sass 地址： https://github.com/sass/node-sass/releases

set SASS_BINARY_PATH=D:/nodesass/win32-x64-64_binding.node

![图片](https://note.youdao.com/yws/res/1818/WEBRESOURCEfc0c06656a2d03b4d928b8093342ec9e)

############ 或者可以不用这么麻烦，直接设置 .npmrc 文件的 sass_binary_pass 就好了

sass_binary_site=https://registry.npmmirror.com/-/binary/node-sass

(npm config ls 可以查看)

（因为elementui 的 package.json 中是 node-sass 和 sass-loader， 而自己项目中是 sass 和 sass-loader ， sass 是依赖 dart-sass，和 node-sass会有兼容性问题）

element 插件：

![图片](https://note.youdao.com/yws/res/1841/WEBRESOURCE1b275eb7e8edd1b076ae28d10ebca446)

自己项目：

sass 依赖：

因为 M1 node-sass 不维护了，然后 rosetta 的方式也行不通（改天有时间可以再研究研究？），所以只能改成 sass 的这种方式 ！！！（而且也是官方推荐的方式） node-sass 2020年 就废弃(deprecated)了,推荐用sass(dart-sass)

（2）、直接升级 sass 和 sass-loader 的 版本号

升级到 "sass": "^1.55.0"

![图片](https://note.youdao.com/yws/res/1844/WEBRESOURCE62e078943ad966d5163db8905a45c9d6)

"sass-loader": "^8.0.2"

**然后删除依赖 再重新安装 （注意： 这时的 node 版本必须是 14， 而且最好是 14.18.x ）不然可能其他的14版本也跑不起来 ）**

之前是sass-loader是 7.0版本，

然后stackoverflow查了一波

最后做的修改：

![图片](https://note.youdao.com/yws/res/1839/WEBRESOURCEe936e7fd21ea8503d44c8c7286b33aa4)

然后把项目中的所有 /deep/ 修改成 ::v-deep 就能成功跑起来了。

总结： 这次的项目（aup-front/portal-frontend 门户前端）总共改了5个地方：

1. node版本要14.18.2

2. 注释掉.npmrc文件的内容（只在内网使用到，外网直接默认连的源仓库地址）registry 和 sass_binary_site

3. 修改 vue.config.js 配置文件的 loaderOptions

![图片](https://note.youdao.com/yws/res/1830/WEBRESOURCE94193ae668e11482c3947f8cd200b933)

css: {

loaderOptions: {

sass: {

// 向全局sass样式传入共享的全局变量

prependData: "$PUBLICPATH: '" +

![图片](https://note.youdao.com/yws/res/1837/WEBRESOURCEcddde69e30f3a305719fcd001aedf047)

process.env.VUE_APP_PUBLICPATH +

"';" +

'@import "./src/scss/element-variables.scss";@import "~@/scss/project-variables.scss";',

sassOptions: { outputStyle: 'expanded' },

},

![图片](https://note.youdao.com/yws/res/1847/WEBRESOURCE4716010fd1b7f73c5e0747d74e700552)

},

}

4. 修改 package.json 文件(删除node-sass, 添加"sass": "^1.55.0", 修改"sass-loader": "^8.0.2")

5. 全局替换, 将项目中用到的 /deep/ 改为 ::v-deep

6. npm install，然后 run serve ，完事。

![图片](https://note.youdao.com/yws/res/1857/WEBRESOURCEa0da6f86f2ab6cf08635ecbf2236bc5f)

然后就大功告成了。！！！

（3）、修改 webpack 配置文件 （vue.config.js） ----- 实测可行

// 将 JS 字符串生成为 style 节点 'style-loader', // 将 CSS 转化成 CommonJS 模块 'css-loader', // 将 Sass 编译成 CSS 'sass-loader',

// 有用代码开始

css: {

![图片](https://note.youdao.com/yws/res/1860/WEBRESOURCE96a9654632844e36bf43590124d435b9)

loaderOptions: {

sass: {

prependData: `@import "~@/styles/variables.scss";`,

sassOptions: {

outputStyle: 'expanded' 选项有 expanded 和 compressed （默认），这里是设置的 sass（dart-sass）的output，不是 node-sass 的

![图片](https://note.youdao.com/yws/res/1878/WEBRESOURCE3ed09fd626cbffe8356419513f15090a)

}

}

}

},

（4）、拆分文件，按需引入scss 文件 （参考 271）

![图片](https://note.youdao.com/yws/res/1883/WEBRESOURCE234d27022acf16b3c21d6199532a90f5)

import 'element-ui/lib/theme-chalk/src/index.css'

### PWA 渐进式网页应用 progressive web application

### 服务端渲染 ssr 和 nuxt.js

csr 客户端渲染 （Vue.js / React.js / AngularJs...） client-side-render 直接浏览器解析js代码，再显示回页面上

ssr 服务端渲染 （ vue的nuxt.js （专注 UI 渲染） 用 vuejs的语法写服务端 ） server-side-render 直接服务器返回渲染好的 html 代码， 直接丢给浏览器显示，直接拿html 进行显示，不用解析 js

![图片](https://note.youdao.com/yws/res/1896/WEBRESOURCEbb9cf78f60a82d5d2fe07ac55925625c)

vue-cli 脚手架 （csr） 和 nuxt.js (ssr) 创建项目

ssr 好处： 解决首页白屏的问题，SEO搜索引擎优化

链接: 彻底理解服务端渲染 - SSR 原理

小实践一波： 网站使用 rem 布局

<nuxt /> 相当于 <router-view /> 组件

![图片](https://note.youdao.com/yws/res/1898/WEBRESOURCE5b032856d185847475dc079c0f5a55d4)

sso: single sign on 单点登录

### box-orient（盒子方向） 属性规定框的子元素应该被水平或垂直排列。

box-orient: horizontal|vertical|inline-axis|block-axis|inherit;

box-flex（类似flex属性） 指定 box 的子元素是否灵活或固定的大小。

box-flex 和 box-orient 就是相当于现在 弹性布局 中的 flex属性 和 flex-direction属性 （是这两个属性的早期版本）

![图片](https://note.youdao.com/yws/res/1900/WEBRESOURCE401fa2afca805aea00814b75e9ceae64)

### http请求的 request headers 中的 content-type 的类型

一般： post 对应 application/json

get 对应 application/x-www-form-urlencoded

还有 form data 类型（一般像文件上传就是用的 form data）

response headers 的 content-disposition 字段，表示响应回复的内容，一般包括 inline 和 filename 两个字段内容，后者携带了返回的文件的后缀名，如果有的话

![图片](https://note.youdao.com/yws/res/5376/WEBRESOURCE75a710363710213445ee0503ff5540e2)

是以内联的形式, 还是以附件的形式下载并保存到本地。

### line-height: 2 自身字体大小（font-size） 的两倍

### Object.create(null) 创建一个纯净的对象（没有原型的对象）

### symbol 基本数据类型

一个Symbol() 返回的是一个唯一的symbol类型的值。 可作为唯一的对象属性的标识符

![图片](https://note.youdao.com/yws/res/1902/WEBRESOURCE2beb3180761165a5cd1f5eba42eb25f8)

### ES6 Map

`const map = new Map()`

map.size

map.set(key, value)

map.get(key)

![图片](https://note.youdao.com/yws/res/1905/WEBRESOURCE7f1516a48b2d298129164d5b307b23d3)

map.has(key)

map.delete(key)

WeakMap: 弱引用 能解决循环引用的问题 key值 不可枚举

key 必须是对象 ， 值 任意

实现 深拷贝 的时候 可以使用 weakMap

![图片](https://note.youdao.com/yws/res/1907/WEBRESOURCEd59740b6e2ccc91d5f5c458856d52b13)

### 扩展运算符 / 深浅拷贝 / 解构

let [a,b, c,d, e] = "hello" 解构这么玩

a: 'h' e: 'o'

### 对象/ 数组的 扩展运算符 [ ...数组 ] / { ...对象 } : 如果待拷贝的对象只有一层，那么这时候扩展运算符就是深拷贝 ! 如果包含多层引用（大于等于2），则第二层开始的引用就是浅拷贝！

（ 扩展： 可以使用 JSON.parse(JSON.stringify()) 进行深拷贝 ）

![图片](https://note.youdao.com/yws/res/1915/WEBRESOURCEb460e61d80ef362996d1ae6d39f25208)

### 引用类型 直接赋值 （=） 那肯定就是浅拷贝了 （简单类型可以理解为就是深拷贝）

### 解构 也是一样 对象只有一层：深拷贝

对象多层嵌套： 浅拷贝 如果是在 vue 中使用解构的话，在某些时候可能会失去响应式

`（ Object.assign( {}, soueceObj / 一层数组 ) 也是浅拷贝 ），跟1、一样的，都是解构`

`let a = {`

![图片](https://note.youdao.com/yws/res/1913/WEBRESOURCE540da0d87b91e08145ca0fd1795ba12e)

name: 'HB',

age: 18,

address: {

detail: '详细地址'

}

![图片](https://note.youdao.com/yws/res/1917/WEBRESOURCEe7aed6e853eaa88e4e8be477438a7672)

}

`let { name, age, address } = a`

### Object.keys( ） 和 Object.getOwnPropertyNames( )

```javascript
可枚举（enumerable）： 属性能否访问的到，如果该属性的 enumerable 为 false，那么 循环 for..in （穷举每一个属性）/ Object.keys() / JSON.stringify( ) 则是访问不到该属性的
```

但是， Object.getOwnPropertyNames( ) 可以访问得到，他是返回所有的属性

![图片](https://note.youdao.com/yws/res/1921/WEBRESOURCE1e0875cce02caa0533a372d050a29578)

Object.create(proto, [ propertiesObject ])

创建一个新对象。第一个参数是一个对象，表示新创建对象的原型对象（里面的方法和属性是增加到创建的对象的原型上的），

第二个参数可选，是该对象本身的属性或方法

返回值是一个对象，带着指定的原型对象和属性。

例： Object.create(null) 创建一个纯净的（不带原型）的对象

![图片](https://note.youdao.com/yws/res/1923/WEBRESOURCE91030c7462bb29f8c000c5bdc644280f)

字面量方式创建空对象 相当于 Object.create(Object.prototype)

Object.defineProperty(obj, prop, descriptor)

descriptor - 描述符 的值：

### configurable: 默认为 false，重新定义该属性就会报 redefine 错误，也不能通过 delete 修饰符删除 优先级最高

设置为 ture 时 就可以 修改了 （重新defineProperty）

![图片](https://note.youdao.com/yws/res/1928/WEBRESOURCE56c6088538d947e788131118be4703c6)

### writable: 默认为 false， 当为 true 时，value 才能被 赋值运算符 修改，但是writable为fasle，configurable为true时 value值 还是可以通过 defineProperty 进行修改的

### enumerable: 默认为 false，是否可枚举，为 true 的时候 for...in/Object.keys()/JSON.stringify()/Object.assign() 才能访问的到该属性

### value: 属性的值， 默认为 undefined，

### set: 属性的setter函数，默认值为 undefined，

### get: 属性的getter函数，默认值为 undefined，

![图片](https://note.youdao.com/yws/res/1930/WEBRESOURCE9d9e07777e4070d2f5d953af014c3592)

Object.getOwnPropertyDescriptor(obj, prop)

检索 对象 的 属性描述

Object.setPrototypeOf(obj, prototype)

设置对象的原型上的 属性

Object.getPrototypeOf(obj) 获取 setPrototype设置的 原型上的属性

![图片](https://note.youdao.com/yws/res/7186/WEBRESOURCEc67cbf22ff98e73e2ae6925925ed6069)

获取对象的原型上的 属性，如果没有继承属性，则返回 null

对象的__proto__ 属性也可以获取对象上的原型属性+方法

冻结对象 Object.freeze(obj) 和 Object.seal(obj) 两者区别 ：

### seal ： 封闭对象 （对象原有属性如果可改那将继续保持该属性可修改，不可增加属性，不可删除已有属性） 传入什么，返回什么

### freeze ： 冻结对象 （完全不能对这个对象做任何操作，该对象原型也不可改） 传入什么，返回什么 把响应式（data中定义的对象）变成非响应式，释放内存！ ---- Object.freeze() Object.seal()

![图片](https://note.youdao.com/yws/res/1974/WEBRESOURCE25b4f4182b09dbc82f48dbd1d46b8414)

`vue3 是 markRaw 标记一个对象，使其永远不会转换为 proxy。 const obj = markRaw({})`

Proxy 对象 的使用 ：

是什么？ 主要是 创建一个对象的代理，从而实现基本操作的 拦截 和 自定义 （比如 属性查找、赋值、枚举、函数调用...）

怎么用？ new Proxy(target, handler)

参数？ target： 被 Proxy 包装的目标对象 （任何类型的对象，包括原生数组、函数、或者另一个代理）

![图片](https://note.youdao.com/yws/res/2001/WEBRESOURCE994fdfe2d8692a5abd877c6911f435ff)

handler：（通常是）一个属性为函数的 对象，包含Proxy的各个捕获器 traps（一堆特点的捕捉器方法）

### js 的连续赋值

`var a = b = { n: 3 }`

相当于：

a = {n:3}

![图片](https://note.youdao.com/yws/res/2004/WEBRESOURCE6285b63a30b18de46589d52b2ac3554c)

b={n:3}

面试题：

所以最终的问题是 a.x 和 b.x 的值

由上图可知

// a.x 为 undefined

![图片](https://note.youdao.com/yws/res/2006/WEBRESOURCE7cfb3c41773f29596f09ac381da230b8)

// b.x 为 { n: 2 }

扩展理解：

------------->>> 啥比题目：

报错的根本原因就是 class 类 中声明的代码总是在严格模式中执行。所以一开始的 window.name 赋值 就是专门用来混淆的，严格模式下是

----------------》 严格模式下，函数里面没有的变量还是会往全局作用域上面找，但是是没有 this 这个执行上下文对象的

![图片](https://note.youdao.com/yws/res/2008/WEBRESOURCE769396719f68f4ee95ea6de23edfaad9)

严格模式下打印this，结果为 undefined

然鹅，非严格模式下（正常情况下写的代码）

然后，函数里面 var 的问题

new 一个函数做了什么？

### 创建一个空obj

![图片](https://note.youdao.com/yws/res/1992/WEBRESOURCE1cb6ef6bea52045708f00fff11280fd2)

### 实例对象obj的__proto__ 等于 构造函数的 prototype

### 改变 this， 指向 obj

### 构造函数有返回值，实例对象就返回该返回值，没有就返回 obj

---------->>>> 所以，再回到原来的这道坑比题目：

上面 *6 是重点

![图片](https://note.youdao.com/yws/res/1994/WEBRESOURCEda0348f02122a26f32e7f68a6da61d32)

然后 对象 又不一样了

---------------------------》》》 有趣的面试题：

### 微信小程序中使用伪元素 伪元素只认得 view 标签 ，直接将伪元素加在引入的组件的标签上，会出现定位的时候有问题！

### git 合并某个分支上的 单个 commit

使用 cherry-pick 命令

![图片](https://note.youdao.com/yws/res/1999/WEBRESOURCEf550f771e971740573bf613c4519c9a7)

单个commit合并

如何处理冲突

合并多个连续分支

### 如果相同代码不同执行结果 （比如患者端 / 推广端 和 医生端 的图片预览 关闭 ： 一个可以，一个不可以）在线上/本地 都一样的话， 记得 刷新 清缓存 ！！！！！！ 多半都是缓存问题。

清缓存之前 记得 先 更新（拉） 最新的代码 ！！！！！！！！

![图片](https://note.youdao.com/yws/res/1997/WEBRESOURCEc3669f6d075964c532bdce0122e426bb)

### typeof 的 返回值

数据类型判断：

### typeof

### instanceof

### constructor （判断 object 不准确） // 修改 constructor 属性 影响判断

![图片](https://note.youdao.com/yws/res/2030/WEBRESOURCEe93b13f52c5ad44f6a55e8aeb4477640)

constructor 构造函数 跟 new prototype 一起使用

可以为除了 null 和 undefined（因为这两者没有相应的构造函数）之外的任何类型指定

constructor 属性（如 String、Number、Boolean 等）

所有函数都有 prototype （原型对象）

### Object.prototype.toString.call(xxx) === '[object Array]' // 判断数组 call / apply 用来改变toString方法的执行上下文

![图片](https://note.youdao.com/yws/res/2043/WEBRESOURCE2be4e629e9327f9406e0c8ab0c42f71f)

### Array.isArray() // 判断数组

call / apply / bind 改变 this 指向 （区别）：

this 是什么 ？ 执行上下文对象

### 管理后台有关 列表页查询面板的 缓存问题

因为管理后台的查询条件都是在项目中使用的vuex进行缓存的，所以默认情况下不做任何的处理，切换路由的时候是会缓存上一次的查询条件的，

![图片](https://note.youdao.com/yws/res/2053/WEBRESOURCE638d2787b67a04fed0efee886824edbb)

如果没有缓存，可能是在 页面路由跳转的时候 router.beforeEach 做了判断，也可能在页面的created/mounted 等初始化函数（方法）中做了处理，

还可能在页面离开（销毁）前做了处理 beforeDestroy / destroy / deactivated 等声明周期函数中。

-------------------------------》》》》》》》》》》》》

之前的所有后台项目默认都是不使用 keep-alive 进行缓存的（要的话在对应的组件的meta元信息里设置cache为true，默认是false）

最后表现：

![图片](https://note.youdao.com/yws/res/2055/WEBRESOURCE04ac4e1e57e2d9622bb74168324f759f)

只有后面的点梯运营后台/ 神匠 运用了 该 缓存机制 ！！！！

项目中这里的keep-alive有开启的话，那么基本路由前进后退等操作（像保留查询条件信息，保留上一次的操作记录）都是由keep-alive 的缓存来执行的，就不跟vuex什么事了。

而且对配置了 keep-alive 的组件的 handleQuery方法在一个地方统一做了处理，就不用每一个页面都加一个钩子函数 + 调用handleQuery了，直接 mixin 到该页面就好了

------------------------》》》》》》》》》》》》》》》》

然后现在的页面都是默认要缓存查询历史的（存的 vuex 中 / 或者直接在 index.vue 页面中，切换路由没刷新页面，所以再切回去 index.vue 页面，还是保留着之前的查询条件？（只要不手动刷新页面）），除非有特殊情况

![图片](https://note.youdao.com/yws/res/2062/WEBRESOURCE984369743315755b73af03637299a0f9)

就是存的 vuex 中 的 （在 searchValue 对象中，默认form.searchValue.xxx 之后 就多了 xxx 这个属性， searchValue 存在的 vuex 中， 所以不刷新页面的情况下是缓存到的xxx该字段的， 这时候又不关 keep-alive 什么事了）

所以总的就是说， 缓存（保留查询条件/上一次的操作记录等），keep-alive 和 vuex 都能实现同样的功能，但是实现的方式是不一样的 ！！！

keep-alive + vuex： keep-alive + vuex 让缓存的页面灵活起来 / 动态修改 include 属性

### align-content 和 align-items 区别：

### iframe 和 web-view 区别

![图片](https://note.youdao.com/yws/res/2012/WEBRESOURCEc7640dd53e316fce05c84e441922c0a0)

### iframe是属于 html的一种标签， web-view 是 原生系统，用于移动端嵌入web技术，方式是 内置了一款高性能 webkit内核 浏览器。

### 

app / 小程序 环境没有 XMLHttpRequest 对象 ，只能用 wx.request({})接口 调用后台接口， im 写成 webview 的， app 在 hybrid 里可以用 document 对象

### npm 报 Unsupported platform for n@8.0.2: wanted {"os":"!win32"} (current: {"os":"win32","arch":"x64"}) 错误时，暴力点 直接加 --force 搞定 ！

node 的 n模块，专门用来管理 node 的版本的

![图片](https://note.youdao.com/yws/res/2019/WEBRESOURCEd865caa16199f55120a0ec04ca5f8bc9)

然而，并没有什么卵用。， n模块并不支持 windows 系统 ！！！！ linux / mac 上可以 ！

所以，还是 上官网下载对应的版本覆盖安装叭~

---------->>> node 的另一个版本管理 --- nvm （node version manage 版本管理工具）

## 一、nvm： 一个单独的软件包， 切换node版本使用， 有 nvm for windows （平时说的nvm就是这个）。nvm 可以在一台电脑上安装多个版本的 nodejs，进行切换，跟 node 是两个独立的安装包

如果cmd报这个错误，

![图片](https://note.youdao.com/yws/res/2016/WEBRESOURCE697dbdafaf7f5d02a44a2e356e3e555d)

则需要切换到管理员模式， 有了：

--------------------- >>>>>>>> nvm 常见的命令 ：

nvm list： 查看当前的 node 版本（* 表示 当前正在使用的版本号）

nvm use xxx（版本号，如 10.18.0）：切换指定的 node 版本

nvm install / uninstall xxx（版本号，如 10.18.0）： 安装/卸载 node 版本

![图片](https://note.youdao.com/yws/res/2022/WEBRESOURCE5a290e42148c21d36d76cd827986a574)

nrm 啊， 就是用来 切换 npm 的镜像（npm 下载源）用的

## 二、n 模块：一个 npm pakage, 是依赖 npm 进行 全局安装，是 node 中的一个模块 （必须先安装 node 和 npm ），不支持 windows 系统

### 提交到暂存区 （工作区 ----> 暂存区 ------> 仓库）

git 三个概念： 提交 （commit） ---- 仓库 （repository）---- 分支 （branch）

仓库 分为 本地仓库 和 远程仓库

![图片](https://note.youdao.com/yws/res/4739/WEBRESOURCE2e01c0d4b42583eec6f58389175dfd94)

（1）、 git commit -m ":sparkles: feat: my new modified code" 是提交到 本地仓库 ！！！！ 提交到本地仓库（commit）的是什么时间 ， 推送（push）到 远程仓库 的就是什么时间 （具体时间以本地仓库的时间为准）

vscode 查看历史 commits （记录的都是本地的commits）

提交到 暂存区 后，要撤回的话

回撤代码（回滚？）------ 就是撤销修改

（1）git checkout <filename> （文件名） 回撤修改的代码文件

![图片](https://note.youdao.com/yws/res/4741/WEBRESOURCE8bf7c1f5f3401f6a63d1c83e89e9ad36)

相当于这个 勾勾 的功能

（2）git checkout master（分支名） 切换到本地名为 master 的分支

------------ 回滚 ----------------->>>

（1）、保存修改的代码，未提交暂存区，直接回滚（ git checkout <filename>）

（2）、提交到暂存区的代码，要回滚，使用 commits 的 Undo Commit （git reset HEAD^） git reset --haed 该分支最后一次成功提交的hash值

![图片](https://note.youdao.com/yws/res/4743/WEBRESOURCE8ec44e15e73a341ee50b3227580f1e81)

（3）、提交到远程仓库了，要回滚。。。笨方法 （找到以前的要修改的那次提交，直接复制那个文件未修改前的代码，修改后重新覆盖提交）

file history 和 时间线 都提供了相同的功能 ， 找到并修改就可以了。

(2)、

### 直接本地文件夹 git init 然后 撸了一堆代码准备提交到 github后

### 添加 远程仓库 并给远程仓库起个名字为 HHH

![图片](https://note.youdao.com/yws/res/4745/WEBRESOURCEecb4fde29e6967b16f41c1348b9baa2d)

git remote add HHH https://github.com/Hbin-Zhuang/Front-End-Learning

在 1、和 2、之间可能还需要条命令： git branch -M main （如果是github的话？）

### 然后就可以 push 了 （ HHH： 仓库名， main/master： 远程仓库默认创建名字叫 main/master 的分支）

git push (-u HHH main/master)

github 新建好的一个仓库：

![图片](https://note.youdao.com/yws/res/4747/WEBRESOURCE9e0296090f60df89d069137826bb61b3)

(3)、合并冲突

current change： 当前已存在的

incoming change: 你修改完的

git merge --abort 放弃此次合并

（4）、删除分支 git branch -d <branch-name>

![图片](https://note.youdao.com/yws/res/4749/WEBRESOURCE075812fe23c36edae48d17717e0558ff)

（5）、vscode 提供的分支图形化操控界面 （不常用，也就那几个命令）

（6）、reset - 提了commit 的回滚

revert - 提了 remote 的回滚

rebase - 产生冲突的变基 （--continue 一个冲突解决后进行下一个冲突的解决， --skip 跳过此次变基，--abort 中止/放弃 此次变基）

每次拉代码的时候都用 git pull --rebase 提前解决冲突 ！！！

![图片](https://note.youdao.com/yws/res/4752/WEBRESOURCEd5f77f486eebc17cd8c5495e9db1a84f)

cherry-pick - 合并其他分支指定的提交（分支排序没有按照时间线）

merge - 合并分支（整个分支合并，可能会时间线交叉）

merge 和 cherry-pick 合并的时候之前有合过相同的提交是不会去重的，而是出现为两条一模一样的提交记录 ！

（7）、切换分支前代码做了修改，直接 git stash （保存本地修改的代码，恢复干净的工作目录，即恢复为未修改前的状态，同时又替你stash藏了代码在stash store 中）就可以切换分支了，然后再从其他分支切回来就 git stash pop（恢复stash栈中的代码，每次pop一条）就好了 （git stash apply 是单纯的恢复，不删除stash记录）

（8）、git blame -L 查看历史修改记录 ------ 追溯谁写的

![图片](https://note.youdao.com/yws/res/4754/WEBRESOURCE063422b86963deb834dbe2c8da234f9a)

用法： git blame -L <n, m> <filename> （指定范围的行数） filename 文件名 必须是完整的路径（src/views/knowledge/Knowledge.vue） 不然定位不到你的那个文件的

每一条提交记录的 id 相当于该条记录的哈希值 ！！！

非 .mit / apache 开源的项目 的 license 文件，使用该项目源码可能需要遵循一些规则。

vscode git 分支 的 一些说明 ：

(9)、git 修改上次git commit的时间

![图片](https://note.youdao.com/yws/res/2086/WEBRESOURCE9f9603f097ade4bb3f2853ec79c6d524)

git commit --amend --date="commit_time"

git commit --amend --date="Sat, 6 May 2023 08:28:59 +0800"

git graph 插件

但是远程仓库好像没有更新到。。

但是本地 git log 能打印到： 就是你手动commit --amend 的那个最新提交记录 没有跟 giuhub 上的一样

![图片](https://note.youdao.com/yws/res/2091/WEBRESOURCE61b8a835a07a8d64c1d819e368febdc7)

常见的 git 问题：

（1）、切分支报 path invalid 错

查了下，主要就是 windows 系统的问题，不支持带 : 的文件命名

解决： 设置 git config core.protectNTFS false

然后再强切，就好了

![图片](https://note.youdao.com/yws/res/2100/WEBRESOURCE3467fddc9a9a8bd75152ff0466413ddc)

github 找项目 途径：

### github.com/trending

### 特殊查找资源的小技巧：

常用的前缀后缀

· 找百科大全 awesome xxx (e.g. awesome vue)

![图片](https://note.youdao.com/yws/res/8582/WEBRESOURCEe13df1130dcc0407c88b37f31b2c25d8)

· 找例子 xxx sample

· 找空项目架子 xxx starter / xxx boilerplate

· 找教程 xxx tutorial

### uniapp 适配 html5+ 扩展规范 plus 对象 只有 在 app上才生效

条件编译调用 HTML5+

![图片](https://note.youdao.com/yws/res/2189/WEBRESOURCE7eb95b8a480823b15d64fd270ad8801e)

html5 runtime

### const 定义的对象（引用类型）的值可以改变，定义的基本数据类型不可以改变。

`主要是指针（栈内存地址）没有发生改变。 const obj = { }, obj.name = 'HB' , 首先先在栈内存中开辟`

一块空间存放 obj（ { } ） 的地址，然后地址指向堆内存空间，存放该地址的值，obj.name 改变的是堆内存

空间，栈内存还是没有改变（指针的指向不变）。

![图片](https://note.youdao.com/yws/res/2244/WEBRESOURCE5ee892211a962fa5bcb5bd3d1c60521f)

let的一个特性是禁止在同一个作用域下重复声明，所以以下代码会报错 :

`const 定义的变量 必须初始化，不然报错； var 和 let 则不用 。`

### refs 和 document.xxx 获取 dom 的 区别 ：

### 、 ref 用来给 元素 或 子组件 注册引用信息，如果是元素，就是引用指向绑定的元素的DOM，如果是子组件，那就是引用指向子组件的实例

用 ref， 不同的组件互相隔离，不存在命名冲突

![图片](https://note.youdao.com/yws/res/2246/WEBRESOURCE1e2f1e927a49aebc26ac74998d4948e6)

精准匹配，在vue 创建dom 的过程就直接赋予，不需二次查询，理论更快。

### 、用原生的方法 document.querySelector / document.getElementById 等方式： 每次用都需要做查询，比较消耗DOM节点的获取，匹配class类名时可能因为多个同名而选不到想要的DOM节点

vue中 v-for 循环时 动态设置 ref 会有问题 ！！

（1）、

（2）、

![图片](https://note.youdao.com/yws/res/2229/WEBRESOURCE1ec6f4ff6ae0969bb25ab7ca91890c52)

### a 标签 的使用

_target 属性 ：

需求： 点击一个链接，如果这个链接浏览器已经打开过，则刷新已经打开的链接窗口；如果这个链接没有打开过，则使用新窗口打开这个链接页面。

只需要 设置target属性值和href属性值一样就好了

默认 _blank 是打开一个新的页面

![图片](https://note.youdao.com/yws/res/2239/WEBRESOURCE35f7fe430458b3e4fa3cdb41a64ac939)

download 属性：

上面的截图也就是下面的意思。

图片转 base64 （dataURL） 或者 Blob ( new Blob( [ url ] ) )+ File Reader 对象

base64 （a-zA-Z0-9+/ 共 64 个字符），每3个字节一组，共24个二进制位。24个二进制位分为4组，每组6个二进制位。每组前加两个00，扩展成32个二进制位，即4个字节。

所以。base64编码 最后是 变大了 （因为3个字节变成了4个字节。）

![图片](https://note.youdao.com/yws/res/2227/WEBRESOURCEe4e15a73ca9bc3060093a2f4996c04c2)

以后的文件下载可以这么参考着来实现（都是请求接口的） 前端自己实现有点麻烦（各种插件尝试，又不熟悉用法+没有现成的完整实例）

后面就直接调接口就完事了！！！！！！

图片转 base64 方法 （就不会有跨域问题了 / 减少 http 请求） （图片的话就不请求接口了，直接把url地址转成base64的，丢到new Blob() 里就行了，或者用html2canvas插件也行）

（1）、 Blob和FileReader 对象

URL.createObjectURL 对象 专门将 blob 转为 DOMString类型的 url 用的api， （该 API 不会有 跨域问题 前端自己实现的）

![图片](https://note.youdao.com/yws/res/2260/WEBRESOURCE96f527c955128fda96e8fe8a5d872379)

浏览器直接打开文件

**注意：：**

（2）、 canvas.toDataURL()方法

创建一个 img 标签

url 转 file

![图片](https://note.youdao.com/yws/res/2263/WEBRESOURCE39ea06a3259505ea0d196aada35aeb05)

url 先转 blob， 再用 new File( [ blob ], filename, { type: 'xxx' } ) 传 blob 转成 file 对象

`// 使用 imgUrlToFile(imgUrl, function(file) { console.log(file); // 文件格式 });`

这样子每次都多一个图片请求了

### 

-S： --save 生产环境

![图片](https://note.youdao.com/yws/res/6980/WEBRESOURCEc6581f9c7f7fcf8ef8e7d8a9e95ff68e)

-D: --dev 开发环境

### 切分支的时候 node_modules 依赖是不会跟着切的

很多时候依赖装好了，但是依然运行项目会报错（而且其他人可以，其他电脑环境可以，就你本地跑的时候有问题），

这个时候就要考虑 依赖的版本问题 导致某些 api不兼容 然后报错的 ！！！

所以一些 主要的依赖要手动锁死版本，防止不兼容报错 ！！！！！！

![图片](https://note.youdao.com/yws/res/6992/WEBRESOURCEbf789b26bf599b565df20f9e4dafa2fa)

（1）、最常见的就是 package.json 中直接写死版本号 （不用 ^ 和 ~ 指定版本号区间）;

（2）、或者拿别人的带 package.lock.json (npm安装) / 带 yarn.lock (yarn 安装) 文件拷贝过来，删除自己的该文件，再重新装包 （npm / yarn 会根据锁定文件的包版本进行安装）

（3）、使用 npm 提供的 shrinlwrap 命令 锁定依赖包

符号^：表示主版本固定的情况下，可更新最新版。例如：vuex: "^3.1.3"，3.1.3及其以上的3.x.x都是满足的

符号~：表示次版本固定的情况下，可更新最新版。如：vuex: "~3.1.3"，3.1.3及其以上的3.1.x都是满足的

![图片](https://note.youdao.com/yws/res/2276/WEBRESOURCEdfaa2303d00e7f47edabc68c4b9b9c3f)

无符号：无符号表示固定版本号，例如：vuex: "3.1.3"，此时一定是安装3.1.3版本

**npm cache clean --fource 强制清楚本地缓存**

Yarn :

像npm一样，yarn使用本地缓存。与npm不同的是，yarn无需互联网连接就能安装本地缓存的依赖项，它提供了离线模式。最主要的是比 npm 快多了

Pnpm: 用的都是缓存来的 (多个项目的包，指向的都是同一个,都是软链) 软链接类似windows系统的快捷方式；

![图片](https://note.youdao.com/yws/res/2278/WEBRESOURCEbb4ee25d871412cca17a8c9c4a8ef66e)

还有一个特点，就是磁盘空间利用高效（Fast, disk space efficient package manager）

支持 monorepo ，monorepo 的宗旨就是用一个 git 仓库来管理多个子项目，而不是一个子项目一个git 仓库 （git submodule 形式，但是多个子项目还是单独是一个git仓库）

monorepo 依赖统一管理（避免重复安装依赖）、代码共享（减少重复代码和组件的开发和维护）、代码可重用性（同一个仓库中的不同项目可以重复使用） 包相互引用

pnpm install： 安装所有的依赖 （其他用法跟npm类似， update / uninstall / ..）

pnpm i 装包报错

![图片](https://note.youdao.com/yws/res/2284/WEBRESOURCE5a78a04beb6ffd20a6e9181a83261cda)

因为设置了淘宝镜像源，要去掉，重新设置成 npmjs 的源

先 npm get registry 查看npm源

设置 npm config set registry https://registry.npmjs.org/ 直接指向 npm

这时候再 pnpm i 就可以正常装包了

pnpm 升级 pnpm pnpm add -g pnpm

![图片](https://note.youdao.com/yws/res/2286/WEBRESOURCEca1cbc24c2d2feb9b187df71614258b3)

后面直接就可以用 pnpm（升到最新版本） 跑 不同的项目项目了 ，不用切换 node 版本（使用 14.x lst 版本）就可以

npm 和 yarn 不一样？

### if ... else if .. else 优化

（1）、单一条件优化

（2）、复合条件优化

![图片](https://note.youdao.com/yws/res/2290/WEBRESOURCEd10ed6f08199a935d63ae3256b04c29f)

### break continue return

### 、 break 是直接跳出整个循环 （不再执行循环函数）

### 、 continue 是跳出本次循环（就是本次循环里continue 后的代码不再执行），然后进入下一个循环体

### 、 return 是 结束当前方法，主要用于方法的返回值（没有返回值可以返回空或者不返回） return 强度 大于 break ; return 之后是整个函数（方法）都不会执行！！！ break是循环之后还有代码还会接着执行!!!!

### pre 标签

![图片](https://note.youdao.com/yws/res/2295/WEBRESOURCE1a4301e686ba512764d4adce832b8395)

### vue-cli 和 vite 的比较

（1）、vue-cli 通过 NODE_ENV 指定环境变量

.env 开发环境 和 生产环境 都会加载的 配置文件

.env.development 只在 开发环境 加载的 配置文件

.env.production 只在 生产环境 加载的 配置文件

![图片](https://note.youdao.com/yws/res/2298/WEBRESOURCE2d9fab610a084f73e4b12449ca08c5a3)

优先级： 同时存在的环境变量、 特定模式的环境文件（.env.development 和 .env.production）优先级高于.env的

修改了配置文件需要重启服务，文件内容变量才生效。

vite 通过 import.meta.env 中的 MODE 指定环境变量

vite的 hmr 原理： hot module replacement（热模块替换）

### 简简单单，数组扁平化 （小 循环 + 递归）， 虽然可以直接 arr.flat()

![图片](https://note.youdao.com/yws/res/5343/WEBRESOURCE5d406f7336dfd9f766fb70ea81652763)

来个回溯：

递归是 reduce ( 调用自己 return pre )

接着来 回溯 吧 ~ 回到题目

### ( 一 )、 document.designMode = 'on' 开启网页设计模式 ，想改哪里 随便改 ！

( 二)、 document.body.contentEditable = true 网页内容可编辑

![图片](https://note.youdao.com/yws/res/2305/WEBRESOURCE26e1612150a400e0a927d2b526da7915)

### css 属性 ： backface-visibility: hidden

设置背景图片（background-image）的透明度（直接opacity会是包括背景图片的整个DOM，而不是单纯只影响背景图片）

### 利用伪元素 ::after div本身设置 position: relative; z-index: 0; after 伪元素设置 绝对定位 和 上下左右都为0，content: ''， 然后再background-image: url(...)， z-index: -1; 就可以了。

### 使用 cross-fade() 图像函数

语法：

![图片](https://note.youdao.com/yws/res/2316/WEBRESOURCEd6376befd72552439dfe2117d5584861)

<image-combination> = cross-fade( <image>, <image>, <percentage> )

这是一张空图片，只有一个点

data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==

效果：

background-image 设置的div显示不正常时，要加上 background-size 属性。设置 cover

![图片](https://note.youdao.com/yws/res/2321/WEBRESOURCEa13f67af2675e974cecddcc36d4e16e5)

### vue3 的 ref、reactive、toRef、toRefs

### vite 配置 https

### vue 的几个 语法糖

v-bind: 相当于 :

v-on: 相当于 @

![图片](https://note.youdao.com/yws/res/2325/WEBRESOURCE2f18e4f1816a959bb5c97bce8df8b626)

v-slot: 相当于 # v-slot 都作用于 <template> 标签中 （除了只有默认插槽的情况下可以写在组件标签上）

(slot-scope 2.6版之前，后面都是 v-slot)

v-slot:default === #default === 什么都不写 默认插槽

v-slot:footer === #footer 具名插槽 （定义是 <slot name="footer">）

```javascript
v-slot:footer="{ record }" === #footer="{ record }" 作用域插槽 （定义是 <slot name="footer" :record="{ xxx: xxx }">）
```

![图片](https://note.youdao.com/yws/res/4248/WEBRESOURCE8a881994447562352153fb179e19dc7e)

### 

### vue3 的子组件 接收 v-model （可绑定 boolean / string / number / 数组 ） 和 vue2 的区别

v-model 绑定 元素（input、textarea、checkbox 单/多选框 加 value 属性、radio 加 name 属性、select） 和 组件

直接套个label就行了，不用 for 和 id 的。

有了 v-model，可以省略 name

![图片](https://note.youdao.com/yws/res/2449/WEBRESOURCE3f85cb5d0bcf8242719d84a33f27986b)

### 前端页面做了校验？？不好意思，我 直接 接口文档（swagger）、Postman 改（插入）数据，直接改数据库， 不用过你前端校验，爱输什么输什么。。。

### git 改 https 为 git://

git config --global url."git://".insteadOf https://

### undefined 和 null 区别 ？？？

(1)、默认情况下，两者使用没啥区别

![图片](https://note.youdao.com/yws/res/2451/WEBRESOURCE17ac1a34ea1826c10d893c4fd91edb03)

`let a = null; let b = undefined; 两者的作用基本一致`

然后双等号 判断 也是 true . undefined == null // true

`(2)、 null 表示 “没有对象”，即此处不应该有值，转数值为0 作为对象原型链的终点（ Object.getPrototypeOf(Object.prototype) === null ）`

undefined 表示“缺少值“，即此处应该有一个值，但是未定义， 转数值为 NaN 函数无返回值为undefined

typeof null === 'object' ? 怎么理解？？？

![图片](https://note.youdao.com/yws/res/2453/WEBRESOURCE2195983af4f2684ca55ff4023bd22fda)

可以理解为是一个历史遗留的 Bug

js的最初版本是使用的32位系统，（js为了节约性能）使用低位存储变量的类型信息；判断数据类型时，是根据机器码的低位表示进行判断的，而 null 的机器码标识和 对象的机器码标识一样都是000，没有事先做过滤，导致误判了 null 为 'object'

js 中 变量没有类型，只有 值 才有 ！！！

### npm run serve / dev / xxx

### async-validator 的 rules集合中的每个对象的type默认是String类型的，做校验的时候可能会因为 number 和 string 来回切换 导致 校验不到

![图片](https://note.youdao.com/yws/res/2487/WEBRESOURCEc2cf9bc8bfa15d2293d882e2571b4b27)

### 动态类名 - 鼠标点击增加样式（文字颜色改变）

classList.contains('active') 判断是否有该类名 做样式切换的时候

### git commit emoji:

git config core.ignorecase false 配置 vscode 修改文件大小写时，当创建新的文件。不会导致 修改大小写时 远程仓库没更新到 。。。

这是 git 内部就支持的功能，不用额外装什么插件显示对应的图标就可以看到对应的提交图标。就提交的时候在提交信息前面加上对应的 :xxx: 语法就可以了（可以装提交选择图标的插件）

![图片](https://note.youdao.com/yws/res/2537/WEBRESOURCEdf724231f208ac402335b1db5fb0ca41)

:recycle: refactor: 重构

:zap: perf: 优化

:lipstick: style: 样式

:memo: docs: 文档

:art: chore: 配置修改

![图片](https://note.youdao.com/yws/res/2541/WEBRESOURCEbd79fa14262524f5ed0e4d0caf2ee49f)

:sparkles: feat: 新功能/特性

:bug: fix: 修改bug

:bookmark: carry: 不同仓库（项目）相同代码搬运 (自定义的)

### 执行顺序 vue 模板 执行顺序：

render => template => el el ----> $refs.idName.$el （整个Vue实例的 DOM 对象）

![图片](https://note.youdao.com/yws/res/2567/WEBRESOURCE785bcb7887112dd28f1cdbd387daec65)

### websocket 与 后端的通信

### 、与 http 比 ？ 服务端可 主动 推送消息 到客户端 不用像登录扫二维码那样 不断的发送 http请求 轮询 接口，直到有返回数据才进行下一步的操作（页面跳转）

### 、大致使用？

⚪ 首先要装一个依赖包 reconnecting-websocket ws掉线自动重连

使用：

![图片](https://note.youdao.com/yws/res/2569/WEBRESOURCE870dd236c168a6f0147207bacde6c899)

`const ws = new ReconnectingWebSocket('ws://....');`

⚪ 然后初始化一个 ws

```javascript
import ReconnectingWebSocket from 'reconnecting-websocket' initWebsocket(wsUrl, protocols) { const debug = process.env.NODE_ENV === 'development' // ws实例是否打印 debug 信息，默认是 false const options = { connectionTimeout: 1000, // 设置超时重连时间（没连上的时候一秒请求一次连接） maxRetries: 100, // 最多重连次数 debug } return new ReconnectingWebSocket(wsUrl, protocols, options) }
```

⚪ 在主布局页面 进行 ws 的连接

详细代码为：

![图片](https://note.youdao.com/yws/res/2574/WEBRESOURCE0ef9e0a0c11541efcf305fcfb2032f96)

```javascript
import store from 'store' import expirePlugin from 'store/plugins/expire' const storage = store.addPlugin(expirePlugin) websocketConnect() { const url = process.env.VUE_APP_WB_URL || null // ws 的请求 api const token = storage.get(ACCESS_TOKEN) // ...详细代码看下面 }
```

首先会发送一个 websocket 的请求， 请求与服务端建立连接，状态码 101 表示 升级协议

initWebsocket() 方法

连接服务器的ws地址

### 判断字符串是否是yyyy-mm-dd的日期格式

![图片](https://note.youdao.com/yws/res/2572/WEBRESOURCEf84165d73d37cc4dffefa85ecbf36595)

targetLength

当前字符串需要填充到的目标长度。如果这个数值小于当前字符串的长度，则返回当前字符串本身。

padString 可选

填充字符串。如果字符串太长，使填充后的字符串长度超过了目标长度，则只保留最左侧的部分，其他部分会被截断。此参数的默认值为 " "（U+0020）。

```javascript
// 判断字符串是否是yyyy-mm-dd的日期格式 包括日期正确性校验 const judgeDate = (() => { function format(d) { return [ d.getFullYear(), `${(d.getMonth() + 1)}`.padStart(2, "0"), `${(d.getDate())}`.padStart(2, "0"), ].join("-"); } return s => format(new Date(s)) === s; // 返回值是一个函数 而且这里没有其他代码有调用这个箭头函数 正所谓 函数不调用就不执行 而这里用了立即执行函数 所以就执行了 最终的返回值是这个箭头函数的返回值！！！ })()
```

![图片](https://note.youdao.com/yws/res/2583/WEBRESOURCE762ba87d6190f835139419a9a9be4e28)

IEFF 立即执行函数

命名空间 - 闭包环境 ？ （里面可以访问外面，外面不能访问里面，不会造成命名冲突，变量污染）

闭包： 内层的函数可以使用外层函数的所有变量，即使外层函数已经执行完毕

### 扩展运算符解构对象，单独定义相同字段，不影响原对象的该字段。

### 前端利用 jsencript.js 进行 RSA 加密

![图片](https://note.youdao.com/yws/res/2586/WEBRESOURCE914215092b94a2f81d13d9648ef1a8a6)

必须有私钥才能对 对应的公钥进行解密 （有公钥是不可能拿得到私钥的，私钥应该可以获取对应的公钥的）

对称： 通信双方公用同一密钥

非对称： 公钥和私钥，公钥加密、私钥解密

RSA 加密： 非对称加密算法

使用： 需要一对 密钥 ，公钥和私钥 （一般公钥加密，私钥解密）,终端跑命令生成

![图片](https://note.youdao.com/yws/res/2589/WEBRESOURCEfa51c5fe5cc028eebe8e1011d2374ecb)

公钥和密钥： 一组数字，其二进制长度为1024位 / 2048位

使用库进行加解密

import JSEncrypt from 'jsencrypt/bin/jsencrypt' // 密钥对生成 http://web.chacuo.net/netrsakeypair const publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBtToApvTzfpax/8OyYkVPKiCx0AFG5AXyZheW3CnL8LE0gQSKEpxghx8Odw306Ne1uOR5aNRMjaD4V9q5TDWtyiC28MgPALqOBZGVA3ZV7rx0xeuGu5IKJrtIQyICkRaKH4v+CRZnEAwGrBVxzjBPxUXlkC/TLpLFBSrtmrN99wIDAQAB' const privateKey = ''

```javascript
// 加密 export function encrypt(txt) { const encryptor = new JSEncrypt() encryptor.setPublicKey(publicKey) // 设置公钥 return encryptor.encrypt(txt) // 对需要加密的数据进行加密 }
```

```javascript
// 解密 export function decrypt(txt) { const encryptor = new JSEncrypt() encryptor.setPrivateKey(privateKey) return encryptor.decrypt(txt) }
```

![图片](https://note.youdao.com/yws/res/2591/WEBRESOURCE8e8dd2484e904c81d9c3b8d67e7f7dd2)

对登录密码进行加密

window.btoa('字符串') 和 window.atob('bsae64编码字符串') base64编解码 （js原生方法） -- 对应有 js-base64 的库可使用

作用： 简单加密，bsae64编码兼容特殊字符，对应ASCII 字符串

使用：

### 根据不同的终端设备显示不同的页面（非一套布局方案的响应式），是写两套的, PC + 移动端 mobile

![图片](https://note.youdao.com/yws/res/2596/WEBRESOURCE899b1eea02cc729e2e9ecf5be4a6ad00)

### 首先，根据 navigator.userAgent 判断 当前终端是 PC还是移动端

### 然后在打包后的dist文件夹中的 index.html 中进行判断，引入不同的静态文件

vue打包过后生成dist文件,对比两套生成的dist文件，将共同的css，js写在一起，不同的通过

document.write 写到文档中，当然也可以用响应式的布局，但是响应式的布局加载慢，写法麻烦后期难以维护，小页面还好大的项目的不同点太多了，几乎跟写两套没什么区别

```javascript
function IsPC() { const userAgentInfo = navigator.userAgent; const Agents = ["Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod"]; let flag = true; for (let v = 0; v < Agents.length; v++) { if (userAgentInfo.indexOf(Agents[v]) > 0) { flag = false; break; } } return flag; } const flag = IsPC(); //true为PC端，false为手机端 if (flag) { //pc端引入的link document.write(' <link href="css/app.fa43afb7.css" rel="preload" as="style"> <link href="js/app.ae6f97da.js" rel="preload" as="script"><link href="css/app.fa43afb7.css" rel="stylesheet">') } else { document.write('<link href="css/app.4646bd09.css" rel="preload" as="style"> <link href="js/app.0b91c116.js" rel="preload" as="script"> <link href="css/app.4646bd09.css" rel="stylesheet">') }
```

![图片](https://note.youdao.com/yws/res/2608/WEBRESOURCE147f4470317deb9ad67f43f05f313e59)

**引入js ，在这里要注意一个点，当写的是双标签的时候在结束标签的前面要加个转译字符**

'\' 不然无效

### 计算对象的层次 （递归实现）

/

### lass 、 pass 、 sass

![图片](https://note.youdao.com/yws/res/2606/WEBRESOURCE17316b3aba6945365b89be02ecd97ca2)

lass 底层应用 cpu / 网络 / 内存 等计算资源

pass 中间件 开发语言 和 开发环境

sass 软件即服务 直接使用开发好的应用软件，只注重运营。

### git flow 工作流

为了更好的管理项目，针对版本发布周期进行的一套代码管理，把控项目，降低项目上线的风险和提高可操控度。

![图片](https://note.youdao.com/yws/res/3922/WEBRESOURCEec43b3779fbab701e140cc47bde9da88)

有一个命令行工具 git-flow，是对 git 命令的封装，简化各分支的繁琐操作。自动将release合到master并打tag和dev，并在本地和远程都删除release，再切换到dev分支。

五个分支：

### master 主分支 运行 / 演示 系统（生产环境）的分支，是确保代码没有Bug时（测试通过的）才可以合并到此分支上

### develop 开发分支 测试环境的分支，主要用于日常的需求开发提交代码的分支，代码未经测试员测试，可能存在bug，及不稳定 基于master创建develop

### feature 新特性 包括小版本迭代（2.1.x），一个版本迭代可以开一个feature 分支，基于 develop 分支 创建 feature，新特性开发完成会合并到 develop 分支，但是不会跟 master 打交道

![图片](https://note.youdao.com/yws/res/3924/WEBRESOURCEcae984d3a00fe61e7cb098c7eb216450)

### release 发布分支 开发周期时间到了/ 完成了指定的版本任务开发 ，从 develop 创建 release 分支， 用于代码发布工作。除了 相关的 bug fix 之外，不允许在该分支增加其他功能的代码。最终会合到master分支，顺便会打标签（tag），备注对应的版本号在master上

### hotfix 专门用于打补丁，修改bug。 从master创建hotfix，待 bug 修改完成后，可以合到其他的分支上，主要是为了不影响其他分支的正常工作，同时又能协同进行 生产环境中（master分支）bug 的修复工作

### 文字左右边对齐： text-align: justify;

### CORB 警告

CORB: Cross-Origin Read Blocking 跨域读阻塞

![图片](https://note.youdao.com/yws/res/3936/WEBRESOURCE47af4b20dc08ccfdba0f96147049c3fa)

为哈会有： 防止网络安全漏洞，出现的站点隔离（corb 实现策略之一）

什么时候会有：

### rem （font size of the root element） 和 自适应百分比 追求的是 比例 一致

fontsize大小自适应 + font-size大小固定

1rem = 根元素（html）的字体大小 （大部分情况下 1rem = 100px） （移动端下是 1rem = 16px）

![图片](https://note.youdao.com/yws/res/3916/WEBRESOURCE54c31cb7196a461343e39df55d02e0f9)

设置 1rem = 100px，主要是：

(1)、方便计算（设置 100px，相当于 100%， 把 1 分成 100 分，把100当成一个系数而已， 好计算啊）

(2)、扩大基准值，适配部分限制最小字体的浏览器

**（chrome最小字体12px，华为个别型号浏览器最小字体8px,如果设置为1rem=10px,在12px最小浏览器中会被强制转换为12px，会导致基准不对比例计算出现问题）**

(3)、常见分辨率下（375px / 700px / 320px ....）的 1rem 都是设置为 100px

![图片](https://note.youdao.com/yws/res/3934/WEBRESOURCE21d0c9002542fc6c317e322d26227f56)

但是更多时候， html 的 font-size 是动态计算的呀！！！！ （为了不同设备上显示的比例一致）

<HTML>元素 的 font-size 计算公式为： ( 用户设备宽度 / 设计稿标准宽度 ) * 100

设备像素比 device pixel ratio （DPR） = 物理像素 (设备像素) / 逻辑像素 (css像素)

### 简历 -- 包装 -- 获得面试机会

### window.open / location.href 会自动拼接上域名url，只需要拼上路径 '/path' 就行

![图片](https://note.youdao.com/yws/res/2617/WEBRESOURCE91e86819b68720e5ab84f2e6d12fdc6c)

document.domain 获取 域名（不带端口）

location.host 获取主机名（域名）（带端口号，如果有端口号） / location.origin 获取完整的URL（带协议 比如： http://）

（1）、因为会自动拼接，所以要加上 ' // '，不然会拼在自己的域名下。

（2）、还有一种方法就是使用 路由的 resolve 方法，进行跳转。就不用判断哪个环境了。

const openUrl = this.$router.resolve(name: 'xxx', query / params).href

![图片](https://note.youdao.com/yws/res/2619/WEBRESOURCE74ec6775e088bd3313fbc983dd6121f5)

window.open(openUrl, '_blank')

其实直接 / （根路径）也是可以的，主要看你拼的参数能不能跳过去！

### 关于路由 一些比较悬的东西

### 技术的出现都是为了解决问题的、而不是单纯地了解一堆API的

掌握一门技术，要知道其局限性/历史 和 本质

![图片](https://note.youdao.com/yws/res/2674/WEBRESOURCEe699a1dbd58fb6e68a99750077927361)

学习也是单纯地为了解决问题的，不是为了去记忆一堆API的。

软件： 计算机数据 + 指令

大前端： 移动端 （unpapp） 和 web端 （网站 / 后台管理系统 / 手机H5） 小程序端 桌面端 服务器开发

### vuex-persistedstate Vuex的持久化插件（将本来存在state的数据映射到本地存储中，既做到了刷新不丢失又能保证数据还是响应式的）

### js 完全搞不懂系列： = =

![图片](https://note.youdao.com/yws/res/2676/WEBRESOURCEf87dd26e10e2540bf13e494f1e82c42c)

{} + '' 结果是转数字 -----> 0

### 循环 push ，如果把 字典 dict 写在 循环外面, 辉出现相同key 覆盖的问题, 解决方案是写在循环里面，确保每次都重新初始化，保证数据不给覆盖

分析原因：

可以发现每次 for 循环添加到字典中，都会覆盖掉上次添加的数据，并且内存地址都是相同的，所以就会影响到列表中已经存入的字典。

因为字典的增加方式dict['aaa] = bbb,这种形式如果字典里有对应的key就会覆盖掉，没有key就会添加到字典里。

![图片](https://note.youdao.com/yws/res/2678/WEBRESOURCE79e1205e0693eaf5f512a44b22e9c8c1)

### 关于表单校验的动态绑定校验值 动态绑定 prop 值

关键点： template循环该数组（绑定在form中的） + prop取值（ 'prices.' + index + '.title' 组件识别这种写法 ）+ v-model 帮值 三者缺一不可

主要就是 循环 绑定 model 中的 form 的数组 + 自定义prop + 自定义rules + v-if 判断 其他的rules那个字段对应的校验函数 一样的写法的

### vue路由跳转的方式

（1）、 router-link 标签 （a标签跳转会重新渲染即刷新页面， router-link跟 this.$router.push 那几个方法就不会，会守卫点击事件，让浏览器不再重新加载页面 只会更新不一样的地方。 所以使用了keep-alive的话就不要用a标签了，会重新刷新页面，使缓存失效的） router-link 是 只会更新变化的部分从而减少DOM性能消耗

![图片](https://note.youdao.com/yws/res/2690/WEBRESOURCE66697821e3fdeef922c0cda21af7bd1c)

a标签的rel="noopener"属性 即 no opener 的意思，就是在同时设置了target="_blank"时打开的新页面中的window.opener值为null，安全起见，获取不到原有的window对象

（2）、this.$router.push / replace / go /back /forward / 方法

router 的两种传参方式 ：

query: { id } 问号拼接

params: { id } 斜杠拼接

![图片](https://note.youdao.com/yws/res/2694/WEBRESOURCEfc74d76d5f02dd2e86fa7290b37f34e9)

this.$router.push('/biz/production/edit' + id) params 拼在 url 上 （刷新页面有）

等价于

`this.$router.push({ params 在 body 上 （刷新页面无）`

name: 'productionEdit',

params: { id }

![图片](https://note.youdao.com/yws/res/4854/WEBRESOURCE90df1633caf2cb114d7cd4f98df35d79)

})

### keep-alive 的 使用：

跳转的时候如果都是用的vue-router提供的方法（router-view或者$router.push等），那么这个keep-alive会自动帮你缓存，就你请求过的再次请求就不会再请求接口了，除非你手动再刷新页面。

浏览器的前进后退功能也是一样的不会刷新页面的，

那如果你是点击切换请求接口的话，那keep-alive就做不了了。每次都重新请求接口，就不会帮你缓存了 。

![图片](https://note.youdao.com/yws/res/4856/WEBRESOURCEb89338add92530fae6ec60107680ec1a)

### 算法 计算/解决问题的步骤。

线性结构（数据对象一对一）： 数组、链表、栈、队列

非线性结构： 二维/多维数组、 广义表、树结构、图结构

### 控制台获取element元素的dom对象

dom元素和组件实例的区别：

![图片](https://note.youdao.com/yws/res/2834/WEBRESOURCE4c434688f0216316891d78cc2ab199f8)

dom元素可以当做ref得到的实例对象中的$el属性

控制台获取element元素的dom实例

vue组件可以分3种

根组件： 最顶层 id="#app"

子组件： 根组件下面的组件（可以多层嵌套）

![图片](https://note.youdao.com/yws/res/2843/WEBRESOURCE01bd84e3af61ff7ba8df04eaa6596d24)

琉璃组件： 挂载的 全局 $xxx 属性

一个组件就是一个实例

### 项目难点：

（1）、公用组件的抽取（抽象功能，进行组件的封装 插槽 / prors / emit ），避免很多重复冗余的代码，同时也提高了开发效率。

（2）、框架的自定义配置 （递归显示菜单 menus 模块）

![图片](https://note.youdao.com/yws/res/2848/WEBRESOURCE42448d19a41384af6ca14516f1e4b3d0)

（3）、系统管理模块

（会员中心）用户（账号）管理 --- （管理中心/管理后台）人员（管理员） 管理

角色（权限）管理

资源（菜单）管理

（4）超时退出重新登录回到原先操作的页面

![图片](https://note.youdao.com/yws/res/2850/WEBRESOURCEb7b8f22da2459d80af7a9026a3d8a035)

（ 点击确认退出按钮的时候 disptch 到 logout 接口，然后成功后的.then中 this.$router.push('/login?redirect_uri=' + this.$route.fullPath) 保存当前操作页面路由，

然后在登录页 this.$route.query 获取到 redirect_uri 直接 this.$router.push 过去就可以了，然后没有 redirect_uri 就默认跳到主页 ）

### 资源显示当前账号下拥有的所有资源（菜单）列表，超级管理员默认拥有显示系统全部菜单的权限（可以操作菜单，排序，显示隐藏，更换菜单图标/菜单排序，是否缓存，组件路径等...）

### 角色管理可以为系统创建角色（给用户分配角色），分配角色菜单

### 账号管理是登录系统用的账号名，可以新增 / 修改密码，分配角色，是否启用该账号，删除账号 等

![图片](https://note.youdao.com/yws/res/2827/WEBRESOURCE209132ad863154e42ac58bd754485f97)

### input 的 type 为 number 时，设置的 maxlength 的写法：

<input type="number" oninput="if(value.length>5)value=value.slice(0,5)" />

### ssh （secure shell）安全外壳 ，。 连接远程服务器用的一种 网络安全协议。 默认端口号是22

### eslint 代码 质量 / 风格 检测工具 linting 检测？ （eslint 代码规范？ ts 类型规范？） （更强调 逻辑/命名 ？） 语法/变量/错误等 JavaScript代码检查工具

prettier 代码风格格式化工具 （更强调 缩进/格式化代码 ？） 缩进/换行/分号/引号 代码格式化工具

![图片](https://note.youdao.com/yws/res/2892/WEBRESOURCE8ac5ec260c6f757504a028f3c4fa1ca9)

lint-staged 限制代码提交规范（提交 （git add） 的时候自动跑 lint 命令）

这样配置就可以了

### QQ浏览器首页的视差滚动效果： background-attachment: fixed 和 js控制 background-position-y 产生位移

### 判断对象是否为空 ( 1. Object.keys().length 2. JSON.stringify({}) === '{}' 3. for ... in 判断 4. Object.getOwnPropertyNames({}).length === 0 )

### 请求接口的方法： form表单（action属性）提交（会引起页面跳转） 、 XMLHttpRequest、 Ajax、Axios

![图片](https://note.youdao.com/yws/res/2937/WEBRESOURCE88d1ef18f3044088dfd71d3ee29a50f2)

### 代码要每天都提交一次！不要按功能完成再提交，确保 服务器能备份到最新的代码，确保本地不会因为硬盘坏了而丢失代码（即使是万分之一的概率）。

提交的时候只要确保基本的编译不会出错就可以了，只是提交到你的 feature 分支，不会影响到 master 分支的完整性的。 到时候一个功能开发完成了再通过测试后

再合并到主分支就可以了。

参考 devOps 的操作流程。

CI / CD 持续集成 / 持续部署（交付） continuous integration

![图片](https://note.youdao.com/yws/res/2941/WEBRESOURCE50b8d169a5404811a36735ebb9c0d107)

continuous deployment（delivery）

### 命名： 数组： menus menuList Nodes records

对象： data menuInfo menuDict

Map: menuMap

Set: menuSet

![图片](https://note.youdao.com/yws/res/4215/WEBRESOURCEbcc42d019f94827f722702518f784870)

### 用 img标签的 onerror（@error）事件 解决图片的src地址访问不到报错（或者图片裂开）的问题

原生：

VUE:

### 可拖拽的视口

cursor: col-resize;

![图片](https://note.youdao.com/yws/res/4218/WEBRESOURCE19aaadbfd01c829800d5b70f3939e752)

### 获取 外部样式 的 方法

window.getComputedStyle( ele ) 获取该元素的所有外部样式对象 （有width属性 ......） // 返回值是 整数

ele.getBoundingClientRect( ).top / bottom / left / right 返回一个矩形对象，包含那4个属性 // 返回值是 精确 的 带小数点的

ele.offsetWidth / offsetHeight / offsetLeft / offsetTop // 返回的是 整数

### package.json 文件中的 dependencies（项目的依赖） 和 devDependencies（开发所需要的模块） 和 peerDependencies 的区别：

![图片](https://note.youdao.com/yws/res/6927/WEBRESOURCE6e39d255561af4257e224acb23dbeef6)

（1）、如果没有发布 npm 包， 那么依赖放在哪里没有区别；

（2）、为了规范，如果是一直使用的包（项目中会一直用，如 ant-design-vue、day.js等），放到 dependencies 中; （这里的依赖是一定会被下载的）

如果是开发时需要用到，上线后（线上环境）不会用到的，如 webpack、eslint、prettier、各种-loader、stylelint等...，放到 devDependencies 中

（3）、peerDependencies 解决核心库被下载多次，统一核心库的版本问题 （项目依赖了 vuex 和 vant 这两个子库，而这两个依赖又都同时依赖了 vue 这个框架。在字库中分别声明 peerDependencies 防止重复安装 vue）

（4）、peerDependenciesMeta 对 peerDependencies 的修饰， 增加一些可选项的配置 。

![图片](https://note.youdao.com/yws/res/6936/WEBRESOURCE2a46c366b4f547cd59f988a3a2a60e11)

### 关于 css 字体 的一些学问 font-weight 属性在不同操作系统（常见win和mac）上的显示效果的不同

（0）、关于 win 和 mac 的默认字体 （不设置font-family属性，会自动读取操作系统的默认字体来显示的）

（1）、win上 ：600为分界线。600往前一个效果，600往后一个效果

mac上：每一个值显示的效果都不一样

（2）、因为操作系统和浏览器的原因，字体在不同操作系统设备上显示的粗细会有所不同。这是 font-weight 这个属性存在的兼容性问题

![图片](https://note.youdao.com/yws/res/6945/WEBRESOURCE15900b2316e2eafb93b331176746666b)

（3）、使用 @font-face 引入自定义字体，font-family 使用该字体名称 （字体后缀名 .ttf .otf .fnt ）

### vue 中 多个组件使用 window.onresize，只有一个生效，导致其他的给覆盖。

### js引擎（spiderMonkey、V8 ...） （js解析器） 和 js编译器 （babel （es6 转 es5，转化 jsx 语法，兼容旧版浏览器，ts语法）、 tsc （typescript compiler）、 swc （speedy web compiler））

### commonJS （服务端 node） 和 ES Module 的区别：

(1)、require 和 import 的

![图片](https://note.youdao.com/yws/res/2977/WEBRESOURCE09b3531d7f298831c04094faca2410c7)

(2)、CommonJS 的思路就是将函数当作模块

### JS的对象遍历是无需的（因为对象本身就没有存储顺序）。但是数组是有的，有index索引，记录每个元素的顺序。

### JS（V8）垃圾回收机制： GC -----> Garbage Collecton 垃圾回收 垃圾收集器 （自动回收机制） （C / C++ 就没有）

标识无用变量方法（GC 跟踪每个变量是否有用，无用变量打上标记，回收其占用内存）： 标记清除 （V8） 和 引用计数

标记清除会导致内存空间分配不连续（内存碎片）、分配速度慢

![图片](https://note.youdao.com/yws/res/6951/WEBRESOURCE659ef1618a5fadb013ea9f170dcc59b6)

标记整理：

引用计数（ 跟踪记录每个值被引用的次数。

）

上面 即 循环引用的 例子

回收

![图片](https://note.youdao.com/yws/res/9516/WEBRESOURCE5d2e227f97a8f57356304474adfe0c37)

### vue 动态绑定属性名

### vue 中 v-for 支持的类型 （比原生的 for 屌很多的） in / of 这两个都可以，作用完全一样（跟原生是不一样的）

### 遍历 数组（字符串）

### 遍历对象 value - key - index

（键值对索引）

![图片](https://note.youdao.com/yws/res/9519/WEBRESOURCE52ea2ec0339cde69faaad0332b84290b)

### 遍历数字

### 遍历 itarable（可迭代）对象

key 属性 ）

Vue3.x 新增 自动生成唯一的 key 值

Vue2.x 必须写key

![图片](https://note.youdao.com/yws/res/6956/WEBRESOURCEaca07be0f0eecedc162050442390c636)

VNode 虚拟节点

template中的每一个标签元素 vue 都会转化成 一个个的 VNode

template -> VNode -> 真实DOM

虚拟DOM ？ 可以干嘛？

（1）、做 diff 算法 （绑定 v-for 的 key）

![图片](https://note.youdao.com/yws/res/6965/WEBRESOURCE81b87925692c5d453b79e12a7a3b77be)

（2）、跨平台。vue 可以写 PC Web / 移动端 / H5 / 小程序 / 甚至 桌面端 / 安卓 / IOS ...... 靠的是 VDOM 的 转换

### position: fixed; 一般都是相对于 屏幕视窗 （浏览器窗口） 来进行 定位 。（屏幕尺寸大小）

所以 设置 width 要动态计算 calc（）函数 除非

### Vue 组件库 相关

（1）、直接用

![图片](https://note.youdao.com/yws/res/2993/WEBRESOURCEd8680defdbf91470d0806a950138d299)

main.js 文件 // 组件库 import Element from 'element-ui' Vue.use(Element) // 使用组件库的所有组件（全部加载，不管用没用到）

（2）、lazy-use 按需引入

main.js 文件 // 组件库 import './lazy_use' // use lazy load components lazy_use.js 文件 import Vue from 'vue' import { Input, List, Dropdown, Tooltip ... } from 'ant-design-vue' // 按需引入 Vue.use(Input) Vue.use(List) Vue.use(Dropdown) Vue.use(Tooltip) ...

如果仅仅是完成上面的操作，还是不能实现真正的按需加载的。

需要下载 babel-plugin-component 插件 并且 在 babel.config.js 文件中进行对应的插件配置（按需引入的组件库和按需引入的样式）

![图片](https://note.youdao.com/yws/res/3183/WEBRESOURCE45d27350038e18f509b7beb309058635)

以上，就可以做到按需引入了。

重写组件库样式：

### 建element-ui.scss文件在style文件夹中，在index.scss中@import引入，然后在 App.vue 文件的 style 中 @import 引入

### 建global.less文件重写 antdv 样式，在 main.js 中直接 import 该文件

这样就可以 了。

![图片](https://note.youdao.com/yws/res/3185/WEBRESOURCE891bf3e4c0e56ecf06f1c81eadf2f2a8)

element-ui 的 layout 布局 中的 响应式布局

<el-col :lg="{span:'12-25'}"></el-col>

.el-col-lg-12-25 { width: 48%; }

```javascript
:lg="{span:'12-25'}" 和 .el-col-lg-12-25 =======> 即 el-col-lg-12-25 设置的 width 就是 :lg="{span:'12-25'}" 对应的宽度 （12-25 就是从第12列到第25列 的意思 ）
```

### property 和 attribute 的 区别 （类似 $attr 和 $props）

![图片](https://note.youdao.com/yws/res/3189/WEBRESOURCEedd5a01f459fea86a561f1339f9afef8)

### property（如 style,className,childNodes,classList ...）

（1）、 指的是操作 DOM 的属性 像获取到DOM后设置 xxx.style.color、xxx.className、xxx.childNodes、xxx.firstChild .......

（2）、值只能是字符串（操作JS）

（3）、长属性名是驼峰写法

### attribute（如 id,class,src,title,alt ...）

![图片](https://note.youdao.com/yws/res/3196/WEBRESOURCEbe0aa93c53150f8db8cf3dd92adf13b9)

（1）、 指的是操作 HTML 的属性 像设置和获取DOM元素的属性 xxx.getAttribute('class')、xxx.setAttribute('title', 'cusTitle')

（2）、值可以是 数组/ 对象等（HTML）

（3）、属性名

e.g.

$listeners （vue3 deprecated）

![图片](https://note.youdao.com/yws/res/3193/WEBRESOURCEf22b74ace541c11a7e11c4945eece520)

`v-bind="$attrs" v-on="$listeners" $options ===> options API 整个 获取自定义属性`

事件监听的具体使用：

父组件：

子组件：

这里的v-on="$listeners" 就相当于 继承了 父组件中的除了 @run 之外的所有事件监听了（也就是红框框出来的那些事件）

![图片](https://note.youdao.com/yws/res/3198/WEBRESOURCE0809c552b6806778a327c77e8769de45)

也相当于：

替代了 手动写$emit 事件 。

所以，这个 @run 在 父组件定义就行了。 在子组件这里再次定义的话就相当于 你执行了 两次 run 了。

### Vue 的 install

### flex （一维 / 单行） grid （ 二(多)维 / 多行 ）

![图片](https://note.youdao.com/yws/res/7236/WEBRESOURCE283cac609391275f703f999864cb7007)

让最后一行列表左对齐

(1)、grid

display: grid; justify-content: space-around; grid-template-columns: repeat(auto-fill, 30%);

// 子元素 width: 30%; height: 100px; background: pink; margin: 12px;

(1)、flex

![图片](https://note.youdao.com/yws/res/7241/WEBRESOURCE4e6caa05e1995343651e4b7795662ba2)

1. 粗暴点，直接给最后一个 margin-right

2. 在后面套两个 <div></div><div></div> (空元素占位)

### JSON Schema

JSON : JavaScript Object Notation js对象（万物皆对象）注释

JSON 是一种语法，用来序列化对象、数组、数值、字符串、布尔值和 null 。key 和 value 都是 字符串

![图片](https://note.youdao.com/yws/res/7266/WEBRESOURCEa7b6f8b0f924c3d104f20ecb998bfd51)

JSON Schema 是一套 JSON规范，用于校验JSON的工具。(格式有点类似于AST？) 就是一份JSON文件的说明文档，叼一点，可以生成form表单（插件支持）和做数据（字段）校验

包含了「表单数据描述」和 「表单校验」功能。

基本格式：

json格式是不支持注释的，所以可以用 json schema 来定义json，在json文件里加个description字段就可以了。title是定义标题的。

field widget 组件

![图片](https://note.youdao.com/yws/res/7271/WEBRESOURCE606fdb527a7568f1328cf80649241925)

语法： 定义了一系列的规则（关键字） ajv: JSON Schema 的校验器

组成：

### schema： 用于描述表单数据的 JSON Schema

### ui-schema： （ui 也可直接配置在 schema 中） 用于配置表单展示样式

### error-schema ：用于配置表单校验错误文案信息

![图片](https://note.youdao.com/yws/res/7269/WEBRESOURCEec12a79c0e85b51dd8d103b0b5e31997)

.......

关键字：

### $schema 关键字：声明了针对哪个版本的JSON Schema标准编写模式。

### $id 属性：为JSON Schema实例声明一个唯一标识符；声明了一个解析$ref 的URI时的基础URI

### $ref 属性：引用同一个JSON Schema文件或其他JSON Schema文件中的JSON Schema实例片段。这个属性的值通常是一个相对或者绝对URI，# 符号后面的部分是JSON指针

![图片](https://note.youdao.com/yws/res/3252/WEBRESOURCE71e4be1dc807693feb34d8e8f756f1e1)

### dependencies 关键字：属性依赖：如果JSON实例拥有对应的属性名name，则也必须有name对应的propertyset数组内的所有属性名

### definitions 关键字：这个关键字可以在JSON Schema实例中定义一个可以在这个JSON Schema实例中被引用的JSON子模式。

### 同一个盒子同时使用 position 和 flex 会产生 冲突 （flex居中 和 子元素 position: absolute 之间产生的冲突）， 给盒子外面再套一层 div，相对 div 定位

### 声明式编程 （函数式编程 是 其中一种） 与之对应的是 命令式编程

### React

![图片](https://note.youdao.com/yws/res/3273/WEBRESOURCE5324474a591b485b88b6dc9a6a27b558)

（1）、开发 web 页面

（2）、React Native 开发 移动端跨平台

（3）、React VR 开发 虚拟现实web应用

依赖包： react / react-dom（ 渲染到不同平台的依赖，比如 react web 和 react native ） / babel

vue只要数据更新,会自动进行render,视图上显示的是最新的数据。

![图片](https://note.youdao.com/yws/res/3275/WEBRESOURCE603d9f4679b37d2a63b347633c4fd92e)

而react默认不会自动执行render (可以是 html元素 或 组件--------类组件和函数式组件)的，要在数据更行后手动执行一下render拿到最新数据

script标签中加 type="text/babel" 为了能让 babel 解析 jsx 代码

函数的返回值默认是 undefined （没写 return 的话）

es6的 class 默认是 绑定的严格模式下的，所以 class 里的 this 如果原本是指向 window 的，会变成 undefined （使用 babel 转换的代码也是，默认都是开启严格模式）

### 禁止移动端的触摸事件（touch event），可以强制不显示浏览器的滚动条（比如 safari 会自动显示滚动条）

![图片](https://note.youdao.com/yws/res/3269/WEBRESOURCEef2eedb94801ce5f43ece1b31d55708e)

touch-event: none;

### VUE_BASE_API 在 .env等 配置文件 中 ：

（1）、完整的 url ： 那就是 base_url + api文件 中的 request_url

本地（开发）环境 和 测试环境 都是 这个的话，那访问的 api 就是

生产环境 的 api

![图片](https://note.youdao.com/yws/res/3277/WEBRESOURCE69b1932c7c4c018b2f63aff6f8196698)

（2）、单纯写 /api 这个是在 nginx.config 配置文件中进行 配置的。 （nginx是部署在服务器上面的，不是在后端代码中的。nginx 服务 配置项目）

### 如果是在 开发环境 中，那么访问的 baseUrl 就是 http://172.21.44.15:8080 + requestUrl 就是 /api/user/search

（如果没在vue.config.js文件中的devServer的proxy中设置 ‘/api’ 代理的话，那就会报 404 not found 错误。要改VUE_BASE_API的）

### 如果是在 测试环境 中，那么访问的 baseUrl 就是 http://api-test.xd0760.com + requestUrl 就是 /api/user/search

### 如果是在 生产（正式）环境 中，那么访问的 baseUrl 就是 https://api.xd0760.com + requestUrl 就是 /api/user/search

![图片](https://note.youdao.com/yws/res/3417/WEBRESOURCE8de08427b1e62ee2436fe4eb88f68057)

**同理，VUE_BASE_API_UPLOAD 也一样，设置 不同环境下的服务器存储文件（图片、视频等）的地址，如果是配置的 /uploads 的话，要注意对应环境，如果是测试环境 就会报错了**

### 随机生成颜色

`const createRandomColor = () => `#${Math.random().toString(16).slice(-6)}``

### 原生方法 监听 本地存储 （localStorage）的变化：

`window.addEventListener('storage', (event) => {`

![图片](https://note.youdao.com/yws/res/3279/WEBRESOURCE77b06deff958682195710ad3ab23b61a)

console.log('storage changed', event)

})

var orignalSetItem = localStorage.setItem

```javascript
localStorage.setItem = function(key,newValue) { var setItemEvent = new Event("setItemEvent") setItemEvent.newValue = newValue window.dispatchEvent(setItemEvent) orignalSetItem.apply(this,arguments) }
```

```javascript
window.addEventListener("setItemEvent", function (e) { alert(e.newValue) }) localStorage.setItem("name","wang")
```

![图片](https://note.youdao.com/yws/res/7174/WEBRESOURCEa3c7f68db9d41b354757c33eef0a140c)

### 修改 node_modules 上的代码： 用 patch-package （依赖 git diff 实现的 patches 文件夹） 呀

**注意: 要改动的包在 package.json 中必须声明确定的版本，不能有~或者^的前缀。**

（1）、安装patch-package

npm i patch-package --save-dev

（2）、修改完依赖后，运行patch-package创建patch文件 npx 执行 node_modules 文件夹 下面的 .bin 可执行文件

![图片](https://note.youdao.com/yws/res/3287/WEBRESOURCEa39d31b7294431be913fab764997bb44)

npx patch-package 依赖名称 例子： npx patch-package element-ui

（3）、修改package.json的内容，在scripts中加入"postinstall": "patch-package"，这个是为了npm install的时候自动为依赖包打上我们改过的依赖

"scripts": { ... "postinstall": "patch-package" }

### vue 的 各版本 对比：

### 编程英语：

![图片](https://note.youdao.com/yws/res/3308/WEBRESOURCE14b08d4342aef39699707188661b798d)

pseudo-element： 伪元素

locale： 特定语言环境的（当地的） 'zh-CN' 'en-US' （'ar-EG' 阿拉伯语言） toLocaleString() toLocaleLowerCase()

SDK： software development kit 软件开发工具包

accessibility： 无障碍 （a11y） ----视 / 听 / 行动 / 理解

patch： 打补丁

![图片](https://note.youdao.com/yws/res/4720/WEBRESOURCEb6aaaee3b377e4dca7652085bf61fb59)

bundler： 打包（工具）（webpack / browserify）

chunk： 块

bundle： 束

vendor： 供应商（第三方库）

我们直接写出来的是 module，webpack 处理时是 chunk，最后生成浏览器可以直接运行的 bundle。（不是多少个 chunk 就会对应生成多少个 bundle ， （虽然大部分情况下会是一一对应） ）

![图片](https://note.youdao.com/yws/res/3459/WEBRESOURCEc0a27ebc8788338a833c8422af18a25e)

invoke： 调用 （函数调用等）

revoke： 撤销

implicit： 隐式的...

explicit： 显式的...

hoisting： 变量提升 (var关键字声明才会有)

![图片](https://note.youdao.com/yws/res/3489/WEBRESOURCEb86a09ce331bcb5c34d099b3b5be89c0)

indicate： 表明，显示

anonymous： 匿名的...

nested： 嵌套的..

polygon： 多边形

immutable： 不可变的 （immer 一直如此 immer.js）

![图片](https://note.youdao.com/yws/res/3495/WEBRESOURCEa0ef518f41d8349894da3b1b9abd4d95)

coupon： 优惠券

trie：字典树

traverse： 遍历

linked list： 链表

expanse ： 花费 （消耗 expansive 贵的）

![图片](https://note.youdao.com/yws/res/3520/WEBRESOURCEeddf23b2e9495bd2ebdeb0b21c085235)

manifest： 清单文件（）

fallback： 兜底

estimate： 估计放

diagnostics： 诊断

concurrent： 并发

![图片](https://note.youdao.com/yws/res/3529/WEBRESOURCEce42af410d0ab15e246043eb5f91e7c8)

shims： 垫片 shims.vue.ts

Polyfill： 填充器

关于符号：（prettie）

semi： 末尾加不加分号 （“false” "true"）一行的结束

semicolon： 分号

![图片](https://note.youdao.com/yws/res/3527/WEBRESOURCE76914c377f01d4af189b17a19f821054)

**semi-spacing： 强制分号间隔；分号前后是否空格**

brackets： 括号

comma： 逗号

trailingComma：多行输入尾部加不加逗号

单引号： singleQoute

![图片](https://note.youdao.com/yws/res/3537/WEBRESOURCE4e8352dbaf8bb661a7ed8b4a8825094b)

双引号： doubleQoute

wrap： 换行 （nowrap --- 一行显示 white-space 只有一行文字的时候） wrapper： 包装器

### 浏览器使用 export / import 语法： 异步方法

默认是在 webpack/cli、 babel 等 脚手架/编译 工具 中有集成 / 做了代码转换

（1）、使用 type="module" （静态引入）

![图片](https://note.youdao.com/yws/res/3539/WEBRESOURCEbecfc3fdc1e03af8c74432b71b8fc062)

（2）、动态引入

（3）、解构（async / await）

### 前端模块化 （js 模块化规范） ---- UMD / CMD / AMD / ES Module （esm ） / CommomJS（cjs）

## 一、 CommonJS

用 module.exports 导出模块, 用 require 加载模块

![图片](https://note.youdao.com/yws/res/3542/WEBRESOURCE24a39593e0ba08f29c53c853bea65b67)

CSS 模块化（防止 文件命名/样式 冲突）：

命名规范（BEM[ block__element--modifier ]、OOCSS [ 原子类 类似 tailwind 思想 ]、SMACSS、ITCSS...）、

CSS Modules（主要 react 在用）、

为 <style> 区块添加 module 属性即可开启 CSS Modules。

CSS-in-JS （styled-components react在用，相当于 vue 的 style scoped） 【 html-in-js： JSX 】 "all in js"

![图片](https://note.youdao.com/yws/res/3546/WEBRESOURCE88f0e58a4742b25b352a82d80d68be96)

emotion 排名第二的维护者 Sam 所在公司弃用了 css-in-js 方案，使用css-modules的解决方案，主要是出于性能考虑。还有增加了包体积

### Div+contenteditable 实现 可插入自定义内容（标签） 的输入框 （简单的富文本输入框）

### jenkins 是 java开发，用于自动化构建（打包）和部署项目 --- 配置命令，自动跑

docker 是 go开发，是个虚拟机，是容器

### 第三方统一登录SSO

![图片](https://note.youdao.com/yws/res/4961/WEBRESOURCEb4f3f41f740cda6a64bb460890bcf157)

带参数跳转回调会自己项目中。

### chrome devtools chrome devtools 开发者文档 实用小技巧：

// $_ (获取控制台上一次的结果)

// $ 1-4 (获取 $0 的 previous 元素)

```javascript
// $(' 类名/id/属性名 ') $('.main') === document.querySelector('.main') $ 就是 document.querySelector() 的别名（语法糖）
```

![图片](https://note.youdao.com/yws/res/4956/WEBRESOURCE1c324ff291a90f1ff8211e8ea17c4018)

// :has() :contains()

// table / dir / clear() 代替 console.table / dir console.clear()

// 监听事件 ？ 像 vue 一样 在方法中打印 当前元素的DOM对象事件 (e) / ($event) ? 使用 monitorEvents 这个 api 啊

// monitor 相当于 vue 中的 watch 了 ，对函数做监听 ？？ 取消就 unmonitor 了

// :has() 选择器 和 :contains() 选择器

![图片](https://note.youdao.com/yws/res/8671/WEBRESOURCEc568a6c613bc443b0fb9f723a88ee3d3)

1. 选中并删除所有带“关键字”的评论 $('.reply-wrap:contains(关键字)').remove()

2. 选中并删除所有用户等级在三级以上的评论 $('.reply-wrap:has(.l3),.reply-wrap:has(.l4),.replay-wrap:has(.l5),reply-wrap:has(.l6)').remove()

### hash 和 history 区别 （浏览器的 /# 重定向 问题）

（1）、如果是 带 #/ 的话，要编码一下的， 用 encodeURIComponent 转一下，再传过去，不然会报错。（可能接口做了处理，特殊字符（#、等）传过去可能不被正常接收 / 浏览器处理？）

history 可以配置 nginx 文件

![图片](https://note.youdao.com/yws/res/7411/WEBRESOURCE5a2f2af5e6943d6485b8bc73c5552d7a)

（2）、加相对路径：

不然会这样

（3）、部署：

### 地图定位（ ip获取范围（省市区），再用 region城市 限制范围 调用获取搜索关键字提示 达到效果 ）

### vue-cli 改造 nuxt

![图片](https://note.youdao.com/yws/res/7416/WEBRESOURCE97146590a6db70f3f532f124b8fa8b0f)

vue 中的 全局变量

### 数据埋点 - 访问数据来源

【直接访问】：直接输入网站 url（网址） 进行访问

【搜索引擎】：关键字搜索跳转

【外部链接】：通过别人的网址跳转（带来源标识 from / source）

![图片](https://note.youdao.com/yws/res/7420/WEBRESOURCEb54079fe32490c82f06f1d7f714d8e3f)

### 跨域解决： 浏览器会先发一个 option 预请求, 不行就报错了

（1）、jsonp 发起标签 script请求，利用script标签的src不受同源的限制

（2）、vue.config.js / webpack 配置文件 修改 devServer 的 proxy 的指向

（3）、（服务端 / node 端）nginx 的 配置文件修改

nginx:－－－－－－－ 负载均衡 / 反向代理 / 动静分离 / http服务器 （高性能 / 轻量级）

![图片](https://note.youdao.com/yws/res/7425/WEBRESOURCEb940d743025b88b20bf1b9e8a22d11f7)

负载均衡： nginx代理服务器 转发请求到各个服务器（每个服务器都有各自的端口号唯一识别），利用空闲的服务器协调工作，做到资源利用最大化

动静分离： nginx将 静态资源 和 动态资源 进行分离，给对应的服务器去解析，降低单个服务器的压力，加速解析

模块配置：

配置文件 xxx.config

工程化 CI/CD ： 项目部署和自动化打包

![图片](https://note.youdao.com/yws/res/7433/WEBRESOURCE11c7b61bee0efdec4f49347b88de4629)

流程：

### 服务器安装 jenkins，然后配置一个项目

### 然后构建规则，轮询配置

### 然后构建，配置构建命令，然后ssh到服务器指定的目录中

### 接着服务器安装nginx，然后配置 location 的 root 为上面的 remote directory，然后完事。就能享受 jenkins 自动化部署，直接访问就能看到新的代码变化了。

![图片](https://note.youdao.com/yws/res/7438/WEBRESOURCE5f230e24cd30e71b59d07a658e3da523)

### v-html 的 本质就是 设置元素原生的innerHTML

dom 的 innerHTML ： 选所有子元素进行显示 （子元素包括标签在内的字符串形式） : -- 文本带标签的显示为带标签后的样式，普通字符串就显示普通字符串内容

dom 的 innerText ： 选所有子元素的所有文字进行显示 （字符串像是） : -- 文本是什么就显示什么（不认标签的）

### markdown 语法 中的 锚点定位

（1）、[ text content ... ]( #myname ) ------ <span id="myname">text content ...</span>

![图片](https://note.youdao.com/yws/res/7443/WEBRESOURCE09ec992b8b335fb2f1cb7221c9e8f5e4)

（2）、<a href="#anchor"> text content... </a> ----- <span id="anchor">the anchor target</span>

（3）、[[toc]] --- table of contents

showndown --- 用 JavaScript 编写的双向 Markdown 到 HTML 到 Markdown 转换器

showndown-toc ---- [toc] 语法 生成 导航锚点定位

会把文章中所有的 heading 信息，通过闭包的形式传达到上层域

![图片](https://note.youdao.com/yws/res/3574/WEBRESOURCEc595a3fb11d7a360794bdba2f960b4de)

markdown 中写 [toc]，即可生成 toc 到 markdown 的相应位置中

一般的md编辑器（vue的 v-md-editor）就有集成了。使用的时候直接配置就行了。

### B站弹幕不遮挡人脸？

AI生成图片，然后给图片设置 -webkit-mask-image: url(xxx) 属性，文字 绝对定位， 图片相对定位。

mask-image CSS属性用于设置元素上遮罩层的图像。

![图片](https://note.youdao.com/yws/res/3583/WEBRESOURCEbd1728760d01ef9eb7f5e081be7e6bdc)

排查问题，遇到报错

哪种情况适合哪种

排除法

打印日志

猜测法（猜哪里最可能出问题，找哪里）

![图片](https://note.youdao.com/yws/res/3589/WEBRESOURCEe5f820256923f4ff48f9e4dea0484473)

debugger

插件调试法

看缓存数据（storage / vuex）

看源码---调试

有的时候代码没发现出问题，可能不是代码本身的问题 （有可能受限于不同浏览器的各种策略，导致有的浏览器正常 有的设备上显示不正常）

![图片](https://note.youdao.com/yws/res/3591/WEBRESOURCEf1e5bf3e165967743706a3ba9882c075)

换个思路，可能是载体做了限制 源码没问题

或者是 api 不同浏览器做了限制，需要换种实现方法 来兼容 不同的设备 ！！

onLoad 只在页面加载的时候更新一次

onShow 每次页面更新的时候都会调用

index > -1 （已经有if ... else ... 判断在了） 就相当于 index > -1 ？ true : false

![图片](https://note.youdao.com/yws/res/3593/WEBRESOURCE1e827062259699e35b579ca6b983cdd4)

代码优化

![图片](https://note.youdao.com/yws/res/3595/WEBRESOURCEd6a95c9158aaa7152474fafe38b9baad)

![图片](https://note.youdao.com/yws/res/3604/WEBRESOURCE3298a2cf9262427761ba7843b1cb4228)

![图片](https://note.youdao.com/yws/res/3606/WEBRESOURCE77dfdf615912fbeb2c045388f46b424d)

![图片](https://note.youdao.com/yws/res/3609/WEBRESOURCEb02596fd69ec18a09dbef85ae500c53a)

![图片](https://note.youdao.com/yws/res/3611/WEBRESOURCE036cd91ce524adf3fa564c332a3b73c5)

![图片](https://note.youdao.com/yws/res/3617/WEBRESOURCEaa792dc41024c15e3b5b261e46850c8d)

![图片](https://note.youdao.com/yws/res/3626/WEBRESOURCE312cf30fb5fe6a51113636cee2437777)

![图片](https://note.youdao.com/yws/res/3633/WEBRESOURCE272f9c22c66ad6f848e94c0cb56e6381)

![图片](https://note.youdao.com/yws/res/3629/WEBRESOURCEa8a9e7f02c8fe6c6160deeb2473aef96)

![图片](https://note.youdao.com/yws/res/3624/WEBRESOURCEa3e8839517c8d12cdfd59da925dda0b0)

![图片](https://note.youdao.com/yws/res/3655/WEBRESOURCEdf59163fc4497e7c42422f1842ff39d7)

![图片](https://note.youdao.com/yws/res/8522/WEBRESOURCE297bcbe19a38d8f528c7cbd142db4916)

![图片](https://note.youdao.com/yws/res/3662/WEBRESOURCE7827ee657d3fcca92d7c1dbc8838ddae)

![图片](https://note.youdao.com/yws/res/3660/WEBRESOURCE1c039c54d3b192e02de72c733befa0fd)

![图片](https://note.youdao.com/yws/res/3668/WEBRESOURCEf806ff17862f111ad1f0b3607cb219c3)

![图片](https://note.youdao.com/yws/res/3666/WEBRESOURCE00695dd7d759e60306440216c4c20255)

![图片](https://note.youdao.com/yws/res/3683/WEBRESOURCEd1364c3843dd6faa7698f7b564b153ed)

![图片](https://note.youdao.com/yws/res/3687/WEBRESOURCE1627d4a477e9c7e2eec99a367b71f8dc)

![图片](https://note.youdao.com/yws/res/3685/WEBRESOURCE98b9b2e39618dbe8e4bcae22c01cabc5)

![图片](https://note.youdao.com/yws/res/3677/WEBRESOURCE3b8ab86b0e995d2602db359a3bf1862a)

![图片](https://note.youdao.com/yws/res/3690/WEBRESOURCE017444984f4f98c75e6d8e3723e503ff)

![图片](https://note.youdao.com/yws/res/3692/WEBRESOURCE9841e876180fd5efbba49410bfc33336)

![图片](https://note.youdao.com/yws/res/3698/WEBRESOURCE0de6c7a6870a87e325b07ff13e42a3fd)

![图片](https://note.youdao.com/yws/res/3696/WEBRESOURCE609a7cc26a7bdb8b25033df2029db048)

![图片](https://note.youdao.com/yws/res/3700/WEBRESOURCE7998c72b853236239fcdabe1d91c3be8)

![图片](https://note.youdao.com/yws/res/5489/WEBRESOURCEb605a55ef040b6aa49f4b8a3eb11c2ef)

![图片](https://note.youdao.com/yws/res/5509/WEBRESOURCEd5f510f6467d27eb5dc9be74dde2ffbc)

![图片](https://note.youdao.com/yws/res/5501/WEBRESOURCE97af7f5e39604aeb7d72ac4df0f903e4)

![图片](https://note.youdao.com/yws/res/5513/WEBRESOURCEdd248f28779340aec3bef9024cf292ff)

![图片](https://note.youdao.com/yws/res/5504/WEBRESOURCEb49ac4ef12ab4d1651fe4159a2b8efbc)

![图片](https://note.youdao.com/yws/res/3704/WEBRESOURCE2125923e86f823cf80d47611ad7d3752)

![图片](https://note.youdao.com/yws/res/3706/WEBRESOURCE3fcab93eb6d0800f3db503e7f4041351)

![图片](https://note.youdao.com/yws/res/3711/WEBRESOURCE1e3a0eeed75bc6176e3eff537ac9ed0b)

![图片](https://note.youdao.com/yws/res/3715/WEBRESOURCE0c539cc3474c5acf9bd36b624f549cac)

![图片](https://note.youdao.com/yws/res/3732/WEBRESOURCEd6996221c37b3e7ed0a7067c026a8b61)

![图片](https://note.youdao.com/yws/res/3734/WEBRESOURCEe1a32d205600809e874446c9a3ab0b81)

![图片](https://note.youdao.com/yws/res/3736/WEBRESOURCEe689c4894a38048800a045d3bd20805a)

![图片](https://note.youdao.com/yws/res/3740/WEBRESOURCE0bd11553e1a7f18d6f76ae4f6787c73c)

![图片](https://note.youdao.com/yws/res/3746/WEBRESOURCEb1e87ab60330fb6b3ac0873159c3de87)

![图片](https://note.youdao.com/yws/res/3748/WEBRESOURCE09249cfe9020daca814be1ae776f2add)

![图片](https://note.youdao.com/yws/res/10054/WEBRESOURCE5bcea65f2641dc6d03a6d7f9d368d4d1)

![图片](https://note.youdao.com/yws/res/10067/WEBRESOURCE15f65505546049bbbdceb24aced480e1)

![图片](https://note.youdao.com/yws/res/10078/WEBRESOURCEfb36026889d97387934ae51570d1641f)

![图片](https://note.youdao.com/yws/res/10088/WEBRESOURCE4144ab7f2861afff652b37414ca8bf0e)

![图片](https://note.youdao.com/yws/res/10091/WEBRESOURCEab35c716c45b0e4a5f149246624718b6)

![图片](https://note.youdao.com/yws/res/10096/WEBRESOURCE42d9e99df70d83794d4413706b48e68e)

![图片](https://note.youdao.com/yws/res/10098/WEBRESOURCE97d028b322f468f7b4bbca607ed3ec60)

![图片](https://note.youdao.com/yws/res/10104/WEBRESOURCE504deee2ba442eddab2aba64b9ba6e4c)

![图片](https://note.youdao.com/yws/res/10116/WEBRESOURCE11d707851bf262941a791dcd958ce669)

![图片](https://note.youdao.com/yws/res/3759/WEBRESOURCE173f79ef1e93dac04f6dad59b1db5619)

![图片](https://note.youdao.com/yws/res/3770/WEBRESOURCEb6313013f26785ddddd867d32dc00c81)

![图片](https://note.youdao.com/yws/res/6695/WEBRESOURCE4d6db4f40b6e59d5fb1530c609dd3cdf)

![图片](https://note.youdao.com/yws/res/3784/WEBRESOURCE98d86388966b8c19c97d77222e651762)

![图片](https://note.youdao.com/yws/res/4815/WEBRESOURCE99cf40296d7d31244ec390d11c2ff5f6)

![图片](https://note.youdao.com/yws/res/4809/WEBRESOURCEe342f7e85311165ae71e26f3eae60fd3)

![图片](https://note.youdao.com/yws/res/3779/WEBRESOURCE41f512c93bb85c93a6e07c3e50056d79)

![图片](https://note.youdao.com/yws/res/4817/WEBRESOURCEcd499c40ccf34afbcec4855a5063b3dc)

![图片](https://note.youdao.com/yws/res/4821/WEBRESOURCEf76cba42b7416a5b0b2474be5c925f45)

![图片](https://note.youdao.com/yws/res/4833/WEBRESOURCE0e36fd2b10d5314cd341255c7f46573e)

![图片](https://note.youdao.com/yws/res/4831/WEBRESOURCE5767f35dd8f2741a892ee9a8b6a6657e)

![图片](https://note.youdao.com/yws/res/6702/WEBRESOURCE4a3a70c94988ce7c461c43737709dd4f)

![图片](https://note.youdao.com/yws/res/6704/WEBRESOURCE9f239e960837dbae6e63f5b7a00b2116)

![图片](https://note.youdao.com/yws/res/11046/WEBRESOURCE4574de14aff47aee07b861a881434e06)

![图片](https://note.youdao.com/yws/res/11055/WEBRESOURCE330b448fc549186e8fc4c3943117cde5)

![图片](https://note.youdao.com/yws/res/11061/WEBRESOURCE0bdf16c5478c87fd919398e979cd3594)

![图片](https://note.youdao.com/yws/res/11067/WEBRESOURCE3585be44068799f67053256dd831d834)

![图片](https://note.youdao.com/yws/res/11075/WEBRESOURCE83a928f339450324c0532c00d8e6a093)

![图片](https://note.youdao.com/yws/res/3793/WEBRESOURCE403647aa423360bbadbbcf80226c7a32)

![图片](https://note.youdao.com/yws/res/3796/WEBRESOURCEba2aa348618310968982512369f1819e)

![图片](https://note.youdao.com/yws/res/3802/WEBRESOURCE20bf7e214a9870cffe1eee510dfe1ea6)

![图片](https://note.youdao.com/yws/res/3804/WEBRESOURCE511133d05cb0a476185c2154eb806398)

![图片](https://note.youdao.com/yws/res/3812/WEBRESOURCE83739292a03e3f8470c797c041f3d6e5)

![图片](https://note.youdao.com/yws/res/3816/WEBRESOURCEe555abe3142bc4973dc33c004c0e6bec)

![图片](https://note.youdao.com/yws/res/3830/WEBRESOURCEbb0464609ba4ba2fc721cbd0e0316aaa)

![图片](https://note.youdao.com/yws/res/3833/WEBRESOURCEb7e45dcaf189aaf1a5d5ab9ffec26481)

![图片](https://note.youdao.com/yws/res/3838/WEBRESOURCEa5d7d4c1b1d6b6e7fbd6a42a22bc08ca)

![图片](https://note.youdao.com/yws/res/3840/WEBRESOURCE593b3f9180f4aa83bd3b41b14b7712de)

![图片](https://note.youdao.com/yws/res/3848/WEBRESOURCE9e8c0693e9b2ddde0eff6390eec7671a)

![图片](https://note.youdao.com/yws/res/3846/WEBRESOURCE96e1eddb6557a3e6507ca30228f69e28)

![图片](https://note.youdao.com/yws/res/3852/WEBRESOURCE5111eb255e1f5b616caecdf41b8c9345)

![图片](https://note.youdao.com/yws/res/3854/WEBRESOURCE806d80a22249668a6f40c7950354e68f)

![图片](https://note.youdao.com/yws/res/3859/WEBRESOURCE571d6c7043ce72b82bc3c1a2355a9663)

![图片](https://note.youdao.com/yws/res/3861/WEBRESOURCEecc993228b80c890c9fe522b463850b2)

![图片](https://note.youdao.com/yws/res/3865/WEBRESOURCEbda7d55e70284cbb76bada948e54a74b)

![图片](https://note.youdao.com/yws/res/3868/WEBRESOURCE98fe30f744a96092b73cd1a52638e0f6)

![图片](https://note.youdao.com/yws/res/3870/WEBRESOURCEc69208495a4c17cfdc8aac4f0ab7f540)

![图片](https://note.youdao.com/yws/res/3872/WEBRESOURCEd036204542dd78a2643d8234c850823f)

![图片](https://note.youdao.com/yws/res/3893/WEBRESOURCEa5cb557a4556e60e27b2f533fe2bf657)

![图片](https://note.youdao.com/yws/res/7717/WEBRESOURCE49fbe06ebfd46e2e526c19910865cf1b)

![图片](https://note.youdao.com/yws/res/7724/WEBRESOURCEa2b7f1f1b53d3603ebb0c74b2c7cb985)

![图片](https://note.youdao.com/yws/res/8438/WEBRESOURCE782bfa1c18209a19badeedaf0bf5e544)

![图片](https://note.youdao.com/yws/res/8443/WEBRESOURCEb25362d0d8f476a5d57eda40617b5cf4)

![图片](https://note.youdao.com/yws/res/7862/WEBRESOURCE52837d4fc411afa99ccef1b613877094)

![图片](https://note.youdao.com/yws/res/10942/WEBRESOURCE7b522e1820668da98b55d602c36fccdd)

![图片](https://note.youdao.com/yws/res/7707/WEBRESOURCEff40f08125b7d767b1535513299fc2f5)

![图片](https://note.youdao.com/yws/res/7712/WEBRESOURCE3ebfbe55a5d214e0bf11a258283aa752)

![图片](https://note.youdao.com/yws/res/7720/WEBRESOURCEc3c01f3ebe6d4f0075f79c65c8c7f964)

![图片](https://note.youdao.com/yws/res/10349/WEBRESOURCE59741d5d9ba97ee731d7878f867e3818)

![图片](https://note.youdao.com/yws/res/10318/WEBRESOURCEc369ae98ba58fe2689ffc2072034cb1b)

![图片](https://note.youdao.com/yws/res/10324/WEBRESOURCE4012c6d23f98a1919afdbb5434da1225)

![图片](https://note.youdao.com/yws/res/10329/WEBRESOURCEd07de1649e238b55b973580b580db70f)

![图片](https://note.youdao.com/yws/res/10337/WEBRESOURCE7a40a045e89a3c59fe756914dbf27bf2)

![图片](https://note.youdao.com/yws/res/10341/WEBRESOURCEd8d68aa296913672de4afbdc8edda8de)

![图片](https://note.youdao.com/yws/res/7774/WEBRESOURCE4972183e0cf0e7a80df77b70e5a1a194)

![图片](https://note.youdao.com/yws/res/7747/WEBRESOURCE41d21b7197e8a5b55cfab2b2fd5b513a)

![图片](https://note.youdao.com/yws/res/7780/WEBRESOURCE9003ae7e9bccdb2d33cb8430158ca00d)

![图片](https://note.youdao.com/yws/res/7759/WEBRESOURCE4230f84f0e2afb6da0fbe5defb24d74b)

![图片](https://note.youdao.com/yws/res/3897/WEBRESOURCE704790a1da2e5a5d604703c34880d0a2)

![图片](https://note.youdao.com/yws/res/3901/WEBRESOURCE7248c8ffcc6f46f3697d3656a028a6da)

![图片](https://note.youdao.com/yws/res/3957/WEBRESOURCE04ca2b84156d17d03d3728671c8e2e82)

![图片](https://note.youdao.com/yws/res/3962/WEBRESOURCE56f9e8e4bb75208d9494634d1ac8d5a8)

![图片](https://note.youdao.com/yws/res/3966/WEBRESOURCE96fc50731179549e3fcad88b1a5c093d)

![图片](https://note.youdao.com/yws/res/3971/WEBRESOURCEb1882198603182be3bfbb8d3acf3781f)

![图片](https://note.youdao.com/yws/res/3969/WEBRESOURCE7b22190050cef35d4cf93e49f0ce0847)

![图片](https://note.youdao.com/yws/res/4466/WEBRESOURCE9140447ff31c3fa6c796382673d54150)

![图片](https://note.youdao.com/yws/res/3976/WEBRESOURCEbc8a4c7bcc73726d1e1801e194f47ce6)

![图片](https://note.youdao.com/yws/res/7123/WEBRESOURCE5d4cb36572b48f919f514e317bf666b7)

![图片](https://note.youdao.com/yws/res/4029/WEBRESOURCEf00645e3638998494814ea98dc209021)

![图片](https://note.youdao.com/yws/res/4033/WEBRESOURCEa1078c36cc4114b9bd9966b9686a682f)

![图片](https://note.youdao.com/yws/res/4037/WEBRESOURCE12f8d3ff02cd96fe121f78a1ea426437)

![图片](https://note.youdao.com/yws/res/4041/WEBRESOURCE22f9bfea18f93b14773fbceb11a74ea8)

![图片](https://note.youdao.com/yws/res/4043/WEBRESOURCEb2bf707778202612290186c7b676d365)

![图片](https://note.youdao.com/yws/res/4045/WEBRESOURCEc278a5b9c61ba73dcd6bb8af3c419f66)

![图片](https://note.youdao.com/yws/res/4052/WEBRESOURCE7c0ed2ad5969b1ee5aed45d3971ea2c8)

![图片](https://note.youdao.com/yws/res/4064/WEBRESOURCE86bf657d8a1328bdd019197bb91d8ca7)

![图片](https://note.youdao.com/yws/res/4067/WEBRESOURCE8f1086ccba37088e2a51d38c94c0288f)

![图片](https://note.youdao.com/yws/res/4069/WEBRESOURCEdd84a981cfdfb2fbb7726a7b6a7f6071)

![图片](https://note.youdao.com/yws/res/4080/WEBRESOURCEea22017c64835725bf94cd77abfa1003)

![图片](https://note.youdao.com/yws/res/4087/WEBRESOURCE7d05981daaf1985217d225612540d2ed)

![图片](https://note.youdao.com/yws/res/4197/WEBRESOURCE12d3f59f8f94a7ed1752ccc911e82093)

![图片](https://note.youdao.com/yws/res/4209/WEBRESOURCE065da33102aa46b82f929fa110b9d6fd)

![图片](https://note.youdao.com/yws/res/4210/WEBRESOURCEc7c26cd21753856de9ce2ed50860004c)

![图片](https://note.youdao.com/yws/res/4220/WEBRESOURCE008268c2f23c646d076b171785d9795d)

![图片](https://note.youdao.com/yws/res/4138/WEBRESOURCEd0b613119b846e8c138b7ef5ecae06d0)

![图片](https://note.youdao.com/yws/res/6548/WEBRESOURCEe2301408dcecd47d5256e5944dd87d6a)

![图片](https://note.youdao.com/yws/res/4167/WEBRESOURCE3c8a013a56d719206f54684c02a5f0ac)

![图片](https://note.youdao.com/yws/res/4170/WEBRESOURCEaa1229a54aa1eeb6fcf74a9ce63d97b5)

![图片](https://note.youdao.com/yws/res/4180/WEBRESOURCEdcb28229b3a00e31a0902ec08d78db1c)

![图片](https://note.youdao.com/yws/res/5252/WEBRESOURCE04d8d8f6f36b9183cd13aef57dca0d8c)

![图片](https://note.youdao.com/yws/res/4288/WEBRESOURCEb8a39d4a7093cce921b51dc9d7ef6517)

![图片](https://note.youdao.com/yws/res/4295/WEBRESOURCE5788da44b0f5e39563235482ed0a81f2)

![图片](https://note.youdao.com/yws/res/4589/WEBRESOURCE91967e9b1d5128c6d7635370b18198bb)

![图片](https://note.youdao.com/yws/res/4601/WEBRESOURCE75f657f47e1d7722f4177f99d1d946bb)

![图片](https://note.youdao.com/yws/res/4608/WEBRESOURCE4e518921427acedc6c0b06aa6b44f481)

![图片](https://note.youdao.com/yws/res/4612/WEBRESOURCEe2c734f36a3bf2df1a6cf339b83ec416)

![图片](https://note.youdao.com/yws/res/4613/WEBRESOURCE3b99a8cbbb80968e5460f6b3361b21c6)

![图片](https://note.youdao.com/yws/res/4618/WEBRESOURCEccd6c45b5bfe0e467a725d64d153c170)

![图片](https://note.youdao.com/yws/res/4620/WEBRESOURCE76d145cfb34b96dd7b1001fa39685f6a)

![图片](https://note.youdao.com/yws/res/4623/WEBRESOURCE73257a620038a8935e0492db731adebd)

![图片](https://note.youdao.com/yws/res/4626/WEBRESOURCEae7f05081c704ae3c02d3127fe3934d3)

![图片](https://note.youdao.com/yws/res/4633/WEBRESOURCE6c53c0ea52bfa03f53261497eaf1972f)

![图片](https://note.youdao.com/yws/res/4635/WEBRESOURCEf1f43c0f2527220c44b754651bb1f9d6)

![图片](https://note.youdao.com/yws/res/4647/WEBRESOURCE8045c7fb6391a6b8bf83d48bbf97f354)

![图片](https://note.youdao.com/yws/res/4658/WEBRESOURCE876600285ef78b68429ea25855726595)

![图片](https://note.youdao.com/yws/res/4661/WEBRESOURCEdc6f0a9a42f1611f083473b5eca5602b)

![图片](https://note.youdao.com/yws/res/4850/WEBRESOURCE6e418a816a46ef44a262327b577ba455)

![图片](https://note.youdao.com/yws/res/4244/WEBRESOURCE5ecc1cd61fab1422dc3a0917379b7635)

![图片](https://note.youdao.com/yws/res/4237/WEBRESOURCE6c82ba9901fc671743224d6c127e7870)

![图片](https://note.youdao.com/yws/res/4696/WEBRESOURCEa099f78c4523b5de50898c7896f11e4d)

![图片](https://note.youdao.com/yws/res/4258/WEBRESOURCEa05da6d7270e9d907b2a7237e77726ca)

![图片](https://note.youdao.com/yws/res/4260/WEBRESOURCE2ee611d92bb609996c16561a501409d1)

![图片](https://note.youdao.com/yws/res/4264/WEBRESOURCE455bcd62b9913e1fa6af24a91af6804e)

![图片](https://note.youdao.com/yws/res/4267/WEBRESOURCE1fbf47b5194507abf7cb3a3ccad6ba94)

![图片](https://note.youdao.com/yws/res/4279/WEBRESOURCEae0058be2427f75994f96233d88ea0d9)

![图片](https://note.youdao.com/yws/res/4277/WEBRESOURCE696f6e26db1314510549efb29ab6f528)

![图片](https://note.youdao.com/yws/res/4341/WEBRESOURCEdea5fcbeb745fd34a1274a683a197ef9)

![图片](https://note.youdao.com/yws/res/4353/WEBRESOURCE2f5e669bc020df01b61d0616b6cdab3a)

![图片](https://note.youdao.com/yws/res/6572/WEBRESOURCE022a2627a1d0612cef1a611064682d8f)

![图片](https://note.youdao.com/yws/res/6581/WEBRESOURCE6f59af1ea4348e44c20ea2405caf62a2)

![图片](https://note.youdao.com/yws/res/6578/WEBRESOURCE67218777f59e4c30fef6f38dccccb576)

![图片](https://note.youdao.com/yws/res/6588/WEBRESOURCE3361e2ce55078eeb0354f4c4a9d32597)

![图片](https://note.youdao.com/yws/res/4358/WEBRESOURCE177b8e5dc04c2b5221e7f2e8c6fd82fb)

![图片](https://note.youdao.com/yws/res/4360/WEBRESOURCE3103fa57bc05cc36d5d9fbe90b7cddf1)

![图片](https://note.youdao.com/yws/res/4362/WEBRESOURCE46e6adf9a4d976892e54203a34fa5d72)

![图片](https://note.youdao.com/yws/res/5006/WEBRESOURCE1656b26587d577113f9a375af0b832a2)

![图片](https://note.youdao.com/yws/res/5014/WEBRESOURCE51a66799e55399bf2b9d43e48f3c10d3)

![图片](https://note.youdao.com/yws/res/5012/WEBRESOURCEbfe1a8f8efc786dbd1f0f8fbf04afe7e)

![图片](https://note.youdao.com/yws/res/5016/WEBRESOURCE52b23b5e9f0f49caf0b098ddec1b553e)

![图片](https://note.youdao.com/yws/res/5056/WEBRESOURCE02a06fbdcf4d6dd1187edd376d5243be)

![图片](https://note.youdao.com/yws/res/5058/WEBRESOURCE1c4710e0c537eab03d8bda21289eb503)

![图片](https://note.youdao.com/yws/res/5040/WEBRESOURCEbc3f4ddc44649cd011ff8396072a2cb8)

![图片](https://note.youdao.com/yws/res/5050/WEBRESOURCE386d985911294fb4942c7e22cb51578e)

![图片](https://note.youdao.com/yws/res/5043/WEBRESOURCEcbc2c0a0664ccfb64e1fc5236c901ae9)

![图片](https://note.youdao.com/yws/res/5044/WEBRESOURCE262d0e26c6eefec4d12ba757e210fd7e)

![图片](https://note.youdao.com/yws/res/5045/WEBRESOURCE506429c88662dc62d13cd68202e82558)

![图片](https://note.youdao.com/yws/res/5032/WEBRESOURCE1d48c03ec4a9265d61fe068739f99426)

![图片](https://note.youdao.com/yws/res/5037/WEBRESOURCE7cfd16244d366af41c75d329023fab6e)

![图片](https://note.youdao.com/yws/res/6156/WEBRESOURCEf75d5e7d073ce0b82575c923862520e1)

![图片](https://note.youdao.com/yws/res/4338/WEBRESOURCE827d295431910495f6a3ac58ddce5fa5)

![图片](https://note.youdao.com/yws/res/4372/WEBRESOURCE70d7f48a15135cbd5418c300e7d6cb77)

![图片](https://note.youdao.com/yws/res/4977/WEBRESOURCE35536f8f60d7055df50918355d434903)

![图片](https://note.youdao.com/yws/res/4979/WEBRESOURCEe788a30bbbda42e045d90da582338ed7)

![图片](https://note.youdao.com/yws/res/4983/WEBRESOURCE7f0a86511d59acb0603c579a54f0cfa0)

![图片](https://note.youdao.com/yws/res/4985/WEBRESOURCEed62b1976c68691afdff35410554d1bd)

![图片](https://note.youdao.com/yws/res/4987/WEBRESOURCE2cd30be3927cf4f0d7cce8458b5fbe09)

![图片](https://note.youdao.com/yws/res/4411/WEBRESOURCEfeee8452820b844d393d4b25adfc0bf3)

![图片](https://note.youdao.com/yws/res/4441/WEBRESOURCE7011d4385d7c2799cd7e51aa73bd5d91)

![图片](https://note.youdao.com/yws/res/4449/WEBRESOURCE16cc94e1a6232edfd0876ce277c9fdf2)

![图片](https://note.youdao.com/yws/res/4456/WEBRESOURCEd63f6d6fbc69632bc50173348e77b4a0)

![图片](https://note.youdao.com/yws/res/4464/WEBRESOURCEbc61e8d30da24780d7553b5573b73b23)

![图片](https://note.youdao.com/yws/res/4914/WEBRESOURCE0cbed7c3f787f963efa85f44277e3795)

![图片](https://note.youdao.com/yws/res/4472/WEBRESOURCE7d8555e31a06bf3c5c82f9384d065639)

![图片](https://note.youdao.com/yws/res/4482/WEBRESOURCE6c0468174510447a62fd68c473c1e537)

![图片](https://note.youdao.com/yws/res/4486/WEBRESOURCEa9d19f6984b490d53da06d9e28b516d0)

![图片](https://note.youdao.com/yws/res/4517/WEBRESOURCEd65ab7d8e05d4e59dfc7adc12b4084e9)

![图片](https://note.youdao.com/yws/res/4493/WEBRESOURCE00ee1bb7c48bb8670a38ed6e8025e8a9)

![图片](https://note.youdao.com/yws/res/4499/WEBRESOURCEe8c9e76ff4642750ab8dc41963763e86)

![图片](https://note.youdao.com/yws/res/4530/WEBRESOURCE734f9dc7328d53bd8d2eb48b19dceba9)

![图片](https://note.youdao.com/yws/res/4532/WEBRESOURCE528b640d0f96ab8678c011946592b248)

![图片](https://note.youdao.com/yws/res/4565/WEBRESOURCEb3271de52019ef6eb2f8416f5533769f)

![图片](https://note.youdao.com/yws/res/4547/WEBRESOURCEe1b3c79a346bcba1c1ea09fa019d66df)

![图片](https://note.youdao.com/yws/res/4552/WEBRESOURCE7fc08b7ee6739bdbb4260df66045479d)

![图片](https://note.youdao.com/yws/res/5293/WEBRESOURCE5a574817aba6958bf0cd8122d89d1b04)

![图片](https://note.youdao.com/yws/res/5295/WEBRESOURCE262147fb843d9315d91431e84e4b3dcd)

![图片](https://note.youdao.com/yws/res/4664/WEBRESOURCE7df9536dcabed03e8f12dd5bdb93907d)

![图片](https://note.youdao.com/yws/res/4666/WEBRESOURCE80030323a5718efcc2b571728a083148)

![图片](https://note.youdao.com/yws/res/10232/WEBRESOURCE3478d69e96c24bd8dbd9ab1f71130822)

![图片](https://note.youdao.com/yws/res/10239/WEBRESOURCE640eefd9da684a3c65e21278a6bfb1ff)

![图片](https://note.youdao.com/yws/res/10242/WEBRESOURCE5d6f13c67f7b781a819564b405731809)

![图片](https://note.youdao.com/yws/res/10246/WEBRESOURCE15cfeaec30d91a14687004db0e91a432)

![图片](https://note.youdao.com/yws/res/10263/WEBRESOURCEc8085973248896720161aa07f989ece2)

![图片](https://note.youdao.com/yws/res/5191/WEBRESOURCE7b96fe6adef579d236df7242c065ae98)

![图片](https://note.youdao.com/yws/res/5194/WEBRESOURCE14a255fdb5bfd2f5115fe1537cd7ee98)

![图片](https://note.youdao.com/yws/res/5196/WEBRESOURCEa8bc12cb3128686ca9ed7f9bb25aba6a)

![图片](https://note.youdao.com/yws/res/5199/WEBRESOURCEae5cb725ce2e38ce6e4f46ce63bdd796)

![图片](https://note.youdao.com/yws/res/4585/WEBRESOURCEaff1f80762db64b490bceea1baa655c4)

![图片](https://note.youdao.com/yws/res/4672/WEBRESOURCEf002025bb828775d099ac45fb4d85f67)

![图片](https://note.youdao.com/yws/res/4677/WEBRESOURCE6b38e2c2dd8f478610f32507aeb3eceb)

![图片](https://note.youdao.com/yws/res/4734/WEBRESOURCE0b6a62be502d345c894e1dc461d2b51b)

![图片](https://note.youdao.com/yws/res/5440/WEBRESOURCE114bbf272b23ea530342a02ead1e150a)

![图片](https://note.youdao.com/yws/res/6358/WEBRESOURCEd1ef19999cd4d546f784ea9a253d6b66)

![图片](https://note.youdao.com/yws/res/4799/WEBRESOURCEd6c51d57a207b25a6b1a54113a344090)

![图片](https://note.youdao.com/yws/res/4802/WEBRESOURCE6a98aa100638c870232ac904c34d2836)

![图片](https://note.youdao.com/yws/res/4805/WEBRESOURCE89416a8ffe5d53423b25052e02721616)

![图片](https://note.youdao.com/yws/res/4796/WEBRESOURCE6b47d4bf434c12f499fac99ae4bceca4)

![图片](https://note.youdao.com/yws/res/4803/WEBRESOURCE5124189af0ecaa75447f32ce71705836)

![图片](https://note.youdao.com/yws/res/4841/WEBRESOURCE87f6ffd3b97fc776afaaf39389d87de8)

![图片](https://note.youdao.com/yws/res/4845/WEBRESOURCE4a99fb052973f0c7bf66d4ce90ce32ea)

![图片](https://note.youdao.com/yws/res/4991/WEBRESOURCE504b151c3771a304da1d9bf227eac532)

![图片](https://note.youdao.com/yws/res/4996/WEBRESOURCE9834fc2c95b6907e6fb16024ebe4bc8d)

![图片](https://note.youdao.com/yws/res/6304/WEBRESOURCE31e4cab503ac94979db180372951529d)

![图片](https://note.youdao.com/yws/res/4963/WEBRESOURCE6ebca84a5fc904f6e79e67f989e28017)

![图片](https://note.youdao.com/yws/res/5069/WEBRESOURCE1de9d149f7e812d65083369cc6e93f87)

![图片](https://note.youdao.com/yws/res/5076/WEBRESOURCEc25a469cb8bdbae9b85edff2d894fab8)

![图片](https://note.youdao.com/yws/res/5084/WEBRESOURCE3254fa6abe468102abc881f5bfd34c7e)

![图片](https://note.youdao.com/yws/res/8801/WEBRESOURCEee1073bd38d7ac5d1b2dde99116da153)

![图片](https://note.youdao.com/yws/res/8806/WEBRESOURCEb26b1b7276a45f46ebba834223ded23e)

![图片](https://note.youdao.com/yws/res/7329/WEBRESOURCEbc0a49e7f7e971fe5f8c08231066c1de)

![图片](https://note.youdao.com/yws/res/9401/WEBRESOURCE3306e2061bb710ebee38110057c9a77f)

![图片](https://note.youdao.com/yws/res/9408/WEBRESOURCE609eb3cb3772b0e334be746d23d72368)

![图片](https://note.youdao.com/yws/res/5081/WEBRESOURCE5a8e0c5f27ba2012eb83a1046ce390ca)

![图片](https://note.youdao.com/yws/res/8816/WEBRESOURCE569e392affa70e53e18fd8ce4cd0a98a)

![图片](https://note.youdao.com/yws/res/8820/WEBRESOURCE7a83e8f9b0afc1db78d9c7ae171ec060)

![图片](https://note.youdao.com/yws/res/8827/WEBRESOURCEef8bc47b8be634e1c8d8f23e91438259)

![图片](https://note.youdao.com/yws/res/8832/WEBRESOURCEd58d07053d40864b4858888dcd164d49)

![图片](https://note.youdao.com/yws/res/8843/WEBRESOURCEa99d9804f4724364aab71fef71aa0544)

![图片](https://note.youdao.com/yws/res/8849/WEBRESOURCE1f751dd5622674b0055ae83eb6ff2a2d)

![图片](https://note.youdao.com/yws/res/8890/WEBRESOURCE1d093d874e7cc05ab33736a2b8901e86)

![图片](https://note.youdao.com/yws/res/4868/WEBRESOURCE4e4eaa4748cbdf93d6ed6816c43bc852)

![图片](https://note.youdao.com/yws/res/5099/WEBRESOURCE4073f9748010315da6886fa49a278b85)

![图片](https://note.youdao.com/yws/res/5092/WEBRESOURCE8850ab5b2ee9a514fed5653892050ecf)

![图片](https://note.youdao.com/yws/res/5102/WEBRESOURCEee86660537f1d4d760055b2dbcc74743)

![图片](https://note.youdao.com/yws/res/5109/WEBRESOURCE7f058649c50e4c4c808e5df3982d75e2)

![图片](https://note.youdao.com/yws/res/5120/WEBRESOURCE86df5241cc28108a85d7cb4196204faf)

![图片](https://note.youdao.com/yws/res/5156/WEBRESOURCE7b9d8fa404acd9a25da2e110e247148b)

![图片](https://note.youdao.com/yws/res/5161/WEBRESOURCEa2f1934d78f6172021fc694368ecbd41)

![图片](https://note.youdao.com/yws/res/5165/WEBRESOURCEf08345b3a39efda4fcf92c8907bbcf35)

![图片](https://note.youdao.com/yws/res/5168/WEBRESOURCE0dcc496d0e56e408de323fe828d3c786)

![图片](https://note.youdao.com/yws/res/5172/WEBRESOURCE0c0e0582178706db9fe69f39fc36cdaf)

![图片](https://note.youdao.com/yws/res/5174/WEBRESOURCE991d18c30f1dfbea715b390ad424d892)

![图片](https://note.youdao.com/yws/res/4921/WEBRESOURCE9a3cf882932c7f60d696d340767a97ab)

![图片](https://note.youdao.com/yws/res/4928/WEBRESOURCE2cbf89b9b47bfdf9ea6a19242088da17)

![图片](https://note.youdao.com/yws/res/4943/WEBRESOURCE7a5603b82100b3e7540e9fb4488ebed6)

![图片](https://note.youdao.com/yws/res/5061/WEBRESOURCEffeefc0499c914e74651e41807fb0fdb)

![图片](https://note.youdao.com/yws/res/5063/WEBRESOURCE302365fe9dae95c2ef2b8e7eef94a341)

![图片](https://note.youdao.com/yws/res/5132/WEBRESOURCE536825185f988cc4085d8478e3040e63)

![图片](https://note.youdao.com/yws/res/5139/WEBRESOURCEe38eefaa33834a4de31d464aefe71f63)

![图片](https://note.youdao.com/yws/res/6475/WEBRESOURCEff677423f6f958acb6386326615906e4)

![图片](https://note.youdao.com/yws/res/6477/WEBRESOURCEcdbdcbbccf012382f0c42e5d6124e462)

![图片](https://note.youdao.com/yws/res/6482/WEBRESOURCEb2c8fe19e9dbcc206aa938017ffb3d8f)

![图片](https://note.youdao.com/yws/res/5151/WEBRESOURCE3330dc5a3733f744a89eb678dc730c25)

![图片](https://note.youdao.com/yws/res/5149/WEBRESOURCEd5e1a67ba6a277e5bef3a6bbb5794d7d)

![图片](https://note.youdao.com/yws/res/5147/WEBRESOURCE8d5f84e2c19a838d377a00471c2c5a9b)

![图片](https://note.youdao.com/yws/res/7908/WEBRESOURCE9b2b80483bce8ab279f630a3b7ce5c67)

![图片](https://note.youdao.com/yws/res/5206/WEBRESOURCE9810d10d33f47353149b00ae6fd71b1c)

![图片](https://note.youdao.com/yws/res/5212/WEBRESOURCE02fa2b459f9253480baedd9c42c9de50)

![图片](https://note.youdao.com/yws/res/5214/WEBRESOURCE2c7fbcbf9bcea8d0a141f848507500e6)

![图片](https://note.youdao.com/yws/res/6661/WEBRESOURCE6ce8ab6eea2105029b4988c61de319c6)

![图片](https://note.youdao.com/yws/res/6667/WEBRESOURCEc662d9f2516dbc9d059cb0f1a833ec2d)

![图片](https://note.youdao.com/yws/res/6675/WEBRESOURCE82147e358ceb1340b27eee60b2e66bca)

![图片](https://note.youdao.com/yws/res/6676/WEBRESOURCEe29ca6bb741907a4f0bd57903bbd9ae1)

![图片](https://note.youdao.com/yws/res/6688/WEBRESOURCE2831a4d047e6423835b8ab5f686029ef)

![图片](https://note.youdao.com/yws/res/5226/WEBRESOURCEb07dfbf5e3ca9f9ccb68bef1c9906f55)

![图片](https://note.youdao.com/yws/res/5255/WEBRESOURCEb97f953ea80b4787662011c587e94f4d)

![图片](https://note.youdao.com/yws/res/5307/WEBRESOURCE37fd49f35a40ee9285e79547ea1904f0)

![图片](https://note.youdao.com/yws/res/6627/WEBRESOURCE854f01d5f0b4f306b9c09472877a353f)

![图片](https://note.youdao.com/yws/res/6629/WEBRESOURCE7df1333285c00d6c44a2a572c7b9b820)

![图片](https://note.youdao.com/yws/res/6633/WEBRESOURCE6129cd9f8407712f66aee02dc4f23aa5)

![图片](https://note.youdao.com/yws/res/6638/WEBRESOURCE4ea9884ec35bee783e38f446a78f5833)

![图片](https://note.youdao.com/yws/res/6641/WEBRESOURCEa25bc85b2370e7adf835ac7b8b545d9e)

![图片](https://note.youdao.com/yws/res/5445/WEBRESOURCE23766f3689358e569a9196e689b8e538)

![图片](https://note.youdao.com/yws/res/5447/WEBRESOURCEceeee587b9487c67b16c56381b4a2715)

![图片](https://note.youdao.com/yws/res/5449/WEBRESOURCE3e284748c45d9bfd557d04b3e32da5a1)

![图片](https://note.youdao.com/yws/res/5461/WEBRESOURCEa225cac0f6624aa34c1c6b222e2d37e6)

![图片](https://note.youdao.com/yws/res/5467/WEBRESOURCEc775b9326b731b10efbb4249be6a05cb)

![图片](https://note.youdao.com/yws/res/5469/WEBRESOURCE9d5263f75c9452608821372c774af232)

![图片](https://note.youdao.com/yws/res/5472/WEBRESOURCEb19f38018a6e3543b565db96c80ced61)

![图片](https://note.youdao.com/yws/res/5480/WEBRESOURCEf36e57b408340af4b9bac6be5fc93929)

![图片](https://note.youdao.com/yws/res/5484/WEBRESOURCE8fc6e4c4397ac69cca719e40fe44d301)

![图片](https://note.youdao.com/yws/res/5573/WEBRESOURCE5942caa221d6b62cc961fc07b885bab7)

![图片](https://note.youdao.com/yws/res/5537/WEBRESOURCE9ac6fdc5ac7fb6688b3fcc8370cc7da1)

![图片](https://note.youdao.com/yws/res/5529/WEBRESOURCEa534816e6a8e95bfe32070d596d09bdb)

![图片](https://note.youdao.com/yws/res/5587/WEBRESOURCEb6ae990f2d3e555e92756b8dc3208664)

![图片](https://note.youdao.com/yws/res/5591/WEBRESOURCE27e18f0d7991e2d3ff83977c76560d7c)

![图片](https://note.youdao.com/yws/res/5611/WEBRESOURCE0817388ba5e5997d27a020bdb5bef7bf)

![图片](https://note.youdao.com/yws/res/5613/WEBRESOURCEe04fd0b79b1578fa74204f0696965ca8)

![图片](https://note.youdao.com/yws/res/5685/WEBRESOURCEd544a81d174c1857fbc9a7315ceee20a)

![图片](https://note.youdao.com/yws/res/5713/WEBRESOURCEaa85131e594daade4b6e549e0a523bb3)

![图片](https://note.youdao.com/yws/res/5726/WEBRESOURCEb4c236b6912d6e75f04ac7a29f8213b6)

![图片](https://note.youdao.com/yws/res/5747/WEBRESOURCE52bcf31d003e7227f2797fc39ec87f1e)

![图片](https://note.youdao.com/yws/res/5831/WEBRESOURCE01cbc60f0016c7f1816dfb701365545b)

![图片](https://note.youdao.com/yws/res/5833/WEBRESOURCE7e63eac8dc7c32cf26e1c66c6ef0085c)

![图片](https://note.youdao.com/yws/res/5835/WEBRESOURCEaee3f7bfee999e57fe096d85e2f20933)

![图片](https://note.youdao.com/yws/res/5837/WEBRESOURCEe230b19d39345e0367457f33a64d3ce5)

![图片](https://note.youdao.com/yws/res/5839/WEBRESOURCEb9f20d62d37f51c40b89e206609325d9)

![图片](https://note.youdao.com/yws/res/5861/WEBRESOURCE1028f592d3ffc5ad42d232371b6cdce2)

![图片](https://note.youdao.com/yws/res/7086/WEBRESOURCE09c2e28eb0ea495693084ba874b1cf28)

![图片](https://note.youdao.com/yws/res/7088/WEBRESOURCEf39a4096f4e36b0d081275eb9c40908a)

![图片](https://note.youdao.com/yws/res/7094/WEBRESOURCEd5cd1eafead3c7d941987167c23697ce)

![图片](https://note.youdao.com/yws/res/8184/WEBRESOURCE03677d0397dd965c4b4dee8935cd1b82)

![图片](https://note.youdao.com/yws/res/7110/WEBRESOURCE6ac06b1a3175082ab57ce3b10a4882cc)

![图片](https://note.youdao.com/yws/res/5900/WEBRESOURCEd9b62c724eaf619b5a182f3b3110d588)

![图片](https://note.youdao.com/yws/res/5904/WEBRESOURCE38a546e11bf807cdbf8e820a8a28f368)

![图片](https://note.youdao.com/yws/res/5955/WEBRESOURCEb2ce6fc20643475fd03afd61b18c55a1)

![图片](https://note.youdao.com/yws/res/5948/WEBRESOURCE9b7cba6c388f2f5494cd65ec73b29e12)

![图片](https://note.youdao.com/yws/res/5952/WEBRESOURCEb2e60c87f524eea30969d73461db0c98)

![图片](https://note.youdao.com/yws/res/5962/WEBRESOURCE31f0488e9f8b681cece8c745ccfb633c)

![图片](https://note.youdao.com/yws/res/5966/WEBRESOURCE8f76e147b7d5c577497f107a23b84611)

![图片](https://note.youdao.com/yws/res/5987/WEBRESOURCE57eb89b6d8da4e8daa69d070d90a99ce)

![图片](https://note.youdao.com/yws/res/5999/WEBRESOURCEc15039fecc97d349b2c1dc5bd5382259)

![图片](https://note.youdao.com/yws/res/6021/WEBRESOURCE2c9d2aff4297a1604f9d8337794eea08)

![图片](https://note.youdao.com/yws/res/6038/WEBRESOURCE26163293c5532e3b4f75b974d66f634b)

![图片](https://note.youdao.com/yws/res/6041/WEBRESOURCE8c55f419ccd218f897e8c5628a389b5c)

![图片](https://note.youdao.com/yws/res/6055/WEBRESOURCE4c51b46a021439fe467ecdbd9f639ed3)

![图片](https://note.youdao.com/yws/res/6066/WEBRESOURCEc04f3c310b58169067624108c71c0aeb)

![图片](https://note.youdao.com/yws/res/6079/WEBRESOURCEcff1148d7df0d759b47ccdcec0eeff56)

![图片](https://note.youdao.com/yws/res/6052/WEBRESOURCE704f129036637053b76c7fc14fb58766)

![图片](https://note.youdao.com/yws/res/6074/WEBRESOURCE7ce85fbe6406309e84a29addc093ddf4)

![图片](https://note.youdao.com/yws/res/6106/WEBRESOURCE8ce80c965b9053d9e05bd9c8bb3450cd)

![图片](https://note.youdao.com/yws/res/6115/WEBRESOURCEa38b24ff2c441f8873e1f46cf1a314d7)

![图片](https://note.youdao.com/yws/res/6119/WEBRESOURCEb1938613e5c1f5f822f05d6306069cd3)

![图片](https://note.youdao.com/yws/res/6127/WEBRESOURCE708fc37b151986b44fb28b4133e0e8d7)

![图片](https://note.youdao.com/yws/res/7133/WEBRESOURCE2bf9a14c8ec51d2e90d01f9697472946)

![图片](https://note.youdao.com/yws/res/7135/WEBRESOURCE9739c9d9b49ee18fdbc74b27602f5419)

![图片](https://note.youdao.com/yws/res/6913/WEBRESOURCE004fa630ec51b9d76fc45d0ea310949c)

![图片](https://note.youdao.com/yws/res/6915/WEBRESOURCEf4b203f9c3d3d1932c0fa8babf2eaeae)

![图片](https://note.youdao.com/yws/res/6174/WEBRESOURCEf87c02129fcebcbad36a244b53b5e786)

![图片](https://note.youdao.com/yws/res/6233/WEBRESOURCE3ec15d4b1afef3bdfa82ca7b5b2c2e55)

![图片](https://note.youdao.com/yws/res/6238/WEBRESOURCE679d27145b7e51e0427fdbe62ab2454c)

![图片](https://note.youdao.com/yws/res/6241/WEBRESOURCE0116b4e3b0ae62062627843ee59f5cbf)

![图片](https://note.youdao.com/yws/res/6243/WEBRESOURCE06479c6ef9121431e147e7aee7121f00)

![图片](https://note.youdao.com/yws/res/6245/WEBRESOURCE474a6987c3b7dac4e3af9ff7b440818d)

![图片](https://note.youdao.com/yws/res/6251/WEBRESOURCE9f20199912355544641912ec0d4b7556)

![图片](https://note.youdao.com/yws/res/7880/WEBRESOURCEd68cf3144e4f66efb1290bc048df5825)

![图片](https://note.youdao.com/yws/res/8620/WEBRESOURCE141612ca810ecd46b10bcd59614929e8)

![图片](https://note.youdao.com/yws/res/8622/WEBRESOURCE36401e605271650841dc014f1b3cfeab)

![图片](https://note.youdao.com/yws/res/6343/WEBRESOURCEa4546ddabf82b93787732e2b6140eea5)

![图片](https://note.youdao.com/yws/res/6319/WEBRESOURCE3622ce8cfdde3c948d7013de488fca8e)

![图片](https://note.youdao.com/yws/res/6326/WEBRESOURCE159c3ee54db489fac2e652a40d511989)

![图片](https://note.youdao.com/yws/res/6338/WEBRESOURCE96ec2d76720fa143085bb5bd66e9c69e)

![图片](https://note.youdao.com/yws/res/6336/WEBRESOURCEb1bc53c4aa6790a9f90d3395d2541b83)

![图片](https://note.youdao.com/yws/res/6332/WEBRESOURCE31124132d7e896e99b38583c5c40b839)

![图片](https://note.youdao.com/yws/res/6367/WEBRESOURCE3324bdad00a432557c892f6d43ee4178)

![图片](https://note.youdao.com/yws/res/6369/WEBRESOURCE9b70acd8d5c391dae5084842e709e29e)

![图片](https://note.youdao.com/yws/res/6388/WEBRESOURCE6188123359559dd0863c830c1895075b)

![图片](https://note.youdao.com/yws/res/6395/WEBRESOURCE29e13a0a1e4fd7580dca82a9ab8bb59a)

![图片](https://note.youdao.com/yws/res/6397/WEBRESOURCEc1d45749e9a63bef9478fda2be03d288)

![图片](https://note.youdao.com/yws/res/6401/WEBRESOURCE0d24adeb84ca636619cfd8671c6b8c28)

![图片](https://note.youdao.com/yws/res/6404/WEBRESOURCEce85c092518ef6b0fc2aa809235198fb)

![图片](https://note.youdao.com/yws/res/6412/WEBRESOURCEb95c1021a9966eb864fb947b10fdaf06)

![图片](https://note.youdao.com/yws/res/6427/WEBRESOURCE57772610167728a9f232de7618b70757)

![图片](https://note.youdao.com/yws/res/6430/WEBRESOURCEd1a66cf44414379b019fd63b6a5e2a8a)

![图片](https://note.youdao.com/yws/res/6434/WEBRESOURCE9e03c899f2970801891d891c4e6f1f48)

![图片](https://note.youdao.com/yws/res/6438/WEBRESOURCE082d0d04d6fb9a8f74126a1bcf6a467b)

![图片](https://note.youdao.com/yws/res/6442/WEBRESOURCE7607557f6a55e2b12df11d7c497e5d2b)

![图片](https://note.youdao.com/yws/res/6450/WEBRESOURCE384549f35fc413dde86071aad44ce4bb)

![图片](https://note.youdao.com/yws/res/6462/WEBRESOURCEee7760b56f21dd5501d78022cf2033f2)

![图片](https://note.youdao.com/yws/res/6466/WEBRESOURCE974da3f49799a02a62c28b0626900fd6)

![图片](https://note.youdao.com/yws/res/6490/WEBRESOURCE068f666f9b91edea670682723558aab7)

![图片](https://note.youdao.com/yws/res/6493/WEBRESOURCE54576b2c8f1cdd3821d16a5603eade65)

![图片](https://note.youdao.com/yws/res/6497/WEBRESOURCEcf370556038cd02c2c75059b5e025aba)

![图片](https://note.youdao.com/yws/res/6509/WEBRESOURCEae7f75fa9cf60226e41b716e551e2cff)

![图片](https://note.youdao.com/yws/res/6520/WEBRESOURCEd588d7f39ee48902fd484d8b90c4706c)

![图片](https://note.youdao.com/yws/res/6846/WEBRESOURCE50d30f682212ddbd04798d8af6d15aad)

![图片](https://note.youdao.com/yws/res/6848/WEBRESOURCE1a392e5b490186fee23d8f101eb6e18c)

![图片](https://note.youdao.com/yws/res/6900/WEBRESOURCEfbc8f46b38a9a66f7e0619978eb702c0)

![图片](https://note.youdao.com/yws/res/6902/WEBRESOURCE6e2f5a8fe9fc6585c1e70c4a6cb450cc)

![图片](https://note.youdao.com/yws/res/6904/WEBRESOURCEf38dd5670b3b0a36a34a9eb37fea0776)

![图片](https://note.youdao.com/yws/res/6910/WEBRESOURCE2a817c87eb0a66d6316e515ecbe76809)

![图片](https://note.youdao.com/yws/res/8136/WEBRESOURCE67bcb2e63816ce417458ca42a413c674)

![图片](https://note.youdao.com/yws/res/6743/WEBRESOURCEf57cb01c143e41daab2541d58d86b274)

![图片](https://note.youdao.com/yws/res/6748/WEBRESOURCE7645bf9f462dde846c0b6f1ab0e84198)

![图片](https://note.youdao.com/yws/res/6752/WEBRESOURCE93f336533decdabe370711b6de683212)

![图片](https://note.youdao.com/yws/res/6756/WEBRESOURCEc94eef05e8686cfb45fc62a148529045)

![图片](https://note.youdao.com/yws/res/6754/WEBRESOURCE94d1a9bff8385a7208acdcd58a7a9f85)

![图片](https://note.youdao.com/yws/res/6760/WEBRESOURCE82a2af33d8dd47e49314f25e44bbd3d8)

![图片](https://note.youdao.com/yws/res/6763/WEBRESOURCE36fdced709174ea838416a7eeecde258)

![图片](https://note.youdao.com/yws/res/6765/WEBRESOURCE24782c2ff95b985884b4f3fd4c2cccfc)

![图片](https://note.youdao.com/yws/res/10968/WEBRESOURCEdef7513f3fd4d92dce02f39740d8bdeb)

![图片](https://note.youdao.com/yws/res/10973/WEBRESOURCE198c24240dbee7a0ab85eba1087d12bb)

![图片](https://note.youdao.com/yws/res/10980/WEBRESOURCEc6860cfa4effdfd3b0b9d494fa8140c6)

![图片](https://note.youdao.com/yws/res/10989/WEBRESOURCEe77ef8b15356faff375dc0f7c22e1570)

![图片](https://note.youdao.com/yws/res/6776/WEBRESOURCEd8ddf4b02bb078197ea15ec7558247ea)

![图片](https://note.youdao.com/yws/res/6790/WEBRESOURCEc202e85642916e2caa4e30e90e9688d1)

![图片](https://note.youdao.com/yws/res/6803/WEBRESOURCEae2b1d0327043f3614b2fce2320c2950)

![图片](https://note.youdao.com/yws/res/6819/WEBRESOURCEd58023643d02751818f28e4232b86232)

![图片](https://note.youdao.com/yws/res/6868/WEBRESOURCE078228f56b4164b6042d23386ae8d494)

![图片](https://note.youdao.com/yws/res/6870/WEBRESOURCE7137be810f1a1686ff80cda50676689d)

![图片](https://note.youdao.com/yws/res/6872/WEBRESOURCEa2b20f92819324804f836e7950a4a0f8)

![图片](https://note.youdao.com/yws/res/6997/WEBRESOURCE67203ef5358982e01da725ac64faff2c)

![图片](https://note.youdao.com/yws/res/7283/WEBRESOURCEdd82cfb1a55eac689656fbaed088c9e6)

![图片](https://note.youdao.com/yws/res/7291/WEBRESOURCE3429c3dd738f9f7bb67b6300a5dffaa7)

![图片](https://note.youdao.com/yws/res/7293/WEBRESOURCEe4d01588f253b947dc371cf1542231cc)

![图片](https://note.youdao.com/yws/res/7295/WEBRESOURCEa61043fc8ee3c89dcbf256f1f2c9863d)

![图片](https://note.youdao.com/yws/res/7299/WEBRESOURCEeb9fa371cac04828331eca8ba00242b9)

![图片](https://note.youdao.com/yws/res/7325/WEBRESOURCE8d14684e9a1a25a3f193ac7470487a7d)

![图片](https://note.youdao.com/yws/res/7370/WEBRESOURCE07eccd134798b41060a603aff686b2a3)

![图片](https://note.youdao.com/yws/res/7384/WEBRESOURCE0cc4e011526b47f31b2dee8f1d53de51)

![图片](https://note.youdao.com/yws/res/7391/WEBRESOURCE1d8fd284cb02d42dabe3367696b8d536)

![图片](https://note.youdao.com/yws/res/9525/WEBRESOURCE61184edf32f58b4cb87078c6b98fa1ff)

![图片](https://note.youdao.com/yws/res/9900/WEBRESOURCE07ad6c3303bd77942781f496d19703fc)

![图片](https://note.youdao.com/yws/res/7668/WEBRESOURCEf70a968e984a0578e8df625bd187fa34)

![图片](https://note.youdao.com/yws/res/7666/WEBRESOURCEdf32262d27ee6a6cafb6a4796c4cf683)

![图片](https://note.youdao.com/yws/res/7672/WEBRESOURCE88436878080077ee055d0a549ff80fba)

![图片](https://note.youdao.com/yws/res/7677/WEBRESOURCEa604f7c1fc7259aba6d4d07544e9c1b0)

![图片](https://note.youdao.com/yws/res/8784/WEBRESOURCEaadd900c2634e3cb53542aa62d0a8f52)

![图片](https://note.youdao.com/yws/res/8772/WEBRESOURCE276c426d66d3661c9387a57cb889514b)

![图片](https://note.youdao.com/yws/res/8777/WEBRESOURCE1d975582f5364a8ce92db2b4ff115076)

![图片](https://note.youdao.com/yws/res/8789/WEBRESOURCE850a4245841e1472ee0f22623f42e5ee)

![图片](https://note.youdao.com/yws/res/7833/WEBRESOURCE95a0df8319cb62accd7f76ce19172562)

![图片](https://note.youdao.com/yws/res/7837/WEBRESOURCE95a862d6b102514737c57e405e25b19f)

![图片](https://note.youdao.com/yws/res/7893/WEBRESOURCE4c6e80e2f730faa63d201def1da2425a)

![图片](https://note.youdao.com/yws/res/7951/WEBRESOURCE8fc2e440b5b7c0fbd2f0b2848f374828)

![图片](https://note.youdao.com/yws/res/10551/WEBRESOURCE9c543c3e77b02c0cfac05e46c18747a6)

![图片](https://note.youdao.com/yws/res/10555/WEBRESOURCE435edaf68dda3428802d1b04d2fd671d)

![图片](https://note.youdao.com/yws/res/10560/WEBRESOURCE95a15f6c4d63e114f2a0d732adc3d983)

![图片](https://note.youdao.com/yws/res/10563/WEBRESOURCE659468463c317d6fc670042cfd180eb9)

![图片](https://note.youdao.com/yws/res/7917/WEBRESOURCE80c33cb725f7f21be7beb06a3cb5144a)

![图片](https://note.youdao.com/yws/res/7933/WEBRESOURCEe5176a0dd0b206ac1a3c872d01861631)

![图片](https://note.youdao.com/yws/res/8041/WEBRESOURCE6ed622d17635ce38a3da18f380d6e701)

![图片](https://note.youdao.com/yws/res/8043/WEBRESOURCE43495c287c455a54e1bc304c7084743f)

![图片](https://note.youdao.com/yws/res/8030/WEBRESOURCEbdc487e46454cc46dfef089eae46aac0)

![图片](https://note.youdao.com/yws/res/8046/WEBRESOURCE7b68765578fd010512edb99aca215542)

![图片](https://note.youdao.com/yws/res/8052/WEBRESOURCEed8b23ff1337f9a1396ddf53540e0903)

![图片](https://note.youdao.com/yws/res/8056/WEBRESOURCEf16f56dfd1ab0b9f46d8b591edfdcde9)

![图片](https://note.youdao.com/yws/res/8067/WEBRESOURCEd938e6d47b3a68423850142a2bfe5ce6)

![图片](https://note.youdao.com/yws/res/8070/WEBRESOURCEd21e795c84c68bd52de9a9523902c844)

![图片](https://note.youdao.com/yws/res/8076/WEBRESOURCE8f8f1df9732f5a87c2b12a93b32877a9)

![图片](https://note.youdao.com/yws/res/8087/WEBRESOURCEfd051bf694f3b233b0002733581071bb)

![图片](https://note.youdao.com/yws/res/9067/WEBRESOURCE1db526e5b8c3e17ed11a8bc2c884aeca)

![图片](https://note.youdao.com/yws/res/9064/WEBRESOURCE807efff6cf4f829ca47fb434fc09dee5)

![图片](https://note.youdao.com/yws/res/7964/WEBRESOURCE7d7e4cf5ffa8c45f5d55ef094db30db2)

![图片](https://note.youdao.com/yws/res/7981/WEBRESOURCE44d477a737202c48a51bfe308aa5ab4a)

![图片](https://note.youdao.com/yws/res/7983/WEBRESOURCE6c099ea60abf9f58039305d3a10e0dc1)

![图片](https://note.youdao.com/yws/res/7989/WEBRESOURCE4337e03de8e00bbf0ba8e5621487141b)

![图片](https://note.youdao.com/yws/res/7995/WEBRESOURCEbcc1209ee38a3bffffad9a1c5896221a)

![图片](https://note.youdao.com/yws/res/8000/WEBRESOURCEa92bb970b50e2223a11ac38d710fdeda)

![图片](https://note.youdao.com/yws/res/8101/WEBRESOURCEdf7f8c795cd81e19a27990639149c2a6)

![图片](https://note.youdao.com/yws/res/8105/WEBRESOURCE37d8272304a41f37ae5e460899725f6a)

![图片](https://note.youdao.com/yws/res/8107/WEBRESOURCEf132900d9ddd77ef3a795822fdc3ba18)

