<!--
📝 笔记信息
创建时间: 2021-12-01 20:52:22
修改时间: 2022-02-22 21:06:52
原始文件: JS 类 和 对象.note
数据来源: 有道云笔记API
-->

1、 面向对象的特性 



（1）、封装 ：  

                         编写类的过程 称之为 是一个 封装的过程

 function Person(name,age,height,adress) { }

Person.prototype.running = function() { }

![](images/WEBRESOURCEc0d80fe66a564726e72643ea7236217d截图.png)

(2)、 继承 ： 



    1、重复利用一些代码 （对代码的复用 ）

    2、继承是多态的前提  



（3）、多态 ：  不同的对象在执行时表现的不同状态



2、 原型链的理解 



![](images/WEBRESOURCEb0af721b22285bd84b4371c56bdf6e05截图.png)



![](images/WEBRESOURCE0a2d6067cfdb3f0bf5c3d28f1bea0195截图.png)



![](images/WEBRESOURCE72c7787ad905e54c484851cb049de3ba截图.png)



      所有 类 的 父类  -----   Object 





