<!--
📝 笔记信息
创建时间: 2021-06-28 23:39:04
修改时间: 2022-12-05 19:03:57
原始文件: TS 学习笔记.note
数据来源: 有道云笔记API
-->



所有可以赋值给 any类型 的变量， 都可以赋值给 unknown类型 的变量  （类型安全的any类型） 类型窄化



      顶层类型  （Top Type   所有其他类型的超类型 ） 和  底层类型  （Bottom Type）

any  和   unknown                                                                    never(从未出现的类型；不会有值) -- 总会抛出错误的函数  /  从来不会有返回值的函数

                                                  申明为 void 类型的变量，只能赋予 undefined 和 null。



![](images/WEBRESOURCEf0cd6fa4c49101f556b06c0bc3a8f31d截图.png)



extends  和  implements

extends 继承： 类 和 接口 都能 继承          （extends  不一定要强制满足继承关系 ,  也可以是 检查是否满足结构兼容性 ）

                                                                       T extends U ? X : Y  表示  若 T 能够赋值给 U，那么类型是 X，否则为 Y。

![](images/WEBRESOURCE6e6b1bc47a9f7c70b31fe8858bb700e2截图.png)

![](images/WEBRESOURCEad386a9c98cfa513ecdbcf31b5d5c8e6截图.png)

![](images/WEBRESOURCE26bbe8d133d3a5190b76a44d3c2a9f4e截图.png)

class 不能 extends interface

![](images/WEBRESOURCE3466618dcdfed3b55c9f79bb13631457截图.png)



![](images/WEBRESOURCE4658f93748a923d373fd1f66c28e3ed7截图.png)

  

implements 实现： 只有 类 能 实现 A implements B，A 上要有 B 对应的属性和方法

![](images/WEBRESOURCEfba957ba20a1862cb2ebb5f111cd7b13截图.png)

类和类 之间：

![](images/WEBRESOURCEdeef5fa1a70093357aba3c79f2c5ad11截图.png)

类和接口 之间：

![](images/WEBRESOURCEc48046e9c8b3f7905e8fc9d7dadc0eb5截图.png)



1、TS的本质：



![](images/WEBRESOURCE0bb4555ef71546b8e26446f66cd37f76截图.png)

2、TS更加可靠：



![](images/WEBRESOURCE8ce3d414dc57d6700cee56ff90ae6dd3截图.png)

3、面向接口编程：



![](images/WEBRESOURCE7b2704825f6631bb90da3c025a768d08截图.png)



![](images/WEBRESOURCE58b2e27fb0fa4d6e5339f366666b41f2截图.png)

4、TS正成为主流：

![](images/WEBRESOURCE4aa456a1199214861245019a17309c50截图.png)



模块学习：



![](images/WEBRESOURCE8530405fa640457822a9029d40540db6截图.png)



![](images/WEBRESOURCE69742ffcc609fec01a0dc9ea4b313832截图.png)



![](images/WEBRESOURCEd1b619d5e0c99ef605a44ac3d2e8082a截图.png)



![](images/WEBRESOURCE35ded901532a5cc25c9f4f95e2594e2d截图.png)





5、索引签名 

interface

![](images/WEBRESOURCE7a35d33ac78c31301a8c0b6d5c6915c1截图.png)

   key值是number ， value 值 也是 number

![](images/WEBRESOURCE8734c0afba728cec793a4bb3c9138de5截图.png)

索引签名的一个坑----结合 keyof 关键字： 

#### 1、先记住一个点 ： 默认（一般）情况下，索引签名的 key 是 string 的时候，keyof 的返回值是 string | number （联合类型）



是不关这个value（number）的事的，取 number / string / any ... 都不影响 keyof 的返回值是 string | number



![](images/WEBRESOURCE3245e044ff434fdd47d74a5cd7bba55f截图.png)

当在 ts config 中开启了 keyofStringsOnly 的时候

![](images/WEBRESOURCEc67db04c5208d04fb7dd91d1f70ead1f截图.png)

![](images/WEBRESOURCEe8c8cb9eb320047aba7698b9aea9d66e截图.png)

就变成了这样： 就是 string 了。

![](images/WEBRESOURCE63ae59775d0d3407be43d724746e8f03截图.png)

或者在不开启 keyofStringsOly 的情况下，就用高级类型（工具类）限制下

![](images/WEBRESOURCE414437dbf1185d26a47a7a44aca71ed6截图.png)

索引签名 key 为 number 就走正常的类型约束了

![](images/WEBRESOURCEde864757454b8be27fe9e40ae8f521c7截图.png)



#### 2、keyof []  也是有 number 值的 （因为一系列方法最后的显示结果都是比如 'slice' 的字符串形式，所以可以理解为是 string 的话就有多一个 number 的选项了）

![](images/WEBRESOURCE2f076f8d0afe8613c994148af4334b1a截图.png)





type 

1)、

![](images/WEBRESOURCE1e57894b0dd9d66462714e61737d86c8截图.png)

![](images/WEBRESOURCEfd83756a9a3342b1311a441334e8be2b截图.png)

type 和 interface 的 区别：  

相同：

（1）、都用来定义 对象 或 函数 (本质也是对象)

（2）、都可以继承

（3）、



不同：

（1）、写法不一样： type 是 类似 赋值（type + 名字 + = + 值） ,   interface 是 名字 直接 接对象，然后对象里面声明字段

![](images/WEBRESOURCE0f4bf197bd98524bad01ba1c125799fc截图.png)

（2）、继承写法不一样： type 是 &， interface 是 extends

![](images/WEBRESOURCE424b8ed0a26f4aa1634beacf2cfd82e5截图.png)

（3）、interface 可以重复声明（会合并声明）， type 不行（会报错）

![](images/WEBRESOURCEae784c0a49e76907e8e49258eb6dc0bd截图.png)

（4）、type 定义 基本类型 / 联合类型 / 元组类型 / typeof + 数据类型





2)、或者使用 Record 

![](images/WEBRESOURCE72d24ef2bd8217224988d19f407bb78b截图.png)



![](images/WEBRESOURCE912ac39c0761cb9d6368c944d4929759截图.png)



6、泛型变量 ：   （< >： 指定泛型变量约束）  

![](images/WEBRESOURCE5e30be1b754d59bf6394ff3e57fc802d截图.png)



![](images/WEBRESOURCE9e430684946312e8221c01e3fbbccf52截图.png)

          K（Key）          表示对象中键的类型

          V（Value）       表示对象中值的类型

          E（Element）   表示元素类型

          R（Result）      表示结果类型

          U ----   单纯只是因为跟 T 挨的比较近     （T、U、V ...）



![](images/WEBRESOURCE7d490bd604bd1c34590ac3510acdefb7截图.png)

（1）、keyof 关键字（操作符） ：  获取某种类型的所有键，返回类型是联合类型             ------             操作  接口 / 类 / 基本类型

![](images/WEBRESOURCE369e3520ef9b3a1aa31405da5a95cc0e截图.png)

keyof  any 结果 就是 string | number | symbol

![](images/WEBRESOURCE0b1d80ef10044a6a1d53130633ba637b截图.png)



![](images/WEBRESOURCEe20e2edadb1d11e5e7a0f2da73d5ee8c截图.png)



![](images/WEBRESOURCE77968c5cf5b57f4608d354c28765f14d截图.png)

结果总结： 

![](images/WEBRESOURCEad7269c6a74d16b2cb8484321457ab36截图.png)

（2）、typeof 关键字（操作符） ：  获取 变量 的类型，返回： 变量什么类型，就返回什么类型             ------             操作 变量

![](images/WEBRESOURCE7d08d8ea005e1518ea23001317fd9578截图.png)



（3）、infer 关键字 （和 extends（能够赋值给）一起使用）     -----------  推断         声明一个类型变量，并且对它进行使用  （在调用的时候传递的）

类型提取：  可以从 泛型 和 函数 中提取类型

1、提取泛型的类型：

![](images/WEBRESOURCE8ca0c262b37224de313126a2a8a4e314截图.png)

![](images/WEBRESOURCE7dbc9baadf04a03ae3476f5862b52767截图.png)

![](images/WEBRESOURCEe2896a060be911dbc0a127a61863858a截图.png)

（4）、ReturnType 关键字：    获取 方法 的 返回类型     -----  操作方法 

![](images/WEBRESOURCEebd267f701ac3365a748b2f92fcaa3d3截图.png)



![](images/WEBRESOURCE6bcab45a8298ed97af4820327a590093截图.png)



![](images/WEBRESOURCEa839fff2f16c25b08c209f5107166569截图.png)



函数的剩余参数和展开参数

![](images/WEBRESOURCE946ac22aaca719d1b100f1db2bb0f733截图.png)

 







（5）、infer 关键字 

 