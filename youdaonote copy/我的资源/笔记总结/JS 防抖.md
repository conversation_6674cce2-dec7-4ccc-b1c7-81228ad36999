<!--
📝 笔记信息
创建时间: 2021-06-27 17:44:57
修改时间: 2021-06-28 08:07:42
原始文件: JS 防抖.note
数据来源: 有道云笔记API
-->

防止表单多次提交的案列：

![](images/WEBRESOURCE0b6d2f37e7d9d178d6fee0d4d3a6bcba截图.png)



![](images/WEBRESOURCEfd6cece71699d24f6120adb1fa31d8d6截图.png)

1、函数中返回函数，一开始haimei调用就执行

2、不能清除一个没有定义的变量名（let timer = setTimeout）

3、作用域链（闭包解决不能访问timer问题） （let timer 写 到return function上面）

4、防抖函数this指向Window，在setTimeout前就把this保存下来