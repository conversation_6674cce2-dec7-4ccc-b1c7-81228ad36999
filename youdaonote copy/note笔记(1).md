<!--
📝 笔记信息
创建时间: 2021-06-20 10:32:49
修改时间: 2021-06-27 13:42:58
原始文件: note笔记(1).note
数据来源: 有道云笔记API
-->

首页适配布局（宽度布局方式）：



方案一：

（1）屏幕尺寸 <= 1600px，首页宽度为1480px ，左边1190px



左右两栏布局，右边宽度写死（按设计图上的278px），

左边分配  1480/1200 - 278  的剩余所有空间



（2）屏幕尺寸 > 1600px，首页宽度为1200px，左边910px



方案二：

直接百分比布局，没有最大最小分辨率适配，给个最大最小的宽度。

正常显示自适应宽度的范围 ： 1214px ~ 1926px   （ 最小可以显示 :1090px）

分辨率  > 1926px  首页宽度 为 1694px 居中显示

                                        



.header-container {

    min-width: 1214px;

 }



.app-wrapper .main-container-parent {

  min-width: 1214px;

}



创建企业小bug：有token和企业信息地址栏输入/createenterprise/list 依然能进入创建企业页面





有token，不在登录页，有角色





  

::v-deep    深度监听       



::v-deep .el-dialog__header {

  border-bottom: none;

}

























复选框 el-checkbox 只能用数组绑定 ，不能用字符串 （单选才可以）

![](images/WEBRESOURCEec34ee2c5f1e3f736f1360bf71318dbc截图.png)



![](images/WEBRESOURCE26c5448dce41961cb84935d92b228b19截图.png)





回去还是学习为主，自己写DEMO了解下，知道每个生命周期都在干嘛，什么时候触发的

分配好任务，严格要求自己每天的工作量，做之前先把流程图画好，提前沟通，



飞书建立文档，列开发计划，到7.3  可以延后2天







































































  基础： 列个每周学习计划         ts-  vue-    es6

    语雀写写知识总结，还有 demo 练习练习

             一个月时间（把基础的做好）         

          



  业务：遇到不懂的直接问，不用想太多



  多沟通，多交流，在对话中打开自己的思维，  把自己open





花3年学2年的没关系，慢慢来  

自己列学习计划，别等安排咯





简单的没关系，同一个东西不要问超过3次，就是你的问题







（管理后台负责） 成长快点，多沟通 ，产品，测试...

 



 性格： 不用太拘谨，自信点，不会就问（问的太少了），不用在意太多



管别人怎么看，学到的就是自己的



向上管理（交代的任务要有始有终，主动汇报）









