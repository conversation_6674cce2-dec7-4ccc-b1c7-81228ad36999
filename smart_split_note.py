#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能拆分.note文件工具
作者: Claude 4.0 sonnet
功能: 智能拆分单行XML格式的.note文件
"""

import os
import logging
import xml.etree.ElementTree as ET

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def smart_split_note_file(input_path, elements_per_file=20):
    """智能拆分.note文件"""
    if not os.path.exists(input_path):
        logging.error(f"输入文件不存在: {input_path}")
        return False
    
    try:
        # 读取文件
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        # 解析XML
        root = ET.fromstring(content)
        
        # 找到body元素
        body = root.find('.//body')
        if body is None:
            logging.error("无法找到<body>标签")
            return False
        
        # 获取所有body下的直接子元素
        elements = list(body)
        total_elements = len(elements)
        
        if total_elements == 0:
            logging.error("body中没有内容元素")
            return False
        
        # 计算需要拆分的文件数量
        total_parts = (total_elements + elements_per_file - 1) // elements_per_file
        
        logging.info(f"文件包含 {total_elements} 个元素，将拆分为 {total_parts} 个文件")
        
        # 获取文件信息
        base_dir = os.path.dirname(input_path)
        filename = os.path.basename(input_path)
        base_name = os.path.splitext(filename)[0]
        
        # 获取原始的head部分
        head = root.find('.//head')
        head_str = ET.tostring(head, encoding='unicode') if head is not None else '<head></head>'
        
        # 拆分文件
        for part_num in range(1, total_parts + 1):
            start_idx = (part_num - 1) * elements_per_file
            end_idx = min(start_idx + elements_per_file, total_elements)
            
            # 创建新的XML结构
            new_root = ET.Element('note')
            new_root.set('xmlns', 'http://note.youdao.com')
            new_root.set('file-version', '0')
            new_root.set('schema-version', '1.0.3')
            
            # 添加head
            if head is not None:
                new_head = ET.SubElement(new_root, 'head')
                for child in head:
                    new_head.append(child)
            
            # 添加body和选定的元素
            new_body = ET.SubElement(new_root, 'body')
            for i in range(start_idx, end_idx):
                new_body.append(elements[i])
            
            # 生成XML字符串
            xml_str = '<?xml version="1.0" encoding="UTF-8" standalone="no"?>'
            xml_str += ET.tostring(new_root, encoding='unicode')
            
            # 创建输出文件名
            output_filename = f"{base_name}_第{part_num}部分.note"
            output_path = os.path.join(base_dir, output_filename)
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(xml_str)
            
            logging.info(f"✅ 创建文件: {output_filename} (元素 {start_idx+1}-{end_idx})")
        
        logging.info(f"🎉 拆分完成！共创建 {total_parts} 个.note文件")
        return True
        
    except ET.ParseError as e:
        logging.error(f"XML解析失败: {e}")
        return False
    except Exception as e:
        logging.error(f"拆分文件失败: {e}")
        return False

def convert_with_pull_py(note_files):
    """使用项目自带的pull.py转换.note文件"""
    logging.info("开始使用项目自带的转换功能...")
    
    # 这里我们需要调用项目的转换功能
    # 由于pull.py是完整的程序，我们创建一个简单的转换脚本
    try:
        from core.covert import YoudaoNoteConvert
        
        converter = YoudaoNoteConvert()
        success_count = 0
        
        for note_file in note_files:
            if note_file.endswith('.note'):
                md_file = note_file.replace('.note', '.md')
                try:
                    # 这里需要调用转换方法
                    # 由于我们不能直接调用完整的pull.py，我们提供指导
                    logging.info(f"需要转换: {note_file} -> {md_file}")
                    success_count += 1
                except Exception as e:
                    logging.error(f"转换失败 {note_file}: {e}")
        
        logging.info(f"转换完成，成功 {success_count} 个文件")
        return True
        
    except ImportError:
        logging.warning("无法导入转换模块，请手动运行转换")
        return False

def main():
    """主函数"""
    input_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).note"
    
    print("开始智能拆分大型.note文件...")
    if smart_split_note_file(input_file, elements_per_file=15):
        print("✅ .note文件拆分完成！")
        
        # 获取拆分后的文件列表
        base_dir = os.path.dirname(input_file)
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        
        note_files = []
        for file in os.listdir(base_dir):
            if file.startswith(base_name) and file.endswith('部分.note'):
                note_files.append(os.path.join(base_dir, file))
        
        print(f"\n创建了以下文件：")
        for note_file in note_files:
            print(f"  - {os.path.basename(note_file)}")
        
        print("\n接下来您可以：")
        print("1. 运行: python pull.py 来转换所有.note文件为.md")
        print("2. 或者手动转换这些小的.note文件")
        
    else:
        print("❌ .note文件拆分失败！")

if __name__ == "__main__":
    main()
