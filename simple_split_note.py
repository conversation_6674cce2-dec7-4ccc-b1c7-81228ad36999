#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的.note文件拆分工具
作者: Claude 4.0 sonnet
功能: 按行数拆分大型文件
"""

import os
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def split_file_by_lines(input_path, lines_per_file=1000):
    """按行数拆分文件"""
    if not os.path.exists(input_path):
        logging.error(f"输入文件不存在: {input_path}")
        return False
    
    try:
        # 读取文件
        with open(input_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        total_lines = len(lines)
        total_parts = (total_lines + lines_per_file - 1) // lines_per_file
        
        logging.info(f"文件包含 {total_lines} 行，将拆分为 {total_parts} 个文件")
        
        # 获取文件信息
        base_dir = os.path.dirname(input_path)
        filename = os.path.basename(input_path)
        base_name = os.path.splitext(filename)[0]
        
        # 拆分文件
        for part_num in range(1, total_parts + 1):
            start_idx = (part_num - 1) * lines_per_file
            end_idx = min(start_idx + lines_per_file, total_lines)
            
            # 创建输出文件名
            output_filename = f"{base_name}_第{part_num}部分.note"
            output_path = os.path.join(base_dir, output_filename)
            
            # 写入分片
            with open(output_path, 'w', encoding='utf-8') as f:
                f.writelines(lines[start_idx:end_idx])
            
            logging.info(f"✅ 创建文件: {output_filename} (行 {start_idx+1}-{end_idx})")
        
        logging.info(f"🎉 拆分完成！共创建 {total_parts} 个文件")
        return True
        
    except Exception as e:
        logging.error(f"拆分文件失败: {e}")
        return False

def create_markdown_from_text(text_content, part_num, total_parts, original_filename):
    """从文本内容创建简单的Markdown"""
    # 提取时间信息
    import re
    time_match = re.search(r'\((\d{4}-\d{2}-\d{2})\s+(\d{2})(\d{2})\)', original_filename)
    if time_match:
        date_str = time_match.group(1)
        hour_str = time_match.group(2)
        minute_str = time_match.group(3)
        full_time_str = f"{date_str} {hour_str}:{minute_str}:00"
    else:
        full_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    header = f"""<!--
📝 笔记信息
创建时间: {full_time_str}
修改时间: {full_time_str}
原始文件: {original_filename}
数据来源: 有道云笔记导出
文件分片: 第{part_num}部分，共{total_parts}部分
-->

# 知识点大杂烩 - 第{part_num}部分

## 原始内容

```xml
{text_content}
```

---

**注意**: 这是原始的有道云笔记XML格式内容。如需要更好的阅读体验，建议使用专门的有道云笔记查看器或转换工具。

"""
    return header

def split_and_create_markdown(input_path, lines_per_file=500):
    """拆分文件并创建Markdown版本"""
    if not os.path.exists(input_path):
        logging.error(f"输入文件不存在: {input_path}")
        return False
    
    try:
        # 读取文件
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按行拆分
        lines = content.split('\n')
        total_lines = len(lines)
        total_parts = (total_lines + lines_per_file - 1) // lines_per_file
        
        logging.info(f"文件包含 {total_lines} 行，将拆分为 {total_parts} 个Markdown文件")
        
        # 获取文件信息
        base_dir = os.path.dirname(input_path)
        filename = os.path.basename(input_path)
        base_name = os.path.splitext(filename)[0]
        
        # 拆分并创建Markdown文件
        for part_num in range(1, total_parts + 1):
            start_idx = (part_num - 1) * lines_per_file
            end_idx = min(start_idx + lines_per_file, total_lines)
            
            # 获取这部分的内容
            part_lines = lines[start_idx:end_idx]
            part_content = '\n'.join(part_lines)
            
            # 创建Markdown内容
            markdown_content = create_markdown_from_text(part_content, part_num, total_parts, filename)
            
            # 创建输出文件名
            output_filename = f"{base_name}_第{part_num}部分.md"
            output_path = os.path.join(base_dir, output_filename)
            
            # 写入Markdown文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            logging.info(f"✅ 创建Markdown文件: {output_filename} (行 {start_idx+1}-{end_idx})")
        
        logging.info(f"🎉 拆分完成！共创建 {total_parts} 个Markdown文件")
        return True
        
    except Exception as e:
        logging.error(f"拆分文件失败: {e}")
        return False

def main():
    """主函数"""
    input_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).note"
    
    print("选择处理方式:")
    print("1. 拆分为多个.note文件")
    print("2. 拆分为多个.md文件（推荐）")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        if split_file_by_lines(input_file, lines_per_file=1000):
            print("✅ .note文件拆分完成！")
        else:
            print("❌ .note文件拆分失败！")
    
    elif choice == "2":
        if split_and_create_markdown(input_file, lines_per_file=300):
            print("✅ Markdown文件拆分完成！")
        else:
            print("❌ Markdown文件拆分失败！")
    
    else:
        print("无效选择，默认创建Markdown文件...")
        if split_and_create_markdown(input_file, lines_per_file=300):
            print("✅ Markdown文件拆分完成！")
        else:
            print("❌ Markdown文件拆分失败！")

if __name__ == "__main__":
    main()
