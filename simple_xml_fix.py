#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

print("🔧 开始XML修复...")

input_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).note"
output_file = "youdaonote/我的资源/笔记总结/知识点大杂烩_修复版.note"

print(f"📁 输入文件: {input_file}")
print(f"📁 输出文件: {output_file}")

if not os.path.exists(input_file):
    print(f"❌ 输入文件不存在")
    exit(1)

print(f"✅ 输入文件存在")

# 读取文件
with open(input_file, 'r', encoding='utf-8') as f:
    content = f.read()

print(f"📊 文件大小: {len(content)} 字符")

# 查找问题
pattern = r'<list-item level="(\d+)"(?!\s+list-id)'
matches = re.findall(pattern, content)
print(f"🔍 找到 {len(matches)} 个缺少list-id的list-item")

if len(matches) > 0:
    # 修复
    list_id = "rhfO-1668067544614"
    
    def replace_func(match):
        level = match.group(1)
        return f'<list-item level="{level}" list-id="{list_id}"'
    
    new_content = re.sub(pattern, replace_func, content)
    
    # 写入文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"✅ 修复完成: {output_file}")
    
    # 验证
    fixed_count = len(re.findall(r'<list-item level="\d+" list-id="[^"]+"', new_content))
    print(f"📊 验证：现在有 {fixed_count} 个正确的list-item")
else:
    print("✅ 无需修复")

print("🎉 完成")
