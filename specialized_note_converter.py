#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门的.note文件转换工具
作者: Claude 4.0 sonnet
功能: 专门转换知识点大杂烩 (2021-08-06 1313)(1).note文件
"""

import os
import re
import html
from xml.etree import ElementTree as ET

class SpecializedNoteConverter:
    """专门的笔记转换器"""

    def __init__(self):
        self.images_dir = "youdaonote/我的资源/笔记总结/images"
        self.local_image_map = {}
        self.load_local_images()

    def load_local_images(self):
        """加载本地图片文件映射"""
        if not os.path.exists(self.images_dir):
            print(f"❌ 图片目录不存在: {self.images_dir}")
            return

        # 获取所有本地图片文件
        for filename in os.listdir(self.images_dir):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
                if filename.startswith('WEBRESOURCE'):
                    # 提取资源ID
                    resource_match = re.search(r'WEBRESOURCE([a-f0-9]{32})', filename)
                    if resource_match:
                        resource_id = resource_match.group(1)
                        self.local_image_map[resource_id] = filename

        print(f"✅ 加载了 {len(self.local_image_map)} 个本地图片文件")

    def extract_text_from_para(self, para_element):
        """从para元素中提取文本"""
        text_elem = para_element.find('text')
        if text_elem is not None and text_elem.text:
            # 解码HTML实体
            decoded_text = html.unescape(text_elem.text)
            return decoded_text.strip()
        return ""

    def extract_text_from_heading(self, heading_element):
        """从heading元素中提取文本"""
        text_elem = heading_element.find('text')
        if text_elem is not None and text_elem.text:
            # 解码HTML实体
            decoded_text = html.unescape(text_elem.text)
            level = heading_element.get('level', '1')
            # 生成对应级别的Markdown标题
            prefix = '#' * int(level)
            return f"{prefix} {decoded_text.strip()}"
        return ""

    def process_image(self, image_element):
        """处理图片元素"""
        source_elem = image_element.find('source')
        if source_elem is not None and source_elem.text:
            image_url = source_elem.text.strip()

            # 提取资源ID
            resource_match = re.search(r'WEBRESOURCE([a-f0-9]{32})', image_url)
            if resource_match:
                resource_id = resource_match.group(1)

                # 检查是否有本地图片
                if resource_id in self.local_image_map:
                    local_filename = self.local_image_map[resource_id]
                    local_path = f"images/{local_filename}"
                    return f"![图片]({local_path})"
                else:
                    return f"![网络图片]({image_url})"
            else:
                return f"![图片]({image_url})"
        return ""

    def convert_note_file(self, input_file, output_file):
        """转换.note文件为Markdown"""
        try:
            # 读取文件内容
            with open(input_file, 'r', encoding='utf-8') as f:
                content = f.read()

            print(f"📄 读取文件: {input_file}")
            print(f"📊 文件大小: {len(content)} 字符")

            # 解析XML
            try:
                root = ET.fromstring(content)
            except ET.ParseError as e:
                print(f"❌ XML解析失败: {e}")
                return False

            # 查找body元素
            body = root.find('body')
            if body is None:
                print("❌ 未找到body元素")
                return False

            markdown_lines = []
            markdown_lines.append("# JavaScript知识点大杂烩\n")
            markdown_lines.append("> 转换自有道云笔记\n")
            markdown_lines.append("> 转换时间: 2024年\n\n")

            # 统计计数器
            text_count = 0
            image_count = 0
            local_image_count = 0

            # 遍历所有子元素
            for element in body:
                if element.tag == 'para':
                    text = self.extract_text_from_para(element)
                    if text:
                        markdown_lines.append(f"{text}\n\n")
                        text_count += 1

                elif element.tag == 'heading':
                    heading = self.extract_text_from_heading(element)
                    if heading:
                        markdown_lines.append(f"{heading}\n\n")
                        text_count += 1

                elif element.tag == 'image':
                    image_md = self.process_image(element)
                    if image_md:
                        markdown_lines.append(f"{image_md}\n\n")
                        image_count += 1
                        if 'images/' in image_md:
                            local_image_count += 1

            # 写入输出文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.writelines(markdown_lines)

            print(f"\n🎉 转换完成!")
            print(f"📁 输出文件: {output_file}")
            print(f"📊 转换统计:")
            print(f"   - 文本段落: {text_count} 个")
            print(f"   - 图片总数: {image_count} 张")
            print(f"   - 本地图片: {local_image_count} 张")
            print(f"   - 网络图片: {image_count - local_image_count} 张")
            print(f"   - 本地化率: {local_image_count/image_count*100:.1f}%" if image_count > 0 else "   - 本地化率: 0%")
            print(f"   - 总行数: {len(markdown_lines)} 行")

            return True

        except Exception as e:
            print(f"❌ 转换失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    print("🔧 开始专门转换.note文件...")

    input_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).note"
    output_file = "youdaonote/我的资源/笔记总结/JavaScript知识点大杂烩_专门转换版.md"

    print(f"📁 输入文件: {input_file}")
    print(f"� 输出文件: {output_file}")

    if not os.path.exists(input_file):
        print(f"❌ 输入文件不存在: {input_file}")
        return

    print(f"✅ 输入文件存在")

    converter = SpecializedNoteConverter()

    if converter.convert_note_file(input_file, output_file):
        print("\n✅ 专门转换成功!")

        # 显示文件信息
        if os.path.exists(output_file):
            size = os.path.getsize(output_file)
            print(f"📁 输出文件大小: {size} 字节")

            # 读取并显示前几行
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            print(f"\n📋 文件预览 (前10行):")
            for i, line in enumerate(lines[:10]):
                print(f"  {i+1:2d}. {line.rstrip()}")
        else:
            print(f"❌ 输出文件未生成: {output_file}")
    else:
        print("❌ 专门转换失败!")

if __name__ == "__main__":
    main()
