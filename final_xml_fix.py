#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

print("🔧 开始最终XML修复...")

input_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).note"
output_file = "youdaonote/我的资源/笔记总结/知识点大杂烩_最终修复版.note"

print(f"📁 输入文件: {input_file}")
print(f"📁 输出文件: {output_file}")

if not os.path.exists(input_file):
    print(f"❌ 输入文件不存在")
    exit(1)

print(f"✅ 输入文件存在")

# 读取文件
with open(input_file, 'r', encoding='utf-8') as f:
    content = f.read()

print(f"📊 文件大小: {len(content)} 字符")

# 查找所有list-item元素
all_list_items = re.findall(r'<list-item[^>]*>', content)
print(f"🔍 找到 {len(all_list_items)} 个list-item元素:")
for i, item in enumerate(all_list_items):
    has_list_id = 'list-id=' in item
    print(f"  {i+1}. {item[:50]}... {'✅' if has_list_id else '❌'}")

# 查找缺少list-id的list-item元素
# 匹配 <list-item> 开始但不包含 list-id 的情况
pattern = r'<list-item(?![^>]*list-id)'
matches = re.findall(pattern, content)
print(f"\n🔍 找到 {len(matches)} 个缺少list-id的list-item")

if len(matches) > 0:
    # 修复：为缺少list-id的list-item添加list-id属性
    list_id = "rhfO-1668067544614"  # 使用文件中已存在的list-id
    
    # 替换函数：在<list-item后面添加list-id属性
    def replace_func(match):
        return f'<list-item list-id="{list_id}"'
    
    new_content = re.sub(pattern, replace_func, content)
    
    # 写入文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"✅ 修复完成: {output_file}")
    
    # 验证修复结果
    fixed_list_items = re.findall(r'<list-item[^>]*>', new_content)
    print(f"📊 修复后验证:")
    for i, item in enumerate(fixed_list_items):
        has_list_id = 'list-id=' in item
        print(f"  {i+1}. {item[:50]}... {'✅' if has_list_id else '❌'}")
    
    # 现在尝试转换为Markdown
    print(f"\n🔄 开始转换为Markdown...")
    
    try:
        # 使用有道云笔记的转换工具
        import sys
        sys.path.append('.')
        from core.covert import YoudaoNoteConvert
        
        # 转换修复后的文件
        YoudaoNoteConvert.covert_xml_to_markdown(output_file)
        
        # 检查是否生成了Markdown文件
        md_file = output_file.replace('.note', '.md')
        if os.path.exists(md_file):
            print(f"✅ Markdown转换成功: {md_file}")
            
            # 显示文件信息
            size = os.path.getsize(md_file)
            print(f"📊 Markdown文件大小: {size} 字节")
            
            # 读取并显示前几行
            with open(md_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"📋 文件预览 (前10行):")
            for i, line in enumerate(lines[:10]):
                print(f"  {i+1:2d}. {line.rstrip()}")
                
            # 清理临时文件
            try:
                os.remove(output_file)
                print(f"🗑️ 已清理临时文件: {output_file}")
            except:
                pass
                
            print(f"\n🎉 完整转换流程成功!")
            print(f"📁 最终文件: {md_file}")
            
        else:
            print(f"❌ Markdown转换失败，未生成文件")
            
    except Exception as e:
        print(f"❌ 转换过程出错: {e}")
        import traceback
        traceback.print_exc()
        
else:
    print("✅ 无需修复，所有list-item都有list-id属性")

print("🎉 完成")
