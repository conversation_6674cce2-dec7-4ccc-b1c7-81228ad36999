#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os

print("🔧 测试转换工具...")

input_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).note"
print(f"📁 检查文件: {input_file}")
print(f"✅ 文件存在: {os.path.exists(input_file)}")

if os.path.exists(input_file):
    size = os.path.getsize(input_file)
    print(f"📊 文件大小: {size} 字节")
    
    # 读取前100个字符
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read(100)
    print(f"📄 文件开头: {content}")

print("✅ 测试完成")
