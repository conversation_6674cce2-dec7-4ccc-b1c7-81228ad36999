#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单文本提取器
作者: Claude 4.0 sonnet
功能: 直接从XML中提取文本内容，不依赖复杂的XML解析
"""

import html
import logging
import os
import re
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SimpleTextExtractor:
    """简单文本提取器"""

    def __init__(self):
        pass

    def extract_text_content(self, xml_content):
        """直接从XML字符串中提取文本内容"""
        text_contents = []

        # 使用正则表达式提取所有<text>标签中的内容
        text_pattern = r'<text>([^<]*)</text>'
        matches = re.findall(text_pattern, xml_content)

        for match in matches:
            if match.strip():
                # 解码HTML实体
                decoded_text = html.unescape(match)
                # 清理空白字符
                cleaned_text = re.sub(r'\s+', ' ', decoded_text).strip()
                if cleaned_text:
                    text_contents.append(cleaned_text)

        return text_contents

    def extract_images(self, xml_content):
        """提取图片链接"""
        image_urls = []

        # 提取图片源链接
        image_pattern = r'<source>([^<]+)</source>'
        matches = re.findall(image_pattern, xml_content)

        for match in matches:
            if match.strip() and 'WEBRESOURCE' in match:
                image_urls.append(match.strip())

        return image_urls

    def detect_content_type(self, text):
        """检测内容类型"""
        if not text:
            return "empty"

        # 检测标题
        if any(text.startswith(prefix) for prefix in ['一、', '二、', '三、', '四、', '五、', '六、', '七、', '八、', '九、', '十、']):
            return "main_heading"

        if re.match(r'^\d+[、）)]', text):
            return "numbered_item"

        # 检测代码
        code_indicators = [
            'function', 'var ', 'let ', 'const ', 'return',
            'console.log', 'document.', 'window.',
            '=>', '===', '!==', '&&', '||',
            'Array.', 'Object.', '.push(', '.map(',
            'addEventListener', 'querySelector'
        ]

        code_count = sum(1 for indicator in code_indicators if indicator in text)
        if code_count >= 2 or (code_count >= 1 and any(char in text for char in ['{', '}', '()', ';'])):
            return "code"

        # 检测重要内容（短文本且包含特殊字符）
        if len(text) < 100 and any(char in text for char in ['!!', '**', '强制', '注意', '重要']):
            return "important"

        return "normal"

    def format_as_markdown(self, text_contents, image_urls):
        """将文本内容格式化为Markdown"""
        markdown_lines = []
        image_index = 0

        for i, text in enumerate(text_contents):
            content_type = self.detect_content_type(text)

            if content_type == "main_heading":
                markdown_lines.append(f"## {text}\n\n")
            elif content_type == "numbered_item":
                # 清理数字前缀
                clean_text = re.sub(r'^[（(]?\d+[、）)]\s*', '', text)
                markdown_lines.append(f"### {clean_text}\n\n")
            elif content_type == "code":
                if len(text) > 100 or '\n' in text:
                    markdown_lines.append(f"```javascript\n{text}\n```\n\n")
                else:
                    markdown_lines.append(f"`{text}`\n\n")
            elif content_type == "important":
                markdown_lines.append(f"**{text}**\n\n")
            else:
                markdown_lines.append(f"{text}\n\n")

            # 每隔几个文本内容插入一张图片
            if image_index < len(image_urls) and (i + 1) % 5 == 0:
                markdown_lines.append(f"![图片]({image_urls[image_index]})\n\n")
                image_index += 1

        # 添加剩余的图片
        while image_index < len(image_urls):
            markdown_lines.append(f"![图片]({image_urls[image_index]})\n\n")
            image_index += 1

        return ''.join(markdown_lines)

    def add_header(self, markdown_content, original_filename):
        """添加文档头部"""
        # 从文件名中提取时间信息
        time_match = re.search(r'\((\d{4}-\d{2}-\d{2})\s+(\d{2})(\d{2})\)', original_filename)
        if time_match:
            date_str = time_match.group(1)
            hour_str = time_match.group(2)
            minute_str = time_match.group(3)
            full_time_str = f"{date_str} {hour_str}:{minute_str}:00"
        else:
            full_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        header = f"""# JavaScript 知识点大杂烩

> 📅 创建时间: {full_time_str}
> 📝 数据来源: 有道云笔记导出
> 🎯 内容: JavaScript 前端开发技巧和知识点整理
> 🔧 转换工具: Claude 4.0 sonnet 简单文本提取器

---

"""
        return header + markdown_content

    def convert_file(self, input_path, output_path=None):
        """转换单个文件"""
        if not os.path.exists(input_path):
            logging.error(f"输入文件不存在: {input_path}")
            return False

        try:
            # 读取文件
            logging.info(f"开始读取文件: {input_path}")
            with open(input_path, 'r', encoding='utf-8') as f:
                xml_content = f.read()

            # 提取文本内容
            logging.info("提取文本内容...")
            text_contents = self.extract_text_content(xml_content)

            # 提取图片
            logging.info("提取图片链接...")
            image_urls = self.extract_images(xml_content)

            logging.info(f"提取到 {len(text_contents)} 段文本，{len(image_urls)} 张图片")

            if not text_contents:
                logging.error("未提取到任何文本内容")
                return False

            # 格式化为Markdown
            logging.info("格式化为Markdown...")
            markdown_content = self.format_as_markdown(text_contents, image_urls)

            # 添加头部
            original_filename = os.path.basename(input_path)
            markdown_content = self.add_header(markdown_content, original_filename)

            # 确定输出路径
            if output_path is None:
                base_name = os.path.splitext(input_path)[0]
                output_path = f"{base_name}_简单提取.md"

            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            logging.info(f"转换成功: {input_path} -> {output_path}")
            return True

        except Exception as e:
            logging.error(f"转换失败: {e}")
            return False

def main():
    """主函数"""
    try:
        extractor = SimpleTextExtractor()

        # 转换原始大文件
        input_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).note"
        output_file = "youdaonote/我的资源/笔记总结/JavaScript知识点大杂烩_简单提取版.md"

        print(f"🚀 开始简单提取转换: {input_file}")

        if extractor.convert_file(input_file, output_file):
            print(f"✅ 简单提取转换成功: {output_file}")

            # 显示文件信息
            if os.path.exists(output_file):
                size = os.path.getsize(output_file)
                print(f"📊 转换后文件大小: {size} 字节")

                # 统计行数和内容
                with open(output_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    print(f"📄 总行数: {len(lines)} 行")

                    # 统计内容类型
                    heading_count = len([line for line in lines if line.startswith('#')])
                    code_block_count = lines.count('```\n')
                    image_count = len([line for line in lines if line.startswith('![')])

                    print(f"📋 内容统计:")
                    print(f"   - 标题数量: {heading_count}")
                    print(f"   - 代码块数量: {code_block_count}")
                    print(f"   - 图片数量: {image_count}")

                    # 显示前30行内容
                    print(f"\n📄 文件预览（前30行）:")
                    print("".join(lines[:30]))

                    if len(lines) > 30:
                        print(f"\n... 还有 {len(lines) - 30} 行内容")
        else:
            print("❌ 转换失败！")
    except Exception as e:
        print(f"❌ 转换出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
