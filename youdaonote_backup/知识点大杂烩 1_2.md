160、  关于 el-cascader  懒加载（动态加载）  编辑页 回显时有时候 会  回显不出来数据 的处理方式  （即时更新的问题）    （el-tree el-cascader  el-table  el-select ）

方法还是之前动态加载的方法，

![](images/WEBRESOURCE6ed21e3e5337d35e19278680ef46221d截图.png)

![](images/WEBRESOURCE919c4379ddb7f43f260cebb3c041ffd7截图.png)

![](images/WEBRESOURCEe272d44642f5cb6fbd826c4cd5e3ee91截图.png)

![](images/WEBRESOURCE62f1844efcb019db5f8cb092c40a0038截图.png)

![](images/WEBRESOURCEe96f12abf3da63091ec2840ab90805ee截图.png)

![](images/WEBRESOURCE04dc44636201f239b4de7b9164df3ef7截图.png)

补充： 看帖子说这种方式也能实现，但是点击下拉框出现再消失的时候 就会内容跟着消失了  . 所以 还是 采取了 一开始的 方法 直接placeholder 显示 完事，简单好用，不花里胡哨 . 

![](images/WEBRESOURCEb4b2b08d2c6fcaada05b3f381fc58aeb截图.png)

![](images/WEBRESOURCE4939e54a823a145f0d3ac7084b06d763截图.png)

![](images/WEBRESOURCE115213743b304d27bebdc70ae2a314b9截图.png)

![](images/WEBRESOURCE91ae106069d5bdf42727d6e64a488c54截图.png)

![](images/WEBRESOURCE187028cd37328cde07800d24f5b717a4截图.png)

input placeholder 的样式    一般来说，直接  类名/标签名  +   ::placeholder 就可以了 ， 上面的写法只是兼容不同浏览器的内核， -webkit-input- 标识 webkit 内核的浏览器（常见 谷歌浏览器），其他前缀标识同理

比较奇怪 可能需要 单独 给 input 加类名 ，才能作用到placeholder       input.arco-select-view-input.region::placeholder

![](images/WEBRESOURCE8c827283c8c79735fabd500517cc3310截图.png)

![](images/WEBRESOURCE676dff6ec82c1dfecaa23162d2f4128d截图.png)

哎，是要直接这样写才行，  deep 的位置不对啊 ！！！！！    这样子就可以了  深度选择器的位置写的有问题

![](images/WEBRESOURCEa758c67196483aa7f792c721fbab2ec1截图.png)

161、   只定义没有给默认值 ， 新增的时候就没有该字段 （有赋值的时候才会有）

![](images/WEBRESOURCEb4ac29ccc51f2be6248fa94f1003bcff截图.png)

![](images/WEBRESOURCE85733ebd4040d290085f0eae4a1ecd92截图.png)

![](images/WEBRESOURCE089ce9862ea0dce7639e8cb58571a403截图.png)

![](images/WEBRESOURCEd0f464e0a6319880bb650395e87c0567截图.png)

162、es6 中 export 和 export default  的区别

1. export与export default均可用于导出常量 const、函数 function、文件 file、模块 module 等

2. 一个文件中可以 export 多个， 只能 export defalut 一个

3. export 在 import 导入的时候需要加 大括号 { } ，export default 不用

4. export default xxx (变量名) ，在 import 的时候可以给 xxx 重新进行任意命名

![](images/WEBRESOURCEf711951346d85e0d7a3e9a8b6c7d014f截图.png)

![](images/WEBRESOURCEd11d6eb550a7212cbd71c4aeadcd9055截图.png)

![](images/WEBRESOURCEa3c77eb61a6e14628d71633c66ab44fb截图.png)

163、 el-upload 自定义 上传文件的请求方法 将默认的上传行为 action 属性的 上传地址 置为空 再自定义 http-request 方法

![](images/WEBRESOURCE0199459b382dcbc45f322be01d5cb811截图.png)

![](images/WEBRESOURCEbbcdb0a3e608663504702738b064e52e截图.png)

![](images/WEBRESOURCE45bb6677c74ec378dec82eab28e4c89d截图.png)

164、 文件上传改用 sdk 形式 ， sdk 内部封装了上传附件的方法，所以不用自己进行上传 （前端实现 ----  sadais-upload）

（1）、会发两个接口：

![](images/WEBRESOURCE4825803bad3823e359e603e5ea1c1137截图.png)

（2）、第一个是请求的OSS服务器（包括 华为云/腾讯云/阿里云， 参数传 标识），返回的是对应的OSS的信息 （当然请求头要携带token信息和其他标识，不然直接访问就 403 forbbiden 了）

这个请求是自己项目的后端出的接口（后端集成了几种云存储的相关信息，用于请求OSS文件上传的）这种重要信息存在后端比较安全，前端明文显示不安全（所以就请求接口获取）

![](images/WEBRESOURCEde3609390a8af86254f8ad8518c58425截图.png)

接口返回的信息：（包含远程请求OSS的关键信息）

![](images/WEBRESOURCE7b170e488ceccbc3839829ede06a5df0截图.png)

（3）、请求成功后，sdk内部再自己发一个请求（通过 XMLHttpRequest ，小程序环境的话通过 uni.uploadFile 方法）**这个方法就是调用OSS上传文件了**

![](images/WEBRESOURCEda90a5ca1304d1d47b9a4f072c19048d截图.png)

对应到的是这个方法了：

![](images/WEBRESOURCEc3d4ff4b8940d1c2b2022d1e0789aec6截图.png)

这是**核心方法**

![](images/WEBRESOURCEeefb9938ecb3361a09988e48afbbf92c截图.png)

**接口响应**（没有任何东西 就是正常的）

![](images/WEBRESOURCEd0ab3ccad8ee83bd82b2fc705fb2eba1截图.png)

header请求头就是成功的

![](images/WEBRESOURCEd5efc5980ebcde81817f635133cf809c截图.png)

上面代码那个 uploadFilePath 的路径是传到 oss 后前端自己将路径返回给页面显示的，主要组成是

https://file.antibao.cn/file/20230402/xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx.jpg

![](images/WEBRESOURCEcdb6472fe87724e55183d23189695b9b截图.png)

165、  beforeDestory 不生效

![](images/WEBRESOURCEd637902e2b2f67d239cfd8f020904a0c截图.png)

166、  使用到 JSON.parse()  /   JSON.stringify()  地方 记得  try { } catch(e) { console.log(e) }  一下      ** 如果是 JSON.parse() 报错的话，try 和 catch 两个块（两边） 都会走的 ！**

![](images/WEBRESOURCEb4eb953b6c7994a87ec45ed5eca3cef3截图.png)

throw  

![](images/WEBRESOURCE4e19c5594ecd54fd10acf04a65efc9f6截图.png)

167、 filter+ indexOf     filter + some    filter + *includes*   筛选出想要的item组成的数组  循环中若该 item 是 true 则会返回该 item 

168、 html2canvas 插件 下载图片遇到 跨域时的解决：

注意 ： 下载的对象必须是原生的 html 标签

![](images/WEBRESOURCEd50ea0b8cbd4d35e8a857a627ad19142截图.png)

或者 不用原生标签的话呢  要包多一个 div ，然后 ref 绑到 div 上面，这样也可以 

![](images/WEBRESOURCE69f3c2abf9f1ab859c3e52b77dc8a1c8截图.png)

方法的代码： 

![](images/WEBRESOURCE6ad8544ce18eacb95ca7408084be8674截图.png)

设置 crossorigin 为 anomymous   （配合服务端也需要进行 跨域请求 设置）

![](images/WEBRESOURCEd742aaa3a2d2b69bc9d3f11b1af70b5c截图.png)

要改一下参数，这样 img 标签不用设置  crossorigin 属性，不会有跨域报错，也能正常下载了

![](images/WEBRESOURCE18a7ec02d6ad4b25dddaeab99f2e9554截图.png)

![](images/WEBRESOURCE45885c6bc3f2d2999ff3f6db4c98bf04截图.png)

使用到 v-for循环 拿 每一个的 唯一值  动态绑定到 ref 上的话，有问题 ！！！！

要换成 document.getElementById 才行 ！！

列表页 template 中 这样写 ！！！

![](images/WEBRESOURCEadbb1718b99cc29339959f0fdf802f98截图.png)

顺便用 async / await 优化下 promiase.then() 方法： 

![](images/WEBRESOURCEc51066dc6bfecaaeac8366908bdf2cd8截图.png)

多个 await 如何精确 获取/定位 到每一个的错误（如果接口报错）

多个 try...catch 块（ 不优雅） 可以用 .then() 包一下 await 后的 promise ， 组合成 [ err, data ] 格式的函数返回值。

![](images/WEBRESOURCEaeb1f6fe577cf12fcd39346a01e75a30截图.png)

![](images/WEBRESOURCEc5bbc8dc97e0fcd4c7ef960e23ee41cb截图.png)

![](images/WEBRESOURCE09a554047a84f1f4ea628c91106a6ad5截图.png)

# **关于 ****html2canvas****  使用还有几个问题：**

1.  长截图不完整的问题（包含滚动条）： 主要是设置 **height**,** windowHeight**, **width**,** windowWidth** 这几个参数。

![](images/WEBRESOURCEcad7751df12b03baa242e62173549a11截图.png)

![](images/WEBRESOURCE057a2097a69fcd8ba48f7e5780bc9cdc截图.png)

最后恢复到滚动位置：

![](images/WEBRESOURCE2b5c8f25f80423104ca30e87f237da2a截图.png)

2. 转换包含 input框页面时， input 框内文字不居中显示：   锁死 1.0.0 版本即可。

![](images/WEBRESOURCE86bd986f0cc680b189f840a27ecbeace截图.png)

3.** ****html2canvas 本身是通过模拟浏览器渲染来工作的。这种模拟很难 100% 覆盖所有 CSS 细节、字体渲染怪癖以及浏览器自身的复杂行为。**

**4. 如果想要高还原保真的话，推荐后端实现，使用无头浏览器（Puppeteer 等）模拟真实浏览器的实现进行pdf生成方案会更具备高保证效果的！**

169、   el-input  的 type =  "number" 时 去除右边的图表显示

![](images/WEBRESOURCE3c2412f7bd0d799facc57c90b39a60cd截图.png)

 

<style lang="scss" scoped>

::v-deep input::-webkit-outer-spin-button,

::v-deep input::-webkit-inner-spin-button {

  -webkit-appearance: none !important;

  -moz-appearance: none !important;

  -o-appearance: none !important;

  -ms-appearance: none !important;

  appearance: none !important;

  margin: 0;

}

::v-deep input[type='number'] {

  -webkit-appearance: textfield;

  -moz-appearance: textfield;

  -o-appearance: textfield;

  -ms-appearance: textfield;

  appearance: textfield;

}

</style>

170、  js 正则校验是否 包含中文字符 

![](images/WEBRESOURCEe0da270bf35a7f6ce004e13f2b36df53截图.png)

 const reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g')

171、  toFixed(0) 和 parseInt()  保留整数

  toFixed() 是 四舍五入    parseInt() 是 只保留整数 部分

![](images/WEBRESOURCEb7481066742d10eeb5276c697450f333截图.png)

![](images/WEBRESOURCEd8bb826387398ba34a16e6193b37ca60截图.png)

172、  防抖节流的区别  ：： 

1、防抖： 规定时间内只执行最后一次     （规定时间值执行一次，不是需要连续请求的场景，只需要最后一次）

2、节流： 规定时间内只触发一次     （每隔相同时间请求相同资源）

![](images/WEBRESOURCE09e576b2bad8cd23d8a164699d80d935截图.png)

![](images/WEBRESOURCE96c4f30dd9b73ed0457daed30efe6718截图.png)

173、    关于 el-form  的 validate() 回调函数的问题    

进不去 validate 回调 导致 不能保存的 问题 ！！！！！

![](images/WEBRESOURCEc4589d4a2576b7c1b6091855daaa1009截图.png)

 

174、 el-menu 的 collapse 属性     默认会使用 false

![](images/WEBRESOURCE86894d4c6a710b49e9219efd44532050截图.png)

1）、ture 的情况：

![](images/WEBRESOURCE930323c61631fb21cc1804c2e98c15f5截图.png)

2）、 false 的情况：

![](images/WEBRESOURCEd5f577acf5546d2e899462c71ab5ef30截图.png)

175、 关于静态路由的 重定向

 

![](images/WEBRESOURCEf474a997fbdc31a68130037c9fe2736d截图.png)

![](images/WEBRESOURCEcfd9d8e3211b45abd0da409da61a8b5c截图.png)

176、绝对定位 + 弹性盒子

![](images/WEBRESOURCE9113874b772b331e9b4e62f7817d4cef截图.png)

![](images/WEBRESOURCEc9a92bb4c60e34908f7a22b8c8102241截图.png)

177、  数组 map 函数的 使用 ：

（ 1、）

![](images/WEBRESOURCE09a26a9799b58c027bfef42de052bef2截图.png)

（ 2、）

![](images/WEBRESOURCEfbb4651947aee75f218902ca1d7dc748截图.png)

![](images/WEBRESOURCEeb1eb0f611589c03bcbfe4558a60fc4f截图.png)

![](images/WEBRESOURCEa100db00ece8d1c564bbe34eccd50ad5截图.png)

![](images/WEBRESOURCEe720de0ded4030bd26ef674f3f15b974截图.png)

![](images/WEBRESOURCE9beb66d17bc8ecb9e6a14de52c6f9ff4截图.png)

178、  几个常见的名词缩写 ： 

CSR           客户端渲染     client-side-rendering 

SSR           服务端渲染     server-side-rendering

SSG           静态站点生成     static site generation

XSS            跨站脚本攻击     cross-site scripting    ( iframe 会引起 xss )

CORS        跨域资源共享     cross-origin resource sharing

CSRF      跨站请求伪造      cross-site request forgery

CORB  跨域读阻塞    cross-origin read blocking

179、 node-sass       sass-loader         node             版本问题    项目运行报错 多半与这两个插件有关 

  ( 就是 node 14.18.0   /  sass 1.55.0  /  sass-loader 8.0.2   版本问题 ，然后就是 vue.config.js 文件的配置, 然后 /deep/ 换 ::v-deep)   

sass: {

        prependData: `@import "~@/styles/variables.scss";`,

        sassOptions: { outputStyle: 'expanded' },

      },

![](images/WEBRESOURCE8c06d0de9bf046f9aa2b14eafaae6d07截图.png)

element ui **打包后** 在chrome浏览器下偶尔存在 icon乱码   解决方案：

![](images/WEBRESOURCEb132bb21848e3ed9dd47ea570930525f截图.png)

（1）、常见的方式  **下载 node-sass， 卸载 sass    ****（ sass + sass-loader  变成  node-sass + sass-loader  ），  **安装后还要记得替换语法。找到项目的::v-deep替换成/deep/。

**node 版本  和  node-sass 版本  对应表：   （node14 的话 node-sass 下 4.14.0 / 4.14.1 版本）**

![](images/WEBRESOURCE5d8f2206b3df2a8ef186f69c5928faf7截图.png)

node-sass 下载不了：  开代理 / 设置 sass-binary-siste 源 /  -f  npm 强制下载

外网安装  node-sass  地址： ** **[**https://github.com/sass/node-sass/releases**](https://github.com/sass/node-sass/releases)

![](images/WEBRESOURCEccad1ddd3fc8d54374d1f94aa00c30e4截图.png)

set SASS_BINARY_PATH=D:/nodesass/win32-x64-64_binding.node

![](images/WEBRESOURCE42bdb92f347cd3d217b11f5f268121fd截图.png)

![](images/WEBRESOURCE709b660bfe21f933ee5a38fe7ddb9340截图.png)

############             或者可以不用这么麻烦，直接设置 .npmrc 文件的 sass_binary_pass 就好了

sass_binary_site=[https://registry.npmmirror.com/-/binary/node-sass](https://registry.npmmirror.com/-/binary/node-sass)

![](images/WEBRESOURCE293db5db848c0bee52610043f83a2bbc截图.png)

(npm config ls  可以查看)

   （因为elementui 的 package.json 中是 node-sass 和 sass-loader， 而自己项目中是 sass 和 sass-loader ， sass 是依赖 dart-sass，和 node-sass会有兼容性问题）

element 插件：

![](images/WEBRESOURCE167693df1de3421a921390fd3ba17340截图.png)

自己项目：

![](images/WEBRESOURCE1a76a2fbbc094aa1d13785b049b55a01截图.png)

sass 依赖： 

![](images/WEBRESOURCE413c1040bf58654272df20f77ad95a2a截图.png)

![](images/WEBRESOURCE819489e3b72022b0558984b07486d309截图.png)

因为 M1 node-sass 不维护了，然后 rosetta 的方式也行不通（改天有时间可以再研究研究？），所以只能改成 sass 的这种方式 ！！！（而且也是官方推荐的方式）  **node-sass 2020年 就废弃(deprecated)了,推荐用sass(dart-sass)**

（2）、直接升级 sass 和 sass-loader 的 版本号

![](images/WEBRESOURCEc2aebf29aa1b957e25b20dbea38023c2截图.png)

升级到 "sass": "^1.55.0"

"sass-loader": "^8.0.2"

然后删除依赖  再重新安装  （注意：  **这时的 node 版本必须是 14， 而且最好是 14.18.x  ）不然可能其他的14版本也跑不起来 **）

之前是sass-loader是 7.0版本，

![](images/WEBRESOURCE51db7501f6b549899c52e37ed9099006截图.png)

然后stackoverflow查了一波

![](images/WEBRESOURCE86e0247afc56655d768b384757f8976e截图.png)

![](images/WEBRESOURCE8a16b9b13e5bef9527c1f8001ca92ce9截图.png)

最后做的修改：

![](images/WEBRESOURCE04124c63dba1140615036754bf8d9aec截图.png)

# **然后把项目中的所有  /deep/   修改成    ::v-deep ****      就能成功跑起来了。**

总结： 这次的项目（aup-front/portal-frontend 门户前端）总共改了5个地方： 

1. node版本要14.18.2    

2. 注释掉.npmrc文件的内容（只在内网使用到，外网直接默认连的源仓库地址）registry 和 sass_binary_site

3. 修改 vue.config.js 配置文件的 loaderOptions 

css: {

  loaderOptions: {

      sass: {

        *// 向全局sass样式传入共享的全局变量*

        prependData: "$PUBLICPATH: '" +

        process.env.VUE_APP_PUBLICPATH +

        "';" +

        '@import "./src/scss/element-variables.scss";@import "~@/scss/project-variables.scss";',

        sassOptions: { outputStyle: 'expanded' },

      },

    },

}

4. 修改 package.json 文件(删除node-sass, 添加"sass": "^1.55.0",   修改"sass-loader": "^8.0.2")

5. 全局替换, 将项目中用到的 **/deep/** 改为 **::v-deep**

6. npm install，然后 run serve ，完事。

然后就大功告成了。！！！

（3）、修改 webpack 配置文件 （vue.config.js）    -----    实测可行

```javascript
// 将 JS 字符串生成为 style 节点
'style-loader',
// 将 CSS 转化成 CommonJS 模块
'css-loader',
// 将 Sass 编译成 CSS
'sass-loader',
```

![](images/WEBRESOURCEfa5396cfa942005656034afee67dab14截图.png)

 // 有用代码开始


css: {


    loaderOptions: {


      sass: {

        prependData: `@import "~@/styles/variables.scss";`,


       ** sassOptions: {
**

**          outputStyle: 'expanded'**   选项有 expanded 和 compressed （默认），这里是设置的 sass（dart-sass）的output，不是 node-sass 的**
**

**        }**


      }


    }


  },

![](images/WEBRESOURCE9fe3a264c19b0352c7508931ec9fa996截图.png)

![](images/WEBRESOURCE0af4735c166c1e1463dcbea6e3e81add截图.png)

（4）、拆分文件，**按需引入scss 文件  （参考 **271**）      **

import  'element-ui/lib/theme-chalk/src/index.css'

![](images/WEBRESOURCEdda57a66ba7c2bdcceb86deb99fc2832截图.png)

180、  PWA **渐进式网页应用   progressive web application**

![](images/WEBRESOURCE8905cb7ed9d9731ba61856e2af726b56截图.png)

![](images/WEBRESOURCEa43cad735ff644c91eecb2d845931381截图.png)

181、   服务端渲染 ssr  和  nuxt.js  

csr 客户端渲染 （Vue.js / React.js / AngularJs...） client-side-render      直接浏览器解析js代码，再显示回页面上

ssr 服务端渲染 （ vue的nuxt.js （专注 UI 渲染） 用 vuejs的语法写服务端   ） server-side-render   直接服务器返回渲染好的 html 代码， 直接丢给浏览器显示，直接拿html 进行显示，不用解析 js

vue-cli 脚手架  （csr）  和 nuxt.js  (ssr)  创建项目

ssr 好处： 解决首页白屏的问题，SEO搜索引擎优化

链接: [彻底理解服务端渲染](https://github.com/yacan8/blog/issues/30) - SSR 原理

小实践一波：   网站使用 rem 布局

![](images/WEBRESOURCEb6a87beb1b18d40d258fc1484ee58d0c截图.png)

**<nuxt /> **   相当于  **<router-view /> ** 组件

sso: single sign on  单点登录

182、box-orient（盒子方向）        属性规定框的子元素应该被水平或垂直排列。

![](images/WEBRESOURCE598e466cb0df042eb928a721dbeaf150截图.png)

```javascript
box-orient: horizontal|vertical|inline-axis|block-axis|inherit;
```

![](images/WEBRESOURCEedf5080af022ec55a8172e7aa3bf7ce8截图.png)

box-flex（类似flex属性）       指定 box 的子元素是否灵活或固定的大小。

![](images/WEBRESOURCEdf5fdd7aa056f6a2fd11cdee6d76261b截图.png)

![](images/WEBRESOURCE234071272b4b3ad3c48d648e1e75e6c0截图.png)

  box-flex 和  box-orient 就是相当于现在 弹性布局 中的 flex属性 和  flex-direction属性  （是这两个属性的早期版本）

![](images/WEBRESOURCEf517d96929551bca0154904426d950b8截图.png)

![](images/WEBRESOURCEf9b8b3e36540cf54bce4df03f35549da截图.png)

183、    http请求的 request headers 中的 content-type 的类型

一般： post 对应 application/json  

            get 对应  application/x-www-form-urlencoded

还有 form data 类型（一般像文件上传就是用的 form data）

response headers 的 content-disposition 字段，表示响应回复的内容，一般包括 inline 和 filename 两个字段内容，后者携带了返回的文件的后缀名，如果有的话

![](images/WEBRESOURCE258e0a959ea3233d33868fb5086a3540截图.png)

是以**内联**的形式, 还是以**附件**的形式下载并保存到本地。

184、 line-height: 2   自身字体大小（font-size）  的两倍

185、Object.create(null) 创建一个纯净的对象（没有原型的对象）

![](images/WEBRESOURCE1ea62a4a93013e50da5f6fd32cb74348截图.png)

186、symbol 基本数据类型   

![](images/WEBRESOURCE8d2f05e913ddab184f8db9f7972db78c截图.png)

一个Symbol() 返回的是一个唯一的symbol类型的值。 可作为唯一的对象属性的标识符

 

![](images/WEBRESOURCEae05beb504185286842872193f65b2a0截图.png)

![](images/WEBRESOURCE8755847de854889fb35709af2d1c2c54截图.png)

![](images/WEBRESOURCEbd8479a4f56b76f4f22c9f7ad8106691截图.png)

![](images/WEBRESOURCEffdc978eda99f41c6e453a98ca630ad2截图.png)

187、 ES6   Map

const map = new Map()

map.size

map.set(key, value)

map.get(key)

map.has(key)

map.delete(key)

![](images/WEBRESOURCE16edd873bbe6e69cb6573a11aebb09bf截图.png)

**WeakMap:    **弱引用** **  能解决循环引用的问题   key值 不可枚举

key 必须是对象 ，   值 任意

实现 深拷贝 的时候 可以使用 weakMap

188、**扩展运算符 / 深浅拷贝 / 解构**

let [a,b, c,d, e] =  "hello"   解构这么玩

a: 'h'   e: 'o'

**1、对象/ 数组的 扩展运算符**** [ ...数组 ] ****/ ****{ ...对象 } :   ****如果待拷贝的对象只有一层，那么这时候扩展运算符就是深拷贝 !     如果包含多层引用（大于等于2），则第二层开始的引用就是浅拷贝！**  

![](images/WEBRESOURCEd9cb7b142d2c0b03fe9203952211585d截图.png)

![](images/WEBRESOURCE5de2a512143f501d9e15c253beb78cc4截图.png)

![](images/WEBRESOURCE50d0575800271fc3a2a05debb88daeff截图.png)

（  扩展： 可以使用 JSON.parse(JSON.stringify()) 进行深拷贝 ）

![](images/WEBRESOURCEbe454cc8815c468c0e2a064283214512截图.png)

**2、引用类型 ****直接赋值 （=）**** 那肯定就是浅拷贝了**  （简单类型可以理解为就是深拷贝）

**3、解构 也是一样  **** 对象只有一层：深拷贝 **

** ****对象多层嵌套： 浅拷贝 ****                 **** **** 如果是在 vue 中使用解构的话，在某些时候可能会失去响应式**

**（    Object.assign( {}, soueceObj / 一层数组 ) 也是浅拷贝  ），跟1、一样的，都是解构**

**let a = {**

**  name: 'HB',**

**  age: 18,**

**  address: {**

**    detail: '详细地址'**

**  }**

**}**

**let { name, age, address } = a**

![](images/WEBRESOURCEf3ba76743141f5f59b84baa18bce3c8f截图.png)

189、 Object.keys( ）  和 Object.getOwnPropertyNames( )

可枚举（enumerable）： 属性能否访问的到，如果该属性的 enumerable 为 false，那么 循环 for..in （穷举每一个属性）/   Object.keys()  /  JSON.stringify( ) 则是访问不到该属性的

但是， Object.getOwnPropertyNames( ) 可以访问得到，他是返回所有的属性

Object.create(proto, [ propertiesObject ])

创建一个新对象。第一个参数是一个对象，表示新创建对象的原型对象（里面的方法和属性是增加到创建的对象的原型上的），

                            第二个参数可选，是该对象本身的属性或方法

返回值是一个对象，带着指定的原型对象和属性。

例： Object.create(null) 创建一个纯净的（不带原型）的对象

![](images/WEBRESOURCE7738f7f5e578cd69bd54d5a89c02fb63截图.png)

字面量方式创建空对象 相当于 Object.create(Object.prototype)

![](images/WEBRESOURCE4cfbe482e7d14d94b4766b154c04a2bc截图.png)

![](images/WEBRESOURCEef85ec1847204d6505e75bda5a88a0fc截图.png)

![](images/WEBRESOURCE64c63ffbed662136b7a4dd0644073cd3截图.png)

Object.defineProperty(obj, prop, descriptor)     

descriptor - 描述符    的值： 

**1、configurable**:                ** 默认为 false**，重新定义该属性就会报 redefine 错误，也不能通过 delete 修饰符删除  **  优先级最高  **

![](images/WEBRESOURCE11ce8275769a0c2401c046cb5e793314截图.png)

**设置为 ture 时 就可以 修改了 （重新defineProperty）**

**2、writable**:                       ** 默认为 false**， 当为 true 时，value 才能被 赋值运算符 修改，但是writable为fasle，configurable为true时 value值 还是可以通过 defineProperty 进行修改的

**3、enumerable**:                 **默认为 false**，是否可枚举，为 true 的时候 for...in/Object.keys()/JSON.stringify()/Object.assign()  才能访问的到该属性

**4、value**: 属性的值，        ** 默认为 undefined**，

**5、set**: 属性的setter函数，**默认值为 undefined**，

**6、get**: 属性的getter函数，**默认值为 undefined**，

Object.getOwnPropertyDescriptor(obj, prop)

检索 对象 的 属性描述

![](images/WEBRESOURCEab2fd184907e4d9295982adbc0ca2882截图.png)

Object.setPrototypeOf(obj, prototype)

设置对象的原型上的 属性

![](images/WEBRESOURCE3ff60f4651e953fee6ec3048d661a0b5截图.png)

![](images/WEBRESOURCE71eda69a86b3a798128140b423386ab1截图.png)

Object.getPrototypeOf(obj)   获取  setPrototype设置的 原型上的属性

获取对象的原型上的 属性，如果没有继承属性，则返回 null 

对象的__proto__ 属性也可以获取对象上的原型属性+方法

![](images/WEBRESOURCEbfe2f440bfb333a36c2a6b07555ea3a8截图.png)

冻结对象  Object.freeze(obj)  和  Object.seal(obj)  两者区别 ：   

1、 seal ：  封闭对象  （对象原有属性如果可改那将继续保持该属性可修改，不可增加属性，不可删除已有属性） **传入什么，返回什么**

2、 freeze ：  冻结对象 （完全不能对这个对象做任何操作，该对象原型也不可改） **传入什么，返回什么				**** 把响应式（data中定义的对象）变成非响应式，释放内存！ ----    Object.freeze()   Object.seal() **

**                                                                                                                                                 vue3 是  **markRaw 标记一个对象，使其永远不会转换为 proxy。 const obj = markRaw({})

Proxy 对象 的使用 ：  

是什么？ 主要是  创建一个对象的代理，从而实现基本操作的 拦截 和 自定义 （比如 属性查找、赋值、枚举、函数调用...）

怎么用？ new Proxy(target, handler)  

参数？ target： 被 Proxy 包装的目标对象 （任何类型的对象，包括原生数组、函数、或者另一个代理）

           handler：（通常是）一个属性为函数的 对象，包含Proxy的各个捕获器 traps（一堆特点的捕捉器方法）

![](images/WEBRESOURCEde145a258c59d5a425d4d29fe43d7eec截图.png)

190、 js 的连续赋值

var a = b = { n: 3 }

相当于：

a = {n:3}

b={n:3}

面试题：

![](images/WEBRESOURCEd37a181f08899e26ddc3bf779466601f截图.png)

所以最终的问题是 a.x 和 b.x 的值

由上图可知

// a.x  为 undefined

// b.x  为 { n: 2 }

扩展理解： 

![](images/WEBRESOURCE5a481b360bbdb891576eff91435dfcd8截图.png)

------------->>> 啥比题目：

![](images/WEBRESOURCEe187ddbe2dd340d8c868665ce7f37f83截图.png)

报错的根本原因就是 class 类 中声明的代码总是在严格模式中执行。所以一开始的 window.name 赋值 就是专门用来混淆的，严格模式下是

![](images/WEBRESOURCE618adc518ddaa8e635a688ee2cc31372截图.png)

 

----------------》   严格模式下，函数里面没有的变量还是会往全局作用域上面找，但是是没有 this 这个执行上下文对象的

![](images/WEBRESOURCEdcedf9da2b063923590afbae3c56d253截图.png)

![](images/WEBRESOURCEdea2475c48cccdabd253d90de739cb05截图.png)

![](images/WEBRESOURCE13dd9a073681b2e11a0934cd870b6432截图.png)

严格模式下打印this，结果为 undefined

![](images/WEBRESOURCE8df829f308904ab3de399ce97d912c77截图.png)

![](images/WEBRESOURCE292998d2b77b59e3b3a78fe0a69fd8de截图.png)

然鹅，非严格模式下（正常情况下写的代码）

![](images/WEBRESOURCEb1e79e68a77af4e44de852056da685e7截图.png)

![](images/WEBRESOURCEcb22c65d48ab85b2baa44b8109bdf4ee截图.png)

然后，函数里面 var 的问题

![](images/WEBRESOURCE2119e204e4547879829ceb0952630f8d截图.png)

![](images/WEBRESOURCE718bd1f66286f1e2e47c4b0a066b3425截图.png)

![](images/WEBRESOURCE0831419b4ebab5a1dc48845a7b369634截图.png)

new 一个函数做了什么？ 

1、创建一个空obj

2、实例对象obj的__proto__ 等于 构造函数的 prototype

3、改变 this， 指向 obj

4、构造函数有返回值，实例对象就返回该返回值，没有就返回 obj

---------->>>>  所以，再回到原来的这道坑比题目：

![](images/WEBRESOURCE4e1e2a0032600c99285a92784eae0475截图.png)

上面 *6 是重点

然后 对象 又不一样了

  

![](images/WEBRESOURCE8e24d6aac72ec7837063beacaee1caa2截图.png)

 

---------------------------》》》 有趣的面试题： 

![](images/WEBRESOURCE78124c1e3e00d84d3e138eeae69b41a7截图.png)

191、微信小程序中使用伪元素 伪元素只认得 view 标签 ，直接将伪元素加在引入的组件的标签上，会出现定位的时候有问题！

![](images/WEBRESOURCEcdfe1bb0123b7f0d1f3fda6d086ce81f截图.png)

![](images/WEBRESOURCE92ca977cf6e6126861d3d7fae7141544截图.png)

192、git 合并某个分支上的 单个 commit 

使用 cherry-pick 命令

![](images/WEBRESOURCE0d3496059ee4686f390195cd0c8f52e1截图.png)

**单个****commit****合并**

![](images/WEBRESOURCEdcaf7d1fd3ed09d5647a19aef323dc83截图.png)

![](images/WEBRESOURCE743ef481a9a1db2ee3f6bce95ca56f55截图.png)

## **如何处理冲突**

![](images/WEBRESOURCE49bb1524b30730630a4ff5f3701e2f97截图.png)

## **合并多个连续分支**

![](images/WEBRESOURCE35032f24655c7bfca802c41e0e4c9d72截图.png)

193、  如果相同代码不同执行结果 （比如患者端 / 推广端  和 医生端 的图片预览 关闭 ： 一个可以，一个不可以）在线上/本地 都一样的话， 记得  刷新  清缓存 ！！！！！！ 多半都是缓存问题。

清缓存之前 记得 先 更新（拉）  最新的代码 ！！！！！！！！

![](images/WEBRESOURCEe852b9b6669ebf715d2cdf8759bd7f8d截图.png)

![](images/WEBRESOURCEe6d9524a7d8521356267b3c1b5d1d4b1截图.png)

194、 typeof  的 返回值

![](images/WEBRESOURCE1b3157a70f68051de6f84e2a7fcb0d22截图.png)

数据类型判断：

1、 typeof

2、instanceof

3、constructor （判断 object 不准确） // 修改 constructor 属性 影响判断

![](images/WEBRESOURCE1752219e28ce46636691e13a2f93c0cf截图.png)

constructor 构造函数     跟 new prototype 一起使用

![](images/WEBRESOURCE4ef4f53da7b7be46dbabcb39ff4294bb截图.png)

![](images/WEBRESOURCE3f7f7f0b7415ac43d6fc5b9c2a1645d5截图.png)

可以为除了 null 和 undefined（因为这两者没有相应的构造函数）之外的任何类型指定 

constructor 属性（如 String、Number、Boolean 等）

![](images/WEBRESOURCEad55854de4e9a25825ad8931e18588e4截图.png)

所有函数都有 prototype （原型对象）

![](images/WEBRESOURCE7183b2269f76261b845090de3371f5d6截图.png)

4、Object.prototype.toString.call(xxx) === '[object Array]'   // 判断数组   call / apply 用来改变toString方法的执行上下文

5、Array.isArray() // 判断数组 

call / apply / bind  改变 this 指向  （区别）：

![](images/WEBRESOURCE5bbf19d21a519285ec14ef3d1838087d截图.png)

![](images/WEBRESOURCE82614e1387056fab5fb39ea5c401ffc7截图.png)

![](images/WEBRESOURCE0e17e7acb43d7c6b4f725d8d7bde08d8截图.png)

this 是什么 ？   执行上下文对象

195、 管理后台有关 列表页查询面板的 缓存问题

  因为管理后台的查询条件都是在项目中使用的vuex进行缓存的，所以默认情况下不做任何的处理，切换路由的时候是会缓存上一次的查询条件的，

 如果没有缓存，可能是在 页面路由跳转的时候 router.beforeEach 做了判断，也可能在页面的created/mounted 等初始化函数（方法）中做了处理，

还可能在页面离开（销毁）前做了处理 beforeDestroy / destroy /  deactivated 等声明周期函数中。

-------------------------------》》》》》》》》》》》》

之前的所有后台项目默认都是不使用 keep-alive 进行缓存的（要的话在对应的组件的meta元信息里设置cache为true，默认是false） 

![](images/WEBRESOURCE3cee381337196c3136ebac48bcd08f6a截图.png)

![](images/WEBRESOURCE365805ee8d94b1424efa45ec1f3b9e2e截图.png)

![](images/WEBRESOURCE5d7d9c46c32a98d4575380355afbe8af截图.png)

![](images/WEBRESOURCEdbc72c7ed7715782c821e8181462282d截图.png)

最后表现：

![](images/WEBRESOURCEd1cf84f661cc7f4a08bcb537253a5530截图.png)

![](images/WEBRESOURCEf5956600e965a10ef3796a2b8361e627截图.png)

![](images/WEBRESOURCEb313ed6901578eff8f49044f5d384374截图.png)

只有后面的点梯运营后台/ 神匠 运用了 该 缓存机制 ！！！！

项目中这里的keep-alive有开启的话，那么基本路由前进后退等操作（像保留查询条件信息，保留上一次的操作记录）都是由keep-alive 的缓存来执行的，就不跟vuex什么事了。

而且对配置了 keep-alive 的组件的 handleQuery方法在一个地方统一做了处理，就不用每一个页面都加一个钩子函数 + 调用handleQuery了，直接 mixin 到该页面就好了

![]()

![](images/WEBRESOURCE2e5b4f4368985e0d934b4ed920faa9ae截图.png)

![](images/WEBRESOURCE671c1fda6129f3aab1445f79b0d22dbb截图.png)

![](images/WEBRESOURCEb6eb3a7d1540df9d0ef44c6dccdafcf4截图.png)

------------------------》》》》》》》》》》》》》》》》

然后现在的页面都是默认要缓存查询历史的（存的 vuex 中  /   或者直接在 index.vue 页面中，切换路由没刷新页面，所以再切回去 index.vue 页面，还是保留着之前的查询条件？（只要不手动刷新页面）），除非有特殊情况

就是存的 vuex 中 的 （在 searchValue 对象中，默认form.searchValue.xxx 之后 就多了 xxx 这个属性， searchValue 存在的 vuex 中， 所以不刷新页面的情况下是缓存到的xxx该字段的，  这时候又不关 keep-alive 什么事了）

![](images/WEBRESOURCE7470b04c991755aecd244901ad5741d7截图.png)

![](images/WEBRESOURCEb7b8118171dd40a690abf2af8501ba13截图.png)

所以总的就是说， 缓存（保留查询条件/上一次的操作记录等），keep-alive 和 vuex 都能实现同样的功能，但是实现的方式是不一样的 ！！！ 

![](images/WEBRESOURCEbfb33a3f0690bb2a795f8f89815fef46截图.png)

keep-alive + vuex：   [keep-alive + vuex 让缓存的页面灵活起来](https://juejin.cn/post/6844903825371824142) / 动态修改 include 属性

196、 align-content 和 align-items 区别：

![](images/WEBRESOURCE65457f7086addcd993b95a46b6d0a6e1截图.png)

197、 iframe 和 web-view 区别

1、 iframe是属于 html的一种标签， web-view 是 原生系统，用于移动端嵌入web技术，方式是 内置了一款高性能 webkit内核 浏览器。

2、

![](images/WEBRESOURCEfe54c323f83852710ab17e509023db82截图.png)

app / 小程序 环境没有 XMLHttpRequest 对象 ，只能用 wx.request({})接口 调用后台接口， im 写成 webview 的， app 在 hybrid 里可以用 document 对象

![](images/WEBRESOURCE7905c7d8cec193ed98f5093b68331c9a截图.png)

![](images/WEBRESOURCE12e7b226700f185dd790c3eecef77380截图.png)

![](images/WEBRESOURCE25e339fcc50c4a0464f85849b66b7fe9截图.png)

![](images/WEBRESOURCE1446a62b37c486d3fc5dd56c1a22ea16截图.png)

![](images/WEBRESOURCE041b9903e35a3da50e9914a5bef88dab截图.png)

198、 npm 报   Unsupported platform for n@8.0.2: wanted {"os":"!win32"} (current: {"os":"win32","arch":"x64"})  错误时，暴力点 直接加 --force 搞定 ！

![](images/WEBRESOURCE514a3050cdfc114d5ac96e7672e225bb截图.png)

node 的 n模块，专门用来管理 node 的版本的

然而，并没有什么卵用。，  n模块并不支持 windows 系统 ！！！！  linux / mac 上可以 ！

所以，还是  上官网下载对应的版本覆盖安装叭~

---------->>>  node 的另一个版本管理   ---   nvm   （node version manage  版本管理工具）

一、nvm： 一个单独的软件包， 切换node版本使用， 有 nvm for windows （平时说的nvm就是这个）。nvm 可以在一台电脑上安装多个版本的 nodejs，进行切换，跟 node 是两个独立的安装包

![](images/WEBRESOURCEe585647fcc6a5661424ec81da988f7ed截图.png)

如果cmd报这个错误，

![](images/WEBRESOURCE8d1ffbd399c4a71a764bb687f7131e3a截图.png)

则需要切换到管理员模式， 有了：

![](images/WEBRESOURCE31454e1ae8fc58801fe0ea2efe169d35截图.png)

    --------------------- >>>>>>>>  nvm 常见的命令 ： 

nvm list： 查看当前的 node 版本（* 表示 当前正在使用的版本号）

nvm use xxx（版本号，如 10.18.0）：切换指定的 node 版本

nvm install / uninstall xxx（版本号，如 10.18.0）： 安装/卸载 node 版本

![](images/WEBRESOURCE179d0362a5bc012b3ba65d7013812f4d截图.png)

nrm 啊， 就是用来 切换 npm 的镜像（npm 下载源）用的

![](images/WEBRESOURCE005be122587d87477b68b442b0914fea截图.png)

二、n 模块：一个 npm pakage, 是依赖 npm 进行 全局安装，是 node 中的一个模块 （必须先安装 node 和 npm ），不支持 windows 系统

 

199、 提交到暂存区    （工作区 ---->    暂存区   ------>   仓库）

    git  三个概念：   提交 （commit） ----     仓库  （repository）----      分支 （branch）

![](images/WEBRESOURCE7f082e83873c6cf3b7ce934da6fd6690截图.png)

仓库 分为 本地仓库 和 远程仓库

（1）、 git commit -m ":sparkles: feat: my new modified code"   是提交到 本地仓库  ！！！！    		**提交到本地仓库（commit）的是什么时间  ，  推送（push）到 远程仓库 的就是什么时间  （具体时间以本地仓库的时间为准）**

![](images/WEBRESOURCEa1ccedd6e08018f1bb5f2e162d567792截图.png)

vscode 查看历史 commits  （记录的都是本地的commits）

![](images/WEBRESOURCE9a15c84671d5e3053fcfebc509b582b4截图.png)

提交到 暂存区 后，要撤回的话

![](images/WEBRESOURCE7c00bc094071f76741ec15f3dca45841截图.png)

回撤代码（回滚？）------ 就是撤销修改

（1）git checkout <filename> （文件名）   回撤修改的代码文件

相当于这个 勾勾 的功能

![](images/WEBRESOURCE277479c752c551b7571158620e6a02be截图.png)

![](images/WEBRESOURCE27e1ddc5e77531575cf8f8c8be4ac7fe截图.png)

（2）git checkout master（分支名） 切换到本地名为 master 的分支

 ------------ 回滚 ----------------->>>

（1）、保存修改的代码，未提交暂存区，直接回滚（ git checkout <filename>）

（2）、提交到暂存区的代码，要回滚，使用 commits 的 Undo Commit  （git reset HEAD^） git reset --haed 该分支最后一次成功提交的hash值

（3）、提交到远程仓库了，要回滚。。。笨方法 （找到以前的要修改的那次提交，直接复制那个文件未修改前的代码，修改后重新覆盖提交）

   file history 和 时间线 都提供了相同的功能 ， 找到并修改就可以了。

![](images/WEBRESOURCE0b445ca4f58e9a860f0b9825516d70c8截图.png)

![](images/WEBRESOURCE6fd4b2ba4bc246b44c12ab46e890589f截图.png)

(2)、 

0、直接本地文件夹 git init 然后 撸了一堆代码准备提交到 github后

1、添加 远程仓库 并给远程仓库起个名字为 HHH

```javascript
git remote add HHH https://github.com/Hbin-Zhuang/Front-End-Learning
```

在 1、和 2、之间可能还需要条命令： git branch -M main （如果是github的话？）  

2、然后就可以 push 了  （ HHH： 仓库名， main/master： 远程仓库默认创建名字叫 main/master 的分支）

```javascript
 git push (-u HHH main/master)
```

github 新建好的一个仓库：

![](images/WEBRESOURCE1b725be79aa4026893e82903ff326af8截图.png)

(3)、合并冲突

current change： 当前已存在的

incoming change: 你修改完的

![](images/WEBRESOURCEcae5b813e8f124b2763cb671cc0adae3截图.png)

git merge --abort   放弃此次合并

（4）、删除分支 git branch -d <branch-name>

（5）、vscode 提供的分支图形化操控界面  （不常用，也就那几个命令）

![](images/WEBRESOURCEb74cf30154180b07d83bcbc51c4eda7a截图.png)

（6）、reset  - 提了commit 的回滚

     revert - 提了 remote 的回滚

     rebase - 产生冲突的变基 （--continue 一个冲突解决后进行下一个冲突的解决， --skip 跳过此次变基，--abort 中止/放弃 此次变基）

           每次拉代码的时候都用   **git pull --rebase**   提前解决冲突 ！！！

            cherry-pick - 合并其他分支指定的提交（分支排序没有按照时间线）

     merge - 合并分支（整个分支合并，可能会时间线交叉）

merge 和 cherry-pick  合并的时候之前有合过相同的提交是不会去重的，而是出现为两条一模一样的提交记录 ！

（7）、切换分支前代码做了修改，直接 git stash （保存本地修改的代码，恢复干净的工作目录，即恢复为未修改前的状态，同时又替你stash藏了代码在stash store 中）就可以切换分支了，然后再从其他分支切回来就 git stash pop（恢复stash栈中的代码，每次pop一条）就好了   （git stash apply  是单纯的恢复，不删除stash记录）

![](images/WEBRESOURCEc0f3df6389c3f6504921661e2629be56截图.png)

![](images/WEBRESOURCE0aa5ef77f88ff49f4dfc134bcdddd1bd截图.png)

（8）、git blame -L 查看历史修改记录  ------  追溯谁写的

用法：  **git blame -L <n, m> <filename>**  （指定范围的行数）  filename 文件名 必须是**完整的路径（src/views/knowledge/Knowledge.vue）** 不然定位不到你的那个文件的

每一条提交记录的 id 相当于该条记录的哈希值 ！！！

非 .mit / apache 开源的项目 的 license 文件，使用该项目源码可能需要遵循一些规则。

vscode git 分支 的 一些说明  ： 

![](images/WEBRESOURCE8763ebf1efb07243b96dc1ad1bbd383b截图.png)

![](images/WEBRESOURCE6e9f2b78c1ae3bfa7b35e630bcd5b030截图.png)

(9)、git 修改上次git commit的时间

git commit --amend  --date="commit_time"

git commit --amend --date="Sat, 6 May 2023 08:28:59 +0800"

![](images/WEBRESOURCE62eb4e81ab3218f9e6993974f8b12abb截图.png)

git graph 插件

![](images/WEBRESOURCEd8b9de63b3fab4db453b84adf9b19070截图.png)

但是远程仓库好像没有更新到。。

![](images/WEBRESOURCE702f3df50a776cbad1ee467b6904b4d7截图.png)

但是本地 git log 能打印到：   就是你手动commit --amend 的那个最新提交记录 没有跟 giuhub 上的一样

![](images/WEBRESOURCEb15f3c6b8ad34fc6b3914a09fe7f488a截图.png)

![](images/WEBRESOURCE18b17c8b1638a0dfc42917e5c32ccc22截图.png)

常见的 git 问题：

（1）、切分支报 path invalid  错

![](images/WEBRESOURCE42b0e15d3ec22dba4794172773a37eb6截图.png)

查了下，主要就是 windows 系统的问题，不支持带 : 的文件命名

![](images/WEBRESOURCE580f6f1ec87be43b557d47d9777ff2c9截图.png)

解决： 设置 git config core.protectNTFS false

![](images/WEBRESOURCE5683290b15ceaef6e96077a360aa48de截图.png)

然后再强切，就好了

![](images/WEBRESOURCEb9c1d115143111ecede27fb079805e1e截图.png)

github 找项目 途径： 

1、 github.com/trending

![](images/WEBRESOURCE4c54e32063fe96c058265572cc1e3601截图.png)

2、特殊查找资源的小技巧：

  常用的前缀后缀

 · 找百科大全   awesome xxx    (e.g. awesome vue)

·  找例子  xxx sample  

·  找空项目架子  xxx starter / xxx boilerplate

·  找教程 xxx tutorial

200、 uniapp 适配  html5+ 扩展规范    plus 对象 只有 在 app上才生效

![](images/WEBRESOURCE0607ef95ee8254a50c40362fe22f1b6e截图.png)

[条件编译调用 HTML5+](https://uniapp.dcloud.io/use-html5plus?id=uni-app%e4%b8%8d%e9%9c%80%e8%a6%81-plus-ready)

[html5 runtime](https://www.html5plus.org/doc/zh_cn/runtime.html)

![](images/WEBRESOURCE2befdb79449fe06682a3296fd3871093截图.png)