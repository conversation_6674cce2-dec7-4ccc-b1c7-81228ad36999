{"2": "1", "3": "Ju9C-1621846617594", "4": {"version": 1, "incompatibleVersion": 0, "fv": "0"}, "5": [{"3": "3060-1621846615933", "5": [{"2": "2", "3": "p5PQ-1621846617594", "7": [{"8": "201、 const 定义的对象（引用类型）的值可以改变，定义的基本数据类型不可以改变。"}]}]}, {"3": "BQt9-1645798280516", "4": {"version": 1}, "5": [{"2": "2", "3": "QfjJ-1667182469098", "7": [{"8": "主要是指针（栈内存地址）没有发生改变。 const obj = { }, obj.name = 'HB'  ,  首先先在栈内存中开辟"}]}]}, {"3": "RKBi-1645798381320", "4": {"version": 1}, "5": [{"2": "2", "3": "ue8b-1667182469098", "7": [{"8": "一块空间存放 obj（"}, {"8": " { } ", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": "） 的地址，然后地址指向堆内存空间，存放该地址的值，obj.name 改变的是堆内存"}]}]}, {"3": "9JJ5-1645798502570", "4": {"version": 1}, "5": [{"2": "2", "3": "BfCV-1667182469098", "7": [{"8": "空间，栈内存还是没有改变（指针的指向不变）。"}]}]}, {"3": "KyjB-1753336470685", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12380/WEBRESOURCE9b3ddc6fbbda403d0df552e303da2ba7", "w": 475, "h": 243}, "6": "im"}, {"3": "2mAh-1645798205696", "4": {"version": 1}, "5": [{"2": "2", "3": "Map8-1667182469098"}]}, {"3": "6XfG-1650191262313", "4": {"version": 1}, "5": [{"2": "2", "3": "aCdF-1667182469098", "7": [{"8": "let的一个特性是禁止在同一个作用域下重复声明，", "9": [{"0": "#F33232", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 14, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": "所以以下代码会报错 :"}]}]}, {"3": "AFjk-1753336470686", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12377/WEBRESOURCE10947bbd84fc9e984b1bf5eeb3672f5b", "w": 503, "h": 169}, "6": "im"}, {"3": "8pkR-1650191190077", "4": {"version": 1}, "5": [{"2": "2", "3": "xeAB-1667182469098"}]}, {"3": "gxKh-1659235551568", "4": {"version": 1}, "5": [{"2": "2", "3": "B9Rc-1667182469098", "7": [{"8": "const  定义的变量  必须初始化，不然报错； var 和 let 则不用 。"}]}]}, {"3": "Dsju-1753336470687", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12374/WEBRESOURCE19aa6770d0c978979d0df2dbc89dccc3", "w": 654, "h": 198}, "6": "im"}, {"3": "0ozj-1659235551789", "4": {"version": 1}, "5": [{"2": "2", "3": "oygi-1667182469098"}]}, {"3": "umJ3-1645526435948", "4": {"version": 1}, "5": [{"2": "2", "3": "K3p4-1667182469098"}]}, {"3": "d8F4-1645526436086", "4": {"version": 1}, "5": [{"2": "2", "3": "pL7J-1667182469098", "7": [{"8": "202", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "、  refs 和 document.xxx  获取 dom 的 区别 ："}]}]}, {"3": "jKez-1646296199873", "4": {"version": 1}, "5": [{"2": "2", "3": "cxXv-1667182469099", "7": [{"8": "1）、 ref 用来给"}, {"8": " 元素", "9": [{"0": "#F33232", "2": "c"}]}, {"8": " 或 "}, {"8": "子组件  ", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "注册引用信息", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": "，如果是元素，就是"}, {"8": "引用指向", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": "绑定的元素的DOM，如果是子组件，那就是"}, {"8": "引用指向", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": "子组件的实例 "}]}]}, {"3": "f8Wn-1646300413693", "4": {"version": 1}, "5": [{"2": "2", "3": "OsYC-1667182469099"}]}, {"3": "jTi2-1646300416434", "4": {"version": 1}, "5": [{"2": "2", "3": "hcbh-1667182469099"}]}, {"3": "w8dc-1646300407450", "4": {"version": 1}, "5": [{"2": "2", "3": "Gy3E-1667182469099", "7": [{"8": "用 ref， 不同的组件互相隔离，不存在命名冲突"}]}]}, {"3": "z0uw-1646296200006", "4": {"version": 1}, "5": [{"2": "2", "3": "zM1k-1667182469099", "7": [{"8": "精准匹配，在vue 创建dom 的过程就直接赋予，不需二次查询，理论更快。"}]}]}, {"3": "aaBy-1646300271652", "4": {"version": 1}, "5": [{"2": "2", "3": "59c2-1667182469099"}]}, {"3": "ZTqy-1646300271769", "4": {"version": 1}, "5": [{"2": "2", "3": "VMle-1667182469099"}]}, {"3": "uuTF-1645798734139", "4": {"version": 1}, "5": [{"2": "2", "3": "Nb9i-1667182469099", "7": [{"8": "2）、用原生的方法 document.querySelector / document.getElementById 等方式：  每次用都需要做查询，比较消耗DOM节点的获取，匹配class类名时可能因为多个同名而选不到想要的DOM节点"}]}]}, {"3": "Roks-1646300477446", "4": {"version": 1}, "5": [{"2": "2", "3": "1WZ0-1667182469099"}]}, {"3": "k2u9-1646300477557", "4": {"version": 1}, "5": [{"2": "2", "3": "6ybc-1667182469099"}]}, {"3": "cH8G-1646300478062", "4": {"version": 1}, "5": [{"2": "2", "3": "EOTY-1667182469099", "7": [{"8": "vue中 v-for 循环时 动态设置 ref 会有问题 ！！", "9": [{"0": "#F33232", "2": "c"}, {"0": 26, "2": "fs"}]}]}]}, {"3": "pSoi-1646301490239", "4": {"version": 1}, "5": [{"2": "2", "3": "jAOa-1667182469099"}]}, {"3": "Um8b-1646301521921", "4": {"version": 1}, "5": [{"2": "2", "3": "yZKV-1667182469099", "7": [{"8": "（1）、"}]}]}, {"3": "gIMB-1753336470688", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12375/WEBRESOURCE7b12589b615c9c3778856bdaf88ba155", "w": 681, "h": 290}, "6": "im"}, {"3": "Qpz2-1753336470689", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12378/WEBRESOURCE9a30f2d2912f0a424b2f7b6f24b63203", "w": 758, "h": 56}, "6": "im"}, {"3": "K52H-1753336470690", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12379/WEBRESOURCE1b225a4cc5217082461cb54b94769dc4", "w": 964, "h": 180}, "6": "im"}, {"3": "07Fa-1646301485576", "4": {"version": 1}, "5": [{"2": "2", "3": "D6Bz-1667182469099"}]}, {"3": "Pq41-1646301613742", "4": {"version": 1}, "5": [{"2": "2", "3": "9YVw-1667182469099"}]}, {"3": "F1Ox-1646301529571", "4": {"version": 1}, "5": [{"2": "2", "3": "Lw3e-1667182469099", "7": [{"8": "（2）、"}]}]}, {"3": "caLq-1753336470691", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12376/WEBRESOURCE8f650e03abc3067d4e922a436b7e84d3", "w": 822, "h": 287}, "6": "im"}, {"3": "lfBJ-1753336470692", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12372/WEBRESOURCE19566f5002bf0abc4f5ae9d0d4332408", "w": 821, "h": 52}, "6": "im"}, {"3": "EQfk-1646300031511", "4": {"version": 1}, "5": [{"2": "2", "3": "Apzc-1667182469099"}]}, {"3": "gGoQ-1646300419161", "4": {"version": 1}, "5": [{"2": "2", "3": "7QsW-1667182469099"}]}, {"3": "cE0O-1646300031614", "4": {"version": 1}, "5": [{"2": "2", "3": "YsFe-1667182469099"}]}, {"3": "9ZYI-1646300031768", "4": {"version": 1}, "5": [{"2": "2", "3": "WoNA-1667182469099", "7": [{"8": "203、 a 标签 的使用 "}]}]}, {"3": "uIVT-1646371260928", "4": {"version": 1}, "5": [{"2": "2", "3": "gBwa-1667182469099"}]}, {"3": "6v6P-1646371261684", "4": {"version": 1}, "5": [{"2": "2", "3": "E4vs-1667182469099", "7": [{"8": "_target 属性 ： "}]}]}, {"3": "N4hh-1646371293640", "4": {"version": 1}, "5": [{"2": "2", "3": "0XEh-1667182469100", "7": [{"8": "需求： "}, {"8": "点击一个链接，如果这个链接浏览器已经打开过，则刷新已经打开的链接窗口；如果这个链接没有打开过，则使用新窗口打开这个链接页面。", "9": [{"0": "#F33232", "2": "c"}]}]}]}, {"3": "153L-1646300031881", "4": {"version": 1}, "5": [{"2": "2", "3": "8uwA-1667182469100", "7": [{"8": "只需要 设置target属性值和href属性值一样就好了"}]}]}, {"3": "rPNm-1753336470693", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12368/WEBRESOURCE4272ef831eeaaa9c43a95b93afc1efd3", "w": 873, "h": 36}, "6": "im"}, {"3": "gAuA-1646300032087", "4": {"version": 1}, "5": [{"2": "2", "3": "evhQ-1667182469100", "7": [{"8": "默认 "}, {"8": "_blank", "9": [{"0": "#F33232", "2": "c"}]}, {"8": " 是"}, {"8": "打开一个新的页面", "9": [{"0": "#F33232", "2": "c"}]}]}]}, {"3": "LqLT-1646300032211", "4": {"version": 1}, "5": [{"2": "2", "3": "KKpr-1667182469100"}]}, {"3": "fco2-1646377364182", "4": {"version": 1}, "5": [{"2": "2", "3": "VC0M-1667182469100"}]}, {"3": "ka1z-1646377364306", "4": {"version": 1}, "5": [{"2": "2", "3": "UdNd-1667182469100", "7": [{"8": "download 属性： "}]}]}, {"3": "TSIs-1753336470694", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12373/WEBRESOURCE31011fe9643a10cf3a69e08f19c84de7", "w": 1130, "h": 107}, "6": "im"}, {"3": "S8qZ-1646371444212", "4": {"version": 1}, "5": [{"2": "2", "3": "7G8n-1667182469100"}]}, {"3": "tt6m-1753336470695", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12369/WEBRESOURCE8e68d4896e18c99fe96bb0e3a0ddf027", "w": 692, "h": 68}, "6": "im"}, {"3": "obgX-1646379158481", "4": {"version": 1}, "5": [{"2": "2", "3": "htUc-1667182469100", "7": [{"8": "上面的截图也就是下面的意思。"}]}]}, {"3": "dcXU-1753336470696", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12366/WEBRESOURCEc0105c532bef0474917c8ce120a25f20", "w": 830, "h": 283}, "6": "im"}, {"3": "lyO3-1646818691699", "4": {"version": 1}, "5": [{"2": "2", "3": "2xhD-1667182469100"}]}, {"3": "erxl-1646818692036", "4": {"version": 1}, "5": [{"2": "2", "3": "REL1-1667182469100", "7": [{"8": "图片转 base64 （dataURL） 或者 Blob (   "}, {"8": "new Blob( [ url ] )   ", "9": [{"0": "#F33232", "2": "c"}]}, {"8": ")+ File Reader 对象 "}]}]}, {"3": "50om-1646379158597", "4": {"version": 1}, "5": [{"2": "2", "3": "Qm6W-1667182469100"}]}, {"3": "Sc5M-1646818882325", "4": {"version": 1}, "5": [{"2": "2", "3": "7bD6-1667182469100", "7": [{"8": "base64 （a-zA-Z0-9+/    共 64 个字符）"}, {"8": "，每3个字节一组，共24个二进制位。", "9": [{"0": "#FFEE7C", "2": "bg"}, {"0": "#B620E0", "2": "c"}]}, {"8": "24个二进制位分为4组，每组6个二进制位。", "9": [{"0": "#F8D2FF", "2": "bg"}]}, {"8": "每组前加两个00，扩展成32个二进制位，即4个字节。"}]}]}, {"3": "Itpj-1753336470697", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12371/WEBRESOURCE5f496756459159e834a26cc91d1e5704", "w": 354, "h": 67}, "6": "im"}, {"3": "5KwL-1646810094655", "4": {"version": 1}, "5": [{"2": "2", "3": "meeG-1667182469100", "7": [{"8": "所以。base64编码 最后是 变大了 （因为3个"}, {"8": "字节", "9": [{"0": "#FFEE7C", "2": "bg"}, {"0": "#B620E0", "2": "c"}]}, {"8": "变成了4个"}, {"8": "字节", "9": [{"0": "#FFEE7C", "2": "bg"}, {"0": "#B620E0", "2": "c"}]}, {"8": "。）"}]}]}, {"3": "8qx7-1659057728408", "4": {"version": 1}, "5": [{"2": "2", "3": "7Fac-1667182469100"}]}, {"3": "iy8x-1659057728759", "4": {"version": 1}, "5": [{"2": "2", "3": "Sl9D-1667182469100"}]}, {"3": "AMyn-1753336470698", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12370/WEBRESOURCE0883dd015a5a23082a831f622b1c33a3", "w": 905, "h": 100}, "6": "im"}, {"3": "z79V-1646810095219", "4": {"version": 1}, "5": [{"2": "2", "3": "v8il-1667182469100"}]}, {"3": "Aa3W-1646905230214", "4": {"version": 1}, "5": [{"2": "2", "3": "ufUS-1667182469101", "7": [{"8": "以后的", "9": [{"0": "#FFEE7C", "2": "bg"}, {"0": "#F33232", "2": "c"}, {"0": 22, "2": "fs"}]}, {"8": "文件下载", "9": [{"0": 22, "2": "fs"}, {"0": "#993AF9", "2": "bg"}, {"0": "#000000", "2": "c"}]}, {"8": "可以这么参考着来实现（都是请求接口的）", "9": [{"0": 22, "2": "fs"}, {"0": "#FFEE7C", "2": "bg"}, {"0": "#F33232", "2": "c"}]}, {"8": "     前端自己实现有点麻烦（各种插件尝试，又不熟悉用法+没有现成的完整实例）"}]}]}, {"3": "GyVF-1646905351741", "4": {"version": 1}, "5": [{"2": "2", "3": "12E0-1667182469101", "7": [{"8": "后面就直接调接口就完事了！！！！！！"}]}]}, {"3": "cEYk-1753336470699", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12367/WEBRESOURCEbccff09b1a105eea836b9c503ae1b2e2", "w": 989, "h": 390}, "6": "im"}, {"3": "QJeT-1753336470700", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12365/WEBRESOURCE683cd432e2d233891a7856c085d8e092", "w": 932, "h": 308}, "6": "im"}, {"3": "Q4CC-1646904714234", "4": {"version": 1}, "5": [{"2": "2", "3": "PUbj-1667182469101"}]}, {"3": "zgil-1646904714490", "4": {"version": 1}, "5": [{"2": "2", "3": "eZ0Z-1667182469101", "7": [{"8": "图片转 base64 方法    （就不会有跨域问题了 / 减少 http 请求） "}, {"8": "（图片的话就不请求接口了，直接把url地址转成base64的，丢到new Blob() 里就行了，或者用html2canvas插件也行）", "9": [{"0": 26, "2": "fs"}]}]}]}, {"3": "Qu2R-1646908126317", "4": {"version": 1}, "5": [{"2": "2", "3": "fFvr-1667182469101", "7": [{"8": "（1）、 B<PERSON>b和FileReader 对象"}]}]}, {"3": "fwPy-1753336470701", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12362/WEBRESOURCE3a1594a7543d9cb920c5a5133287cf78", "w": 801, "h": 118}, "6": "im"}, {"3": "Cx1k-1753336470702", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12361/WEBRESOURCEe283d14598dda27f346f327fa1cce935", "w": 758, "h": 371}, "6": "im"}, {"3": "Bdkn-1753336470703", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12360/WEBRESOURCE8648213a344da685128c0008e88ee0d1", "w": 766, "h": 91}, "6": "im"}, {"3": "q2KX-1672885354491", "4": {"version": 1}, "5": [{"2": "2", "3": "1BWD-1672885354447", "7": [{"8": "", "9": [{"0": "#F33232", "2": "c"}]}]}]}, {"3": "AN7b-1672885354827", "4": {"version": 1}, "5": [{"2": "2", "3": "7JCe-1672885354785", "7": [{"8": "URL.createObjectURL", "9": [{"0": "#F33232", "2": "c"}]}, {"8": " 对象 专门将 blob 转为 DOMString类型的 url 用的api，  （该 API 不会有 跨域问题 前端自己实现的）"}]}]}, {"3": "NEyi-1647222737194", "4": {"version": 1}, "5": [{"2": "2", "3": "Xg7K-1667182469101", "7": [{"8": "浏览器直接打开文件"}]}]}, {"3": "Sqja-1663668413100", "4": {"version": 1}, "5": [{"2": "2", "3": "052k-1667182469101"}]}, {"3": "tFCq-1753336470704", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12363/WEBRESOURCE07a630e9c4ba48a170aaccb94a88a194", "w": 410, "h": 52}, "6": "im"}, {"3": "1Fw9-1753336470705", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12359/WEBRESOURCE7b6887dd48dd40a482cb25af9e6505b7", "w": 703, "h": 327}, "6": "im"}, {"3": "gU4v-1676791955139", "4": {"version": 1}, "5": [{"2": "2", "3": "7vux-1676791955105", "7": [{"8": "注意：：", "9": [{"0": 26, "2": "fs"}, {"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}]}]}, {"3": "d4Na-1753336470706", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12364/WEBRESOURCEcde3167d0a7edee2cceb90b97a148665", "w": 667, "h": 462}, "6": "im"}, {"3": "Wzer-1676820024981", "4": {"version": 1}, "5": [{"2": "2", "3": "pQmg-1676820024949"}]}, {"3": "me3S-1647222695030", "4": {"version": 1}, "5": [{"2": "2", "3": "lsfp-1667182469101", "7": [{"8": "（2）、 canvas.toDataURL()方法"}]}]}, {"3": "hMQH-1753336470707", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12354/WEBRESOURCE4663fad3c74c674b2137ead290982314", "w": 647, "h": 143}, "6": "im"}, {"3": "Vwh1-1753336470708", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12357/WEBRESOURCE944e455e4d157a35b00962d8a2b2ec47", "w": 656, "h": 276}, "6": "im"}, {"3": "HWNV-1753336470709", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12358/WEBRESOURCE231110febed6ed51083fb8b14a739e66", "w": 451, "h": 262}, "6": "im"}, {"3": "xXqy-1753336470710", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12356/WEBRESOURCE4093e3f92588717a2b9b7a1f73fc79bb", "w": 754, "h": 178}, "6": "im"}, {"3": "pFSv-1646371444773", "4": {"version": 1}, "5": [{"2": "2", "3": "Uxo8-1667182469102", "7": [{"8": "创建一个 img 标签 "}]}]}, {"3": "fdND-1753336470711", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12355/WEBRESOURCEe19107a2c92c4660139a794852754273", "w": 522, "h": 174}, "6": "im"}, {"3": "2cl2-1672887239167", "4": {"version": 1}, "5": [{"2": "2", "3": "qaYR-1672887239128"}]}, {"3": "q0Z0-1672915791364", "4": {"version": 1}, "5": [{"2": "2", "3": "RITn-1672915791315", "7": [{"8": "url 转 file", "9": [{"0": "#FFEE7C", "2": "bg"}, {"0": "#F33232", "2": "c"}, {"0": 22, "2": "fs"}]}]}]}, {"3": "sQVy-1672915813272", "4": {"version": 1}, "5": [{"2": "2", "3": "OoqF-1672915813229", "7": [{"8": "url 先转 blob， 再用 new File( [ blob ], filename, { type: 'xxx' } ) 传 blob 转成 file 对象"}]}]}, {"3": "5fyY-1753336470712", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12353/WEBRESOURCEda144b914c5f33c22f407b97f2a742d2", "w": 411, "h": 292}, "6": "im"}, {"3": "5wqE-1753336470713", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12351/WEBRESOURCEd83b908d3bc94e901b5600562c7e70d1", "w": 469, "h": 130}, "6": "im"}, {"3": "czhM-1672916192981", "4": {"version": 1, "la": "javascript", "th": "default"}, "5": [{"3": "VWMu-1672916192981", "5": [{"2": "2", "3": "Eo7L-1672916192981", "7": [{"8": "// 使用"}]}], "6": "cl"}, {"3": "b3h6-1672916199262", "5": [{"2": "2", "3": "JFh1-1672916199217", "7": [{"8": "imgUrlToFile(imgUrl, function(file) {"}]}], "6": "cl"}, {"3": "CyYS-1672916192981", "5": [{"2": "2", "3": "zxo9-1672916192981", "7": [{"8": "       console.log(file); // 文件格式"}]}], "6": "cl"}, {"3": "IpJ4-1672916192981", "5": [{"2": "2", "3": "VmmC-1672916192981", "7": [{"8": " });"}]}], "6": "cl"}], "6": "cd"}, {"3": "Y9Xt-1753336470714", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12350/WEBRESOURCEbd82b083da867efbb7ba722baf290531", "w": 425, "h": 358}, "6": "im"}, {"3": "ecAx-1672916207999", "4": {"version": 1}, "5": [{"2": "2", "3": "1RN7-1672916207957", "7": [{"8": "这样子每次都多一个图片请求了"}]}]}, {"3": "pHaO-1672985785448", "4": {"version": 1}, "5": [{"2": "2", "3": "Endv-1672985785404"}]}, {"3": "4BhG-1672985785905", "4": {"version": 1}, "5": [{"2": "2", "3": "6wB4-1672985785863"}]}, {"3": "9uVO-1672887239518", "4": {"version": 1}, "5": [{"2": "2", "3": "TXwI-1672887239478", "7": [{"8": "204、"}]}]}, {"3": "Qp1i-1646371449574", "4": {"version": 1}, "5": [{"2": "2", "3": "eriF-1667182469102", "7": [{"8": " "}]}]}, {"3": "Swv2-1753336470715", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12349/WEBRESOURCE160fad73dcbbe19edad5801dd347a690", "w": 609, "h": 68}, "6": "im"}, {"3": "fS8C-1646371449826", "4": {"version": 1}, "5": [{"2": "2", "3": "epxX-1667182469102", "7": [{"8": "-S： --save     生产环境"}]}]}, {"3": "GudE-1646649527505", "4": {"version": 1}, "5": [{"2": "2", "3": "3BP0-1667182469102", "7": [{"8": "-D: --dev         开发环境"}]}]}, {"3": "mTaw-1646371450125", "4": {"version": 1}, "5": [{"2": "2", "3": "K0Cn-1667182469102"}]}, {"3": "oMNS-1646371450442", "4": {"version": 1}, "5": [{"2": "2", "3": "j4gw-1667182469102"}]}, {"3": "bpDZ-1646721150704", "4": {"version": 1}, "5": [{"2": "2", "3": "MdhV-1667182469102"}]}, {"3": "37xn-1646721150860", "4": {"version": 1}, "5": [{"2": "2", "3": "tK07-1667182469102", "7": [{"8": "205、 切分支的时候 node_modules 依赖是不会跟着切的"}]}]}, {"3": "vW7t-1646731227887", "4": {"version": 1}, "5": [{"2": "2", "3": "unfQ-1667182469102"}]}, {"3": "Gpqt-1646731228629", "4": {"version": 1}, "5": [{"2": "2", "3": "AAN6-1667182469102", "7": [{"8": "           很多时候依赖装好了，但是依然运行项目会报错（而且其他人可以，其他电脑环境可以，就你本地跑的时候有问题），"}]}]}, {"3": "rmXl-1646731289268", "4": {"version": 1}, "5": [{"2": "2", "3": "7UUQ-1667182469102", "7": [{"8": " \t   这个时候就要考虑 依赖的版本问题 导致某些 api不兼容 然后报错的 ！！！"}]}]}, {"3": "ambN-1646731329134", "4": {"version": 1}, "5": [{"2": "2", "3": "cRX2-1667182469102", "7": [{"8": "           所以一些 主要的依赖要手动"}, {"8": "锁死版本", "9": [{"0": "#FFEE7C", "2": "bg"}, {"0": "#F33232", "2": "c"}]}, {"8": "，防止不兼容报错 ！！！！！！"}]}]}, {"3": "k3HP-1646731393635", "4": {"version": 1, "s": {"ti": 28}}, "5": [{"2": "2", "3": "fU72-1667182469102", "7": [{"8": "   （1）、最常见的就是 package.json 中直接写死版本号 （不用 ^ 和 ~ 指定版本号区间）;"}]}]}, {"3": "y7vt-1646731625252", "4": {"version": 1, "s": {"ti": 28}}, "5": [{"2": "2", "3": "VkQw-1667182469102", "7": [{"8": "   （2）、或者拿别人的带 package.lock.json (npm安装)     /    带 yarn.lock (yarn 安装)    文件拷贝过来，删除自己的该文件，再重新装包 （npm / yarn 会根据锁定文件的包版本进行安装）"}]}]}, {"3": "5IK9-1646721174167", "4": {"version": 1, "s": {"ti": 28}}, "5": [{"2": "2", "3": "1nzG-1667182469102", "7": [{"8": "   （3）、使用 npm 提供的 shrinlwrap 命令 锁定依赖包"}]}]}, {"3": "PNWE-1753336470716", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12348/WEBRESOURCEcd5b92e9c5fbc71f119a5c29d5f9fbe5", "w": 893, "h": 81}, "6": "im"}, {"3": "DosZ-1646731511973", "4": {"version": 1, "s": {"ti": 28}}, "5": [{"2": "2", "3": "qysj-1667182469102"}]}, {"3": "3J6S-1646731518195", "4": {"version": 1, "li": "csJW-1753336470651", "ll": null, "lt": "unordered", "s": {"ti": 0}}, "5": [{"2": "2", "3": "zOJ5-1667182469102", "7": [{"8": "符号"}, {"8": "^", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "：表示主版本固定的情况下，可更新最新版。例如：vuex: \"^3.1.3\"，3.1.3及其以上的3."}, {"8": "x.x", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "都是满足的"}]}], "6": "l"}, {"3": "DAw2-1646731525151", "4": {"version": 1, "li": "csJW-1753336470651", "ll": null, "lt": "unordered"}, "5": [{"2": "2", "3": "8mGM-1667182469103", "7": [{"8": " 符号"}, {"8": "~", "9": [{"0": "#B620E0", "2": "c"}]}, {"8": "：表示次版本固定的情况下，可更新最新版。如：vuex: \"~3.1.3\"，3.1.3及其以上的3.1."}, {"8": "x", "9": [{"0": "#B620E0", "2": "c"}]}, {"8": "都是满足的\n"}]}], "6": "l"}, {"3": "8s2D-1646731518195", "4": {"version": 1, "li": "csJW-1753336470651", "ll": null, "lt": "unordered"}, "5": [{"2": "2", "3": "tKOa-1667182469103", "7": [{"8": "无符号：无符号表示固定版本号，例如：vuex: \"3.1.3\"，此时一定是安装3.1.3版本"}]}], "6": "l"}, {"3": "ELky-1646731512111", "4": {"version": 1}, "5": [{"2": "2", "3": "Lq1J-1667182469103"}]}, {"3": "4USD-1753336470717", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12352/WEBRESOURCE566f3a07bf55cdeef984e954aefd1d7f", "w": 858, "h": 56}, "6": "im"}, {"3": "VimJ-1646731569650", "4": {"version": 1}, "5": [{"2": "2", "3": "mB89-1667182469103", "7": [{"8": "npm cache clean --fource 强制清楚本地缓存"}]}]}, {"3": "j6YL-1646731569915", "4": {"version": 1}, "5": [{"2": "2", "3": "XOIm-1667182469103"}]}, {"3": "NXMP-1646965235937", "4": {"version": 1}, "5": [{"2": "2", "3": "G992-1667182469103", "7": [{"8": "Yarn : "}]}]}, {"3": "UAFq-1753336470718", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12345/WEBRESOURCEb4cfc934cc1c4575df78d430752aa9d7", "w": 842, "h": 65}, "6": "im"}, {"3": "KhoK-1646721174304", "4": {"version": 1}, "5": [{"2": "2", "3": "YlDq-1667182469103", "7": [{"8": "像npm一样，yarn使用本地缓存。与npm不同的是，yarn无需互联网连接就能安装本地缓存的依赖项，它提供了离线模式。最主要的是比 npm 快多了", "9": [{"0": "#000000", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 15, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}]}]}, {"3": "Ul7p-1646965321578", "4": {"version": 1}, "5": [{"2": "2", "3": "zzXy-1667182469103"}]}, {"3": "dab5-1646965321965", "4": {"version": 1}, "5": [{"2": "2", "3": "wgbM-1667182469103"}]}, {"3": "BE5B-1646964766474", "4": {"version": 1}, "5": [{"2": "2", "3": "RfqV-1667182469103", "7": [{"8": "Pnpm:    用的都是缓存来的 (多个项目的包，指向的都是同一个,都是软链) "}, {"8": "软链接类似windows系统的快捷方式；", "9": [{"0": "#FA6400", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}]}]}]}, {"3": "OZ2E-1753336470719", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12347/WEBRESOURCEa7051cf40809d4ed3ffa5694e1073fd0", "w": 860, "h": 82}, "6": "im"}, {"3": "q8fm-1646964766741", "4": {"version": 1}, "5": [{"2": "2", "3": "lccx-1667182469103", "7": [{"8": "还有一个特点，就是磁盘空间利用高效（"}, {"8": "Fast, disk space efficient package manager", "9": [{"0": "#666666", "2": "c"}, {"0": "rgb(248, 248, 248)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": "）"}]}]}, {"3": "kHgz-1646964767016", "4": {"version": 1}, "5": [{"2": "2", "3": "wtLY-1667182469103", "7": [{"8": "支持 monorepo ，"}, {"8": "monorepo 的宗旨就是用一个 git 仓库来管理多个子项目", "9": [{"0": "#F33232", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": "，而不是一个子项目一个git 仓库 （git submodule 形式，但是多个子项目还是单独是一个git仓库）", "9": [{"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}, {"0": "#333333", "2": "c"}]}]}]}, {"3": "BXP4-1678004993847", "4": {"version": 1}, "5": [{"2": "2", "3": "YJiJ-1678004993813", "7": [{"8": "monorepo", "9": [{"0": "#000000", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}, {"2": "b"}]}, {"8": "  "}, {"8": "依赖统一管理", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "（避免重复安装依赖）、"}, {"8": "代码共享", "9": [{"0": "#FFEE7C", "2": "bg"}, {"0": "#F33232", "2": "c"}]}, {"8": "（减少重复代码和组件的开发和维护）、"}, {"8": "代码可重用性", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "（同一个仓库中的不同项目可以重复使用） 包相互引用"}]}]}, {"3": "t1ew-1646965640302", "4": {"version": 1}, "5": [{"2": "2", "3": "7U64-1667182469104"}]}, {"3": "qhjF-1753336470652", "4": {"version": 1, "l": "h3", "s": {"lh": 1.5}}, "5": [{"2": "2", "3": "JM4p-1667182469104", "7": [{"8": "pnpm install： 安装所有的依赖   （其他用法跟npm类似， update / uninstall / ..）", "9": [{"2": "b"}, {"0": 16, "2": "fs"}]}]}], "6": "h"}, {"3": "Gqir-1753336470720", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12344/WEBRESOURCE734e9a9f2a46cb4e18ef3b8652b40530", "w": 403, "h": 151}, "6": "im"}, {"3": "SfjI-1646965640782", "4": {"version": 1}, "5": [{"2": "2", "3": "Yrpy-1667182469104", "7": [{"8": "pnpm i  装包报错"}]}]}, {"3": "PFW1-1753336470721", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12346/WEBRESOURCE15968ffbff644422187ac325b464230d", "w": 949, "h": 120}, "6": "im"}, {"3": "yQK9-1647069669733", "4": {"version": 1}, "5": [{"2": "2", "3": "nGDn-1667182469104", "7": [{"8": "因为设置了淘宝镜像源，要去掉，重新设置成 npmjs 的源"}]}]}, {"3": "Wg0u-1647070593084", "4": {"version": 1}, "5": [{"2": "2", "3": "UxtO-1667182469104"}]}, {"3": "JjO1-1647070593579", "4": {"version": 1}, "5": [{"2": "2", "3": "FUt9-1667182469104", "7": [{"8": "先 npm get registry 查看npm源"}]}]}, {"3": "Jo1q-1753336470722", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12342/WEBRESOURCE36567b846616b8aac2943b845c617b44", "w": 679, "h": 63}, "6": "im"}, {"3": "aE20-1647069669964", "4": {"version": 1}, "5": [{"2": "2", "3": "KDtr-1667182469104", "7": [{"8": "设置 npm config set registry https://registry.npmjs.org/  直接指向 npm"}]}]}, {"3": "YLSE-1753336470723", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12343/WEBRESOURCE9ab59c24aadffada444a472eae17d8c6", "w": 819, "h": 77}, "6": "im"}, {"3": "qcbl-1647069671733", "4": {"version": 1}, "5": [{"2": "2", "3": "n1eM-1667182469104", "7": [{"8": "这时候再 pnpm i 就可以正常装包了"}]}]}, {"3": "JX8Q-1753336470724", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12341/WEBRESOURCE7c13831ec5a2e51e4730e5e81559a4d9", "w": 888, "h": 1006}, "6": "im"}, {"3": "PcCM-1647069671940", "4": {"version": 1}, "5": [{"2": "2", "3": "HZwG-1667182469104", "7": [{"8": "pnpm 升级 pnpm    "}, {"8": "pnpm add -g pnpm", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}]}]}, {"3": "fhuH-1753336470725", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12339/WEBRESOURCE9d1caafea9d5f2355cf9b9160d8564f2", "w": 581, "h": 631}, "6": "im"}, {"3": "eb4m-1753336470726", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12338/WEBRESOURCE59aa5b98abf58f99122077672e3778d2", "w": 815, "h": 113}, "6": "im"}, {"3": "a34j-1646964767356", "4": {"version": 1}, "5": [{"2": "2", "3": "iZEL-1667182469104", "7": [{"8": "后面直接就可以用 pnpm（升到最新版本） 跑 不同的项目项目了 ，不用切换 node 版本（使用 14.x lst 版本）就可以", "9": [{"0": "#F33232", "2": "c"}]}]}]}, {"3": "usRU-1647071573173", "4": {"version": 1}, "5": [{"2": "2", "3": "Nwt1-1667182469104"}]}, {"3": "A68p-1647071573380", "4": {"version": 1}, "5": [{"2": "2", "3": "aAnU-1667182469104", "7": [{"8": "npm 和 yarn 不一样？"}]}]}, {"3": "mOVZ-1667183280849", "4": {"version": 1}, "5": [{"2": "2", "3": "QVIP-1667183280800"}]}, {"3": "VEyz-1667183544235", "4": {"version": 1}, "5": [{"2": "2", "3": "gcQO-1667183544187", "7": [{"8": "  "}]}]}, {"3": "wFJ2-1667183223266", "4": {"version": 1}, "5": [{"2": "2", "3": "jvUM-1667183223213"}]}, {"3": "tsda-1667183247316", "4": {"version": 1}, "5": [{"2": "2", "3": "5v3k-1667183247273"}]}, {"3": "Src8-1647071573581", "4": {"version": 1}, "5": [{"2": "2", "3": "eJ55-1667182469104"}]}, {"3": "oyro-1646721174422", "4": {"version": 1}, "5": [{"2": "2", "3": "9OIy-1667182469104", "7": [{"8": "206、   if ... else if .. else  优化"}]}]}, {"3": "KNpQ-1646721150991", "4": {"version": 1}, "5": [{"2": "2", "3": "qL8M-1667182469104"}]}, {"3": "tHR9-1646732834603", "4": {"version": 1}, "5": [{"2": "2", "3": "uqbZ-1667182469104", "7": [{"8": "（1）、单一条件优化"}]}]}, {"3": "JBP5-1753336470727", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12340/WEBRESOURCE840336201c860602b7d7a7387829761f", "w": 610, "h": 359}, "6": "im"}, {"3": "fDSf-1646732633935", "4": {"version": 1}, "5": [{"2": "2", "3": "vAMj-1667182469104"}]}, {"3": "3IoF-1646732634218", "4": {"version": 1}, "5": [{"2": "2", "3": "LlsX-1667182469104", "7": [{"8": "（2）、复合条件优化"}]}]}, {"3": "oIwZ-1753336470728", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12336/WEBRESOURCE057778d5d2858a9a25d4d8c6661abdb2", "w": 650, "h": 919}, "6": "im"}, {"3": "vTEY-1646732642553", "4": {"version": 1}, "5": [{"2": "2", "3": "1aZ3-1667182469105"}]}, {"3": "pezf-1646749562470", "4": {"version": 1}, "5": [{"2": "2", "3": "nKZ6-1667182469105"}]}, {"3": "YP6D-1646732642841", "4": {"version": 1}, "5": [{"2": "2", "3": "48NU-1667182469105"}]}, {"3": "VC0P-1646732643118", "4": {"version": 1}, "5": [{"2": "2", "3": "IAFI-1667182469105", "7": [{"8": "207、 "}, {"8": "break", "9": [{"0": "#B620E0", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}, {"0": 18, "2": "fs"}]}, {"8": " "}, {"8": "continue", "9": [{"0": "#B620E0", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}, {"0": 18, "2": "fs"}]}, {"8": " "}, {"8": "return", "9": [{"0": "#FFEE7C", "2": "bg"}, {"0": "#B620E0", "2": "c"}, {"0": 18, "2": "fs"}]}, {"8": "  "}]}]}, {"3": "UMVf-1646790488751", "4": {"version": 1}, "5": [{"2": "2", "3": "0WpT-1667182469105"}]}, {"3": "bEEh-1646790668635", "4": {"version": 1}, "5": [{"2": "2", "3": "lMVw-1667182469105", "7": [{"8": "1)、 break 是直接跳出整个循环 （不再执行循环函数）"}]}]}, {"3": "mm5l-1646790670097", "4": {"version": 1}, "5": [{"2": "2", "3": "Ombp-1667182469105", "7": [{"8": "2)、 continue 是跳出本次循环（就是本次循环里continue 后的代码不再执行），然后进入下一个循环体"}]}]}, {"3": "GI7g-1646790672037", "4": {"version": 1}, "5": [{"2": "2", "3": "UL0R-1667182469105", "7": [{"8": "3)、 return 是 "}, {"8": "结束当前方法", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "，主要用于方法的返回值（没有返回值可以返回空或者不返回）  "}, {"8": " return", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": " 强度 大于", "9": [{"0": "#FFEE7C", "2": "bg"}, {"0": "#F33232", "2": "c"}]}, {"8": " break   ", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": " ;    "}, {"8": "return 之后是整个函数（方法）都不会执行！！！ break是循环之后还有代码还会接着执行!!!!", "9": [{"0": "#69D600", "2": "c"}]}]}]}, {"3": "PCEh-1646790489593", "4": {"version": 1}, "5": [{"2": "2", "3": "hyrI-1667182469105"}]}, {"3": "RWey-1753336470729", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12335/WEBRESOURCE35d220be0bba9ca43e03f3c690906e21", "w": 1001, "h": 155}, "6": "im"}, {"3": "WCUb-1646790490402", "4": {"version": 1}, "5": [{"2": "2", "3": "aimJ-1667182469105"}]}, {"3": "LO2g-1646790493607", "4": {"version": 1}, "5": [{"2": "2", "3": "40RY-1667182469105"}]}, {"3": "gib2-1646791161849", "4": {"version": 1}, "5": [{"2": "2", "3": "h6Oz-1667182469105", "7": [{"8": "208、pre 标签 "}]}]}, {"3": "MAaT-1753336470730", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12333/WEBRESOURCEfc6cea50d601f117efe4b6c84f0aec6a", "w": 587, "h": 60}, "6": "im"}, {"3": "fjUC-1646790490972", "4": {"version": 1}, "5": [{"2": "2", "3": "wVqn-1667182469105"}]}, {"3": "JcAo-1753336470731", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12337/WEBRESOURCEf22f7cd1b81f75097a9a2590dec4f6c3", "w": 944, "h": 452}, "6": "im"}, {"3": "NkQz-1646886202028", "4": {"version": 1}, "5": [{"2": "2", "3": "yjLM-1667182469105"}]}, {"3": "Ir0r-1646886202266", "4": {"version": 1}, "5": [{"2": "2", "3": "ZcmW-1667182469105"}]}, {"3": "HvVe-1646886202570", "4": {"version": 1}, "5": [{"2": "2", "3": "I8Tc-1667182469105"}]}, {"3": "y3R7-1646886202820", "4": {"version": 1}, "5": [{"2": "2", "3": "FkPj-1667182469105", "7": [{"8": "209、  vue-cli  和 vite  的比较"}]}]}, {"3": "diao-1646972138694", "4": {"version": 1}, "5": [{"2": "2", "3": "ad9P-1667182469105", "7": [{"8": "（1）、vue-cli 通过 NODE_ENV 指定环境变量"}]}]}, {"3": "mC1M-1753336470732", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12334/WEBRESOURCEbb6ab855b4bc24fa5658cc63b51499f3", "w": 352, "h": 115}, "6": "im"}, {"3": "sfwY-1646732643464", "4": {"version": 1}, "5": [{"2": "2", "3": "y5dN-1667182469105"}]}, {"3": "oqfT-1660705781143", "4": {"version": 1}, "5": [{"2": "2", "3": "fbFY-1667182469106", "7": [{"8": ".env  ", "9": [{"0": "#6CDFFF", "2": "c"}]}, {"8": "                                 开发环境 和 生产环境 都会加载的 配置文件"}]}]}, {"3": "EaQq-1660705788349", "4": {"version": 1}, "5": [{"2": "2", "3": "lhQ7-1667182469106", "7": [{"8": ".env.development              只在 开发环境 加载的 配置文件 "}]}]}, {"3": "fEpq-1660705800240", "4": {"version": 1}, "5": [{"2": "2", "3": "8llm-1667182469106", "7": [{"8": ".env.production \t\t  只在 生产环境 加载的 配置文件"}]}]}, {"3": "HNmN-1660705781484", "4": {"version": 1}, "5": [{"2": "2", "3": "9Exx-1667182469106"}]}, {"3": "amvK-1660706266617", "4": {"version": 1}, "5": [{"2": "2", "3": "Jt6f-1667182469106", "7": [{"8": "优先级：  同时存在的环境变量、  特定模式的环境文件（", "9": [{"2": "b"}]}, {"8": ".env.development"}, {"8": " 和 ", "9": [{"2": "b"}]}, {"8": ".env.production"}, {"8": "）优先级高于.env的", "9": [{"2": "b"}]}]}]}, {"3": "f1Mv-1660705781784", "4": {"version": 1}, "5": [{"2": "2", "3": "YiBb-1667182469106", "7": [{"8": "修改了配置文件需要重启服务，文件内容变量才生效。"}]}]}, {"3": "Hxie-1660706246305", "4": {"version": 1}, "5": [{"2": "2", "3": "2ZAs-1667182469106"}]}, {"3": "ylG2-1660705782108", "4": {"version": 1}, "5": [{"2": "2", "3": "onqa-1667182469106", "7": [{"8": "vite 通过 import.meta.env  中的 MODE 指定环境变量"}]}]}, {"3": "4eCc-1753336470733", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12332/WEBRESOURCEd0d3ee84dcf038892ba774fea6153116", "w": 473, "h": 173}, "6": "im"}, {"3": "rvI2-1646972238520", "4": {"version": 1}, "5": [{"2": "2", "3": "FTOJ-1667182469106"}]}, {"3": "5ctT-1659864085272", "4": {"version": 1}, "5": [{"2": "2", "3": "yBSG-1667182469106", "7": [{"8": "vite的 hmr 原理： hot module replacement（热模块替换）"}]}]}, {"3": "ekNw-1753336470734", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12330/WEBRESOURCE3444efda67c1887a46006379b459dd2f", "w": 693, "h": 252}, "6": "im"}, {"3": "Gqak-1753336470735", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12328/WEBRESOURCEcb742d8613a86d875fdaa933fba3c947", "w": 986, "h": 85}, "6": "im"}, {"3": "jYHc-1753336470736", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12331/WEBRESOURCE311483ef03e931aaad4278a1c3e22941", "w": 796, "h": 173}, "6": "im"}, {"3": "FkNl-1646973254032", "4": {"version": 1}, "5": [{"2": "2", "3": "YBTU-1667182469106"}]}, {"3": "tUaw-1659864296565", "4": {"version": 1}, "5": [{"2": "2", "3": "AxQ3-1667182469106"}]}, {"3": "UQ3j-1646973254522", "4": {"version": 1}, "5": [{"2": "2", "3": "qQBI-1667182469106"}]}, {"3": "813w-1646973254769", "4": {"version": 1}, "5": [{"2": "2", "3": "OJkT-1667182469106", "7": [{"8": "210、   简简单单，数组扁平化 （小 循环 + 递归）， 虽然可以直接 arr.flat()"}]}]}, {"3": "mA52-1753336470737", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12327/WEBRESOURCE3e08e615e193a1281105c0264df5c6bc", "w": 563, "h": 255}, "6": "im"}, {"3": "AWTI-1646973259926", "4": {"version": 1}, "5": [{"2": "2", "3": "9f7l-1667182469106"}]}, {"3": "2xUH-1753336470738", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12325/WEBRESOURCE178a86fa087ef00e9db147b843842cc6", "w": 462, "h": 97}, "6": "im"}, {"3": "WSC1-1647013294858", "4": {"version": 1}, "5": [{"2": "2", "3": "Wdt9-1667182469106"}]}, {"3": "rleZ-1753336470739", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12329/WEBRESOURCE1e028ec8ce9f35ca1622757d603e6839", "w": 692, "h": 580}, "6": "im"}, {"3": "O0PC-1647013010379", "4": {"version": 1}, "5": [{"2": "2", "3": "s5JX-1667182469106"}]}, {"3": "olzz-1667873991536", "4": {"version": 1}, "5": [{"2": "2", "3": "txX6-1667873991486", "7": [{"8": "来个回溯：", "9": [{"2": "b"}]}, {"8": "  "}]}]}, {"3": "D4xy-1667989945549", "4": {"version": 1}, "5": [{"2": "2", "3": "BmYc-1667989945549"}]}, {"3": "lKKx-1667873999745", "4": {"version": 1}, "5": [{"2": "2", "3": "9Mpd-1667873999694", "7": [{"8": "递归是 "}, {"8": "reduce", "9": [{"0": "#F33232", "2": "c"}]}, {"8": " (  调用自己 "}, {"8": "return pre", "9": [{"0": "#F33232", "2": "c"}]}, {"8": " ) "}]}]}, {"3": "XuXr-1753336470740", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12322/WEBRESOURCEa5951db64442ee8db09957059d435932", "w": 635, "h": 274}, "6": "im"}, {"3": "zsjI-1667874242897", "4": {"version": 1}, "5": [{"2": "2", "3": "LDi8-1667874242853"}]}, {"3": "sh3V-1667874000671", "4": {"version": 1}, "5": [{"2": "2", "3": "trw6-1667874000627", "7": [{"8": "接着来 "}, {"8": "回溯", "9": [{"2": "b"}]}, {"8": " 吧 ~  回到题目"}]}]}, {"3": "NVB9-1667877127492", "4": {"version": 1}, "5": [{"2": "2", "3": "qv47-1667877127447"}]}, {"3": "nMjY-1667877127951", "4": {"version": 1}, "5": [{"2": "2", "3": "6VYq-1667877127905"}]}, {"3": "F90j-1667877096405", "4": {"version": 1}, "5": [{"2": "2", "3": "eC9t-1667877096360"}]}, {"3": "rdDx-1667877097459", "4": {"version": 1}, "5": [{"2": "2", "3": "h6ta-1667877097414"}]}, {"3": "9YnB-1667873992027", "4": {"version": 1}, "5": [{"2": "2", "3": "CQXb-1667873991969"}]}, {"3": "ulJH-1647013010924", "4": {"version": 1}, "5": [{"2": "2", "3": "lvuH-1667182469106"}]}, {"3": "8F6I-1647013310712", "4": {"version": 1}, "5": [{"2": "2", "3": "PRYE-1667182469106"}]}, {"3": "2XRH-1647013011092", "4": {"version": 1}, "5": [{"2": "2", "3": "oapg-1667182469106", "7": [{"8": "211、  ( 一 )、 "}, {"8": "document.designMode = 'on'", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}, {"0": 22, "2": "fs"}]}, {"8": " ", "9": [{"0": 22, "2": "fs"}]}, {"8": "   开启网页设计模式 ，想改哪里 随便改 ！"}]}]}, {"3": "GVxz-1753336470741", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12326/WEBRESOURCEd8f9cba5e1882fc439b42ec97abbdfda", "w": 898, "h": 492}, "6": "im"}, {"3": "7n0Z-1646973260405", "4": {"version": 1}, "5": [{"2": "2", "3": "QYfg-1667182469107"}]}, {"3": "qx86-1665674654752", "4": {"version": 1}, "5": [{"2": "2", "3": "pYVV-1667182469107", "7": [{"8": "( 二)、"}, {"8": "  ", "9": [{"0": 22, "2": "fs"}]}, {"8": "document.body.contentEditable = true ", "9": [{"0": 22, "2": "fs"}, {"0": "#FFEE7C", "2": "bg"}, {"0": "#F33232", "2": "c"}]}, {"8": "        网页内容可编辑"}]}]}, {"3": "Bclm-1665674655079", "4": {"version": 1}, "5": [{"2": "2", "3": "tY0Z-1667182469107"}]}, {"3": "kcV6-1647013312239", "4": {"version": 1}, "5": [{"2": "2", "3": "ILwh-1667182469107"}]}, {"3": "0hI7-1647091193723", "4": {"version": 1}, "5": [{"2": "2", "3": "ukSq-1667182469107", "7": [{"8": "212、 css 属性 ： backface-visibility: hidden "}]}]}, {"3": "BguL-1753336470742", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12321/WEBRESOURCE9b6257ce66f54c588f02111180abf14a", "w": 819, "h": 456}, "6": "im"}, {"3": "Av8O-1753336470743", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12323/WEBRESOURCE219e235817e3f3d16ebab4b0257ba507", "w": 841, "h": 455}, "6": "im"}, {"3": "4A1L-1646973255015", "4": {"version": 1}, "5": [{"2": "2", "3": "hyEK-1667182469107"}]}, {"3": "p5tJ-1661241999814", "4": {"version": 1}, "5": [{"2": "2", "3": "Z9yV-1667182469107", "7": [{"8": "设置背景图片（background-image）的透明度", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "（直接opacity会是包括背景图片的整个DOM，而不是单纯只影响背景图片）"}]}]}, {"3": "IKN1-1661242074701", "4": {"version": 1}, "5": [{"2": "2", "3": "OXLQ-1667182469107", "7": [{"8": "1、利用伪元素 ::after  div本身设置 position: relative; "}, {"8": "z-index: 0; ", "9": [{"2": "b"}]}, {"8": " after 伪元素设置 绝对定位 和 上下左右都为0，content: ''， 然后再background-image: url(...)， "}, {"8": "z-index: -1", "9": [{"2": "b"}]}, {"8": "; 就可以了。"}]}]}, {"3": "WuJB-1753336470744", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12324/WEBRESOURCEd1ced455e3459ac3e3e607d06cebce0d", "w": 893, "h": 688}, "6": "im"}, {"3": "4Ct4-1661242384033", "4": {"version": 1}, "5": [{"2": "2", "3": "9w7W-1667182469107", "7": [{"8": "2、"}, {"8": "使用", "9": [{"0": "#333333", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}]}, {"8": " ", "9": [{"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "#F33232", "2": "c"}]}, {"8": "cross-fade()", "9": [{"0": "#F33232", "2": "c"}, {"0": "#E6E6E6", "2": "bg"}]}, {"8": "  图像函数", "9": [{"0": "#333333", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}]}]}]}, {"3": "AVjS-1661242691706", "4": {"version": 1}, "5": [{"2": "2", "3": "IGlD-1667182469107", "7": [{"8": "语法： ", "9": [{"0": "#333333", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"2": "b"}]}]}]}, {"3": "uDZC-1661242711576", "4": {"version": 1, "la": "javascript", "th": "default"}, "5": [{"3": "jFqe-1667182469108", "5": [{"2": "2", "3": "832f-1667182469108", "7": [{"8": "<image-combination> = cross-fade( <image>, <image>, <percentage> )"}]}], "6": "cl"}], "6": "cd"}, {"3": "pnjf-1661242075021", "4": {"version": 1}, "5": [{"2": "2", "3": "rEM1-1667182469108", "7": [{"8": "这是一张空图片，只有一个点  "}]}]}, {"3": "6M1o-1661242529188", "4": {"version": 1, "la": "javascript", "th": "default"}, "5": [{"3": "MrmZ-1667182469108", "5": [{"2": "2", "3": "Xg9w-1667182469108", "7": [{"8": "data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw=="}]}], "6": "cl"}], "6": "cd"}, {"3": "vRr0-1753336470745", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12318/WEBRESOURCE6dbbf8a111f9a67e4e84a6ee4f558c8c", "w": 183, "h": 117}, "6": "im"}, {"3": "8cfw-1753336470746", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12317/WEBRESOURCE32f470fb3aad6516c8426087baffe6e5", "w": 992, "h": 174}, "6": "im"}, {"3": "Hi6Y-1661242661057", "4": {"version": 1}, "5": [{"2": "2", "3": "sSWE-1667182469108", "7": [{"8": "效果： "}]}]}, {"3": "Jf3x-1753336470747", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12319/WEBRESOURCEf012b5fb9c5c220bf7ea114fc80e0ada", "w": 290, "h": 756}, "6": "im"}, {"3": "0OBI-1661242655113", "4": {"version": 1}, "5": [{"2": "2", "3": "G9dB-1667182469108", "7": [{"8": "background-image ", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "设置的div显示不正常时，"}, {"8": "要加上 background-size 属性。设置 cover", "9": [{"2": "b"}]}]}]}, {"3": "AoGo-1753336470748", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12320/WEBRESOURCE4b1a389b0458b14a223b8d030568cd77", "w": 699, "h": 287}, "6": "im"}, {"3": "tYVj-1661242740736", "4": {"version": 1}, "5": [{"2": "2", "3": "Nyik-1667182469108"}]}, {"3": "SoII-1647091252095", "4": {"version": 1}, "5": [{"2": "2", "3": "jSUT-1667182469108"}]}, {"3": "WaQd-1647091253158", "4": {"version": 1}, "5": [{"2": "2", "3": "sqGY-1667182469108", "7": [{"8": "213、vue3 的 ref、reactive、toRef、toRefs"}]}]}, {"3": "JXpx-1753336470749", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12314/WEBRESOURCEc052209fa01614df01fa7d9b67759d17", "w": 886, "h": 469}, "6": "im"}, {"3": "Adia-1647091265348", "4": {"version": 1}, "5": [{"2": "2", "3": "eKNf-1667182469108"}]}, {"3": "5pEH-1647267975926", "4": {"version": 1}, "5": [{"2": "2", "3": "ByZh-1667182469108"}]}, {"3": "7sbM-1647267976246", "4": {"version": 1}, "5": [{"2": "2", "3": "QfZb-1667182469108", "7": [{"8": "214、 vite 配置 https"}]}]}, {"3": "6ubW-1753336470750", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12316/WEBRESOURCEcae96e5adf798b00246d78ba41470341", "w": 759, "h": 727}, "6": "im"}, {"3": "VliI-1647267980346", "4": {"version": 1}, "5": [{"2": "2", "3": "Ric1-1667182469108"}]}, {"3": "Cs6I-1647835218313", "4": {"version": 1}, "5": [{"2": "2", "3": "FMcP-1667182469108"}]}, {"3": "BkVK-1647835219240", "4": {"version": 1}, "5": [{"2": "2", "3": "eZDd-1667182469108", "7": [{"8": "215、 vue 的几个 语法糖"}]}]}, {"3": "mZYC-1647958680516", "4": {"version": 1}, "5": [{"2": "2", "3": "Nkma-1667182469108"}]}, {"3": "ijMu-1647958680731", "4": {"version": 1}, "5": [{"2": "2", "3": "CvoQ-1667182469108", "7": [{"8": "v-bind:   相当于 :"}]}]}, {"3": "tb7X-1647958696595", "4": {"version": 1}, "5": [{"2": "2", "3": "eWN3-1667182469108", "7": [{"8": "v-on:   相当于 @"}]}]}, {"3": "7E6m-1647958709339", "4": {"version": 1}, "5": [{"2": "2", "3": "T3eh-1667182469108", "7": [{"8": "v-slot:   相当于 #          v-slot 都作用于    <template>   标签中   （除了只有默认插槽的情况下可以写在组件标签上）"}]}]}, {"3": "FbH7-1647958728798", "4": {"version": 1}, "5": [{"2": "2", "3": "WQvx-1667182469108", "7": [{"8": "(slot-scope 2.6版之前，后面都是 v-slot)"}]}]}, {"3": "6CJj-1647958763187", "4": {"version": 1}, "5": [{"2": "2", "3": "Rb86-1667182469108", "7": [{"8": "v-slot:default   ===   #default   ===  什么都不写    "}, {"8": " 默认插槽", "9": [{"0": "#FFEE7C", "2": "bg"}]}]}]}, {"3": "vhB4-1647958796809", "4": {"version": 1}, "5": [{"2": "2", "3": "S2J2-1667182469108", "7": [{"8": "v-slot:footer  ===  #footer    "}, {"8": "具名插槽", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": "     （定义是   <slot  name=\"footer\">）"}]}]}, {"3": "2u46-1647958863064", "4": {"version": 1}, "5": [{"2": "2", "3": "STc0-1667182469108", "7": [{"8": "v-slot:footer=\"{ record }\"    ===    #footer=\"{ record }\"    "}, {"8": " 作用域插槽 ", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": "      （定义是   <slot  name=\"footer\"  :record=\"{ xxx: xxx }\">）   "}]}]}, {"3": "2Ego-1647835222026", "4": {"version": 1}, "5": [{"2": "2", "3": "799u-1667182469109"}]}, {"3": "P83D-1647835222234", "4": {"version": 1}, "5": [{"2": "2", "3": "DFRB-1667182469109"}]}, {"3": "AU3x-1647835222442", "4": {"version": 1}, "5": [{"2": "2", "3": "v5wW-1667182469109"}]}, {"3": "ZuhG-1647959214758", "4": {"version": 1}, "5": [{"2": "2", "3": "pMIR-1667182469109", "7": [{"8": "216、"}]}]}, {"3": "kyVn-1647959222326", "4": {"version": 1}, "5": [{"2": "2", "3": "EI4M-1667182469109"}]}, {"3": "p5MC-1753336470751", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12310/WEBRESOURCE8e843292e915ed4fd33430346fb7a559", "w": 468, "h": 613}, "6": "im"}, {"3": "dZcz-1647959222678", "4": {"version": 1}, "5": [{"2": "2", "3": "JFsn-1667182469109"}]}, {"3": "vNof-1647267981694", "4": {"version": 1}, "5": [{"2": "2", "3": "B65D-1667182469109"}]}, {"3": "XhLe-1647267976467", "4": {"version": 1}, "5": [{"2": "2", "3": "8JmD-1667182469109"}]}, {"3": "k5HZ-1648088820699", "4": {"version": 1}, "5": [{"2": "2", "3": "zPxY-1667182469109", "7": [{"8": "217、"}, {"8": " vue3 的子组件 接收  v-model  （可绑定 ", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "boolean / string / number / ", "9": [{"0": "#000000", "2": "c"}]}, {"8": "数组 ", "9": [{"0": "#000000", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "）   和 vue2 的区别", "9": [{"0": "#F33232", "2": "c"}]}]}]}, {"3": "iX1W-1648176228532", "4": {"version": 1}, "5": [{"2": "2", "3": "Y2A2-1667182469109", "7": [{"8": " v-model 绑定 元素（input、textarea、"}, {"8": "checkbox 单/多选框 加 value 属性", "9": [{"0": "#B620E0", "2": "c"}]}, {"8": "、"}, {"8": "radio 加 name 属性、", "9": [{"0": "#0091FF", "2": "c"}]}, {"8": "select） 和 组件"}]}]}, {"3": "W20s-1753336470752", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12315/WEBRESOURCE3e206d771f0e7699c5c8cc491c9a72ee", "w": 660, "h": 62}, "6": "im"}, {"3": "oKo3-1753336470753", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12313/WEBRESOURCE3d1eac72432b8bb5a0ce3929f4ca4770", "w": 333, "h": 89}, "6": "im"}, {"3": "ZLo3-1648176229184", "4": {"version": 1}, "5": [{"2": "2", "3": "QXhV-1667182469109", "7": [{"8": "直接套个label就行了，不用 for 和 id 的。"}]}]}, {"3": "FSzL-1753336470754", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12308/WEBRESOURCEeecb3d707d6780110fdd8ef495bbc997", "w": 737, "h": 288}, "6": "im"}, {"3": "D9DP-1753336470755", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12311/WEBRESOURCE459d3587103de2a62d56bc32cfd29a5b", "w": 675, "h": 234}, "6": "im"}, {"3": "rrvt-1753336470756", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12309/WEBRESOURCE5079602301d9c298be207b87ce83477b", "w": 699, "h": 193}, "6": "im"}, {"3": "A85M-1648176229396", "4": {"version": 1}, "5": [{"2": "2", "3": "bLJW-1667182469109", "7": [{"8": "有了 v-model，可以省略 name "}]}]}, {"3": "fydB-1661073756333", "4": {"version": 1}, "5": [{"2": "2", "3": "Qodn-1667182469109"}]}, {"3": "zmfM-1661073756514", "4": {"version": 1}, "5": [{"2": "2", "3": "wtzH-1667182469109"}]}, {"3": "okpW-1661073756673", "4": {"version": 1}, "5": [{"2": "2", "3": "0uXI-1667182469109", "7": [{"8": "218、前端页面做了校验？？不好意思，我 直接  接口文档（swagger）、Postman 改（插入）数据，直接改数据库， 不用过你前端校验，爱输什么输什么。。。"}]}]}, {"3": "5Mkc-1648212451335", "4": {"version": 1}, "5": [{"2": "2", "3": "HlMs-1667182469109"}]}, {"3": "UpYg-1648212451764", "4": {"version": 1}, "5": [{"2": "2", "3": "ACVT-1667182469109"}]}, {"3": "Eyqq-1648212452025", "4": {"version": 1}, "5": [{"2": "2", "3": "ErKX-1667182469109"}]}, {"3": "Wmls-1648212452287", "4": {"version": 1}, "5": [{"2": "2", "3": "g2F6-1667182469109"}]}, {"3": "57UW-1648212452475", "4": {"version": 1}, "5": [{"2": "2", "3": "1FKx-1667182469109", "7": [{"8": "219、 git 改 https 为  git://"}]}]}, {"3": "XaeE-1649944807345", "4": {"version": 1}, "5": [{"2": "2", "3": "J0zF-1667182469110"}]}, {"3": "iNoW-1649944807652", "4": {"version": 1}, "5": [{"2": "2", "3": "14Ub-1667182469110", "7": [{"8": "git config --", "9": [{"0": "#383a42", "2": "c"}, {"0": "rgb(250, 250, 250)", "2": "bg"}, {"0": 14, "2": "fs"}, {"0": "Courier New", "2": "ff"}]}, {"8": "global", "9": [{"0": 14, "2": "fs"}, {"0": "Courier New", "2": "ff"}, {"0": "#a626a4", "2": "c"}]}, {"8": " url.", "9": [{"0": 14, "2": "fs"}, {"0": "Courier New", "2": "ff"}, {"0": "#383a42", "2": "c"}, {"0": "rgb(250, 250, 250)", "2": "bg"}]}, {"8": "\"git://\"", "9": [{"0": 14, "2": "fs"}, {"0": "Courier New", "2": "ff"}, {"0": "#50a14f", "2": "c"}]}, {"8": ".", "9": [{"0": 14, "2": "fs"}, {"0": "Courier New", "2": "ff"}, {"0": "#383a42", "2": "c"}, {"0": "rgb(250, 250, 250)", "2": "bg"}]}, {"8": "insteadOf", "9": [{"0": 14, "2": "fs"}, {"0": "Courier New", "2": "ff"}, {"0": "#a626a4", "2": "c"}]}, {"8": " ", "9": [{"0": 14, "2": "fs"}, {"0": "Courier New", "2": "ff"}, {"0": "#383a42", "2": "c"}, {"0": "rgb(250, 250, 250)", "2": "bg"}]}, {"8": "https", "9": [{"0": 14, "2": "fs"}, {"0": "Courier New", "2": "ff"}, {"0": "#50a14f", "2": "c"}]}, {"8": ":", "9": [{"0": 14, "2": "fs"}, {"0": "Courier New", "2": "ff"}, {"0": "#383a42", "2": "c"}, {"0": "rgb(250, 250, 250)", "2": "bg"}]}, {"8": "//", "9": [{"0": 14, "2": "fs"}, {"0": "Courier New", "2": "ff"}, {"0": "#a0a1a7", "2": "c"}, {"2": "i"}]}]}]}, {"3": "LXmM-1649944773191", "4": {"version": 1}, "5": [{"2": "2", "3": "mvRb-1667182469110"}]}, {"3": "kji8-1649944773450", "4": {"version": 1}, "5": [{"2": "2", "3": "J0er-1667182469110"}]}, {"3": "W0QV-1649944773634", "4": {"version": 1}, "5": [{"2": "2", "3": "ZQLC-1667182469110", "7": [{"8": "220、 undefined  和  null  区别 ？？？"}]}]}, {"3": "Vjan-1650181257311", "4": {"version": 1}, "5": [{"2": "2", "3": "xdp1-1667182469110", "7": [{"8": "(1)、默认情况下，两者使用没啥区别 "}]}]}, {"3": "f6UH-1650181503844", "4": {"version": 1}, "5": [{"2": "2", "3": "9bPi-1667182469110", "7": [{"8": "let a = null;   let b = undefined;  两者的作用基本一致"}]}]}, {"3": "TKKP-1650181536725", "4": {"version": 1}, "5": [{"2": "2", "3": "hq1o-1667182469110", "7": [{"8": "然后双等号 判断 也是 true  .     undefined == null      // true  "}]}]}, {"3": "rW94-1650182901816", "4": {"version": 1}, "5": [{"2": "2", "3": "3neb-1667182469110"}]}, {"3": "jkFH-1650182481977", "4": {"version": 1}, "5": [{"2": "2", "3": "xvuQ-1667182469110", "7": [{"8": "(2)、 null 表示 “没有对象”，即此处不应该有值，"}, {"8": "转数值为0       ", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "作为对象原型链的终点", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "（  "}, {"8": "Object.getPrototypeOf(", "9": [{"0": "#B620E0", "2": "c"}]}, {"8": "Object.prototype", "9": [{"0": "#69D600", "2": "c"}]}, {"8": ")", "9": [{"0": "#B620E0", "2": "c"}]}, {"8": "  === null ）"}]}]}, {"3": "HSwH-1650182595874", "4": {"version": 1}, "5": [{"2": "2", "3": "jPYV-1667182469111"}]}, {"3": "q8rj-1650182596000", "4": {"version": 1}, "5": [{"2": "2", "3": "ILlJ-1667182469111", "7": [{"8": "         undefined 表示“缺少值“，即此处应该有一个值，但是未定义，"}, {"8": " 转数值为 NaN  ", "9": [{"0": "#F33232", "2": "c"}]}, {"8": " 函数无返回值为undefined", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}]}]}, {"3": "p7FK-1650181257804", "4": {"version": 1}, "5": [{"2": "2", "3": "8dzn-1667182469111"}]}, {"3": "jdml-1650181258504", "4": {"version": 1}, "5": [{"2": "2", "3": "GG2t-1667182469111", "7": [{"8": "typeof null === 'object'   ?     怎么理解？？？", "9": [{"0": "#0091FF", "2": "c"}]}]}]}, {"3": "iTGR-1650183610262", "4": {"version": 1}, "5": [{"2": "2", "3": "95Vd-1667182469111", "7": [{"8": "可以理解为是一个历史遗留的 Bug", "9": [{"0": "#F33232", "2": "c"}]}]}]}, {"3": "P4uS-1650184070582", "4": {"version": 1}, "5": [{"2": "2", "3": "OuuT-1667182469111", "7": [{"8": "js的最初版本是使用的32位系统，（js为了节约性能）使用低位存储变量的类型信息；判断数据类型时，是根据机器码的低位表示进行判断的，而 null 的机器码标识和 对象的机器码标识一样都是000，没有事先做过滤，导致误判了 null 为 'object'", "9": [{"0": "#000000", "2": "c"}]}]}]}, {"3": "Pqu4-1650183881013", "4": {"version": 1}, "5": [{"2": "2", "3": "xGhf-1667182469111"}]}, {"3": "Jvwg-1650183865434", "4": {"version": 1}, "5": [{"2": "2", "3": "KPE3-1667182469111", "7": [{"8": "js 中 变量没有类型，只有 值 才有 ！！！", "9": [{"0": "#B620E0", "2": "c"}]}]}]}, {"3": "yJGo-1650183610384", "4": {"version": 1}, "5": [{"2": "2", "3": "7tqV-1667182469111"}]}, {"3": "b77z-1650183435849", "4": {"version": 1}, "5": [{"2": "2", "3": "PKXQ-1667182469111"}]}, {"3": "2Cri-1650183436026", "4": {"version": 1}, "5": [{"2": "2", "3": "HqFg-1667182469111"}]}, {"3": "WurY-1650181258626", "4": {"version": 1}, "5": [{"2": "2", "3": "KnnU-1667182469111", "7": [{"8": "221、 npm run serve / dev / xxx"}]}]}, {"3": "QlnT-1753336470757", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12306/WEBRESOURCE256bfb115f6d2ebd85e3656b2da1c0da", "w": 709, "h": 500}, "6": "im"}, {"3": "n24w-1753336470758", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12307/WEBRESOURCE67cefc0d88027a556cc277e7b06f41c1", "w": 1039, "h": 701}, "6": "im"}, {"3": "c696-1753335819749", "4": {"version": 1}, "5": [{"2": "2", "3": "khuJ-1753335819750"}]}, {"3": "rKcW-1753336470759", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12303/WEBRESOURCE495d7b1338b31ab269fedb68c8af154b", "w": 935, "h": 151}, "6": "im"}, {"3": "BVOD-1650193118333", "4": {"version": 1}, "5": [{"2": "2", "3": "wyTK-1667182469111"}]}, {"3": "J0eU-1650193329967", "4": {"version": 1}, "5": [{"2": "2", "3": "thOr-1667182469111"}]}, {"3": "EVBK-1650193330089", "4": {"version": 1}, "5": [{"2": "2", "3": "IeoQ-1667182469111"}]}, {"3": "kwA1-1650193330220", "4": {"version": 1}, "5": [{"2": "2", "3": "gUHz-1667182469111", "7": [{"8": "222、   async-validator 的 rules集合中的每个对象的type默认是String类型的，做校验的时候可能会因为 number 和 string 来回切换 导致 校验不到 "}]}]}, {"3": "rynH-1650556043156", "4": {"version": 1}, "5": [{"2": "2", "3": "LC46-1667182469111"}]}, {"3": "PZVc-1650556043311", "4": {"version": 1}, "5": [{"2": "2", "3": "YpLL-1667182469111"}]}, {"3": "fYwe-1650556043447", "4": {"version": 1}, "5": [{"2": "2", "3": "mkFs-1667182469111", "7": [{"8": "223、"}, {"8": "动态类名", "9": [{"0": "#F33232", "2": "c"}]}, {"8": " - 鼠标点击增加样式（文字颜色改变）"}]}]}, {"3": "QLjv-1753336470760", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12302/WEBRESOURCE8fc18c04d74a7e77666a48c381ec4f87", "w": 1419, "h": 33}, "6": "im"}, {"3": "NLzT-1650556200884", "4": {"version": 1}, "5": [{"2": "2", "3": "55yU-1667182469111"}]}, {"3": "2HNV-1658744346458", "4": {"version": 1}, "5": [{"2": "2", "3": "YfQf-1667182469111", "7": [{"8": "  "}]}]}, {"3": "1f3B-1753336470761", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12300/WEBRESOURCE01af0813c816e6d99c2f04bf404822db", "w": 304, "h": 77}, "6": "im"}, {"3": "Sbr3-1753336470762", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12304/WEBRESOURCE004f3bf9041340fdf902e6cdffc9e874", "w": 300, "h": 86}, "6": "im"}, {"3": "26wP-1753336470763", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12301/WEBRESOURCE166530e289b7f58828a3b517c2b8f238", "w": 170, "h": 192}, "6": "im"}, {"3": "QUwA-1650556233169", "4": {"version": 1}, "5": [{"2": "2", "3": "jy5n-1667182469112"}]}, {"3": "dRlG-1650556233418", "4": {"version": 1}, "5": [{"2": "2", "3": "EqJL-1667182469112", "7": [{"8": "classList", "9": [{"0": "#e06c75", "2": "c"}]}, {"8": ".", "9": [{"2": "b"}, {"0": "#838fa7", "2": "c"}]}, {"8": "contains", "9": [{"2": "b"}, {"0": "#6495ee", "2": "c"}]}, {"8": "(", "9": [{"0": "#838fa7", "2": "c"}]}, {"8": "'active'", "9": [{"0": "#98c379", "2": "c"}]}, {"8": ")   判断是否有该类名 做样式切换的时候", "9": [{"0": "#838fa7", "2": "c"}]}]}]}, {"3": "hhpd-1650556695856", "4": {"version": 1}, "5": [{"2": "2", "3": "8vIG-1667182469112"}]}, {"3": "1PPp-1650556233603", "4": {"version": 1}, "5": [{"2": "2", "3": "Npqo-1667182469112"}]}, {"3": "P8bK-1650556267650", "4": {"version": 1}, "5": [{"2": "2", "3": "XPEu-1667182469112", "7": [{"8": "224、  git commit emoji:  "}]}]}, {"3": "MbU7-1663138391498", "4": {"version": 1}, "5": [{"2": "2", "3": "cOQJ-1667182469112"}]}, {"3": "EWvS-1668499885687", "4": {"version": 1}, "5": [{"2": "2", "3": "aC43-1668499885630", "7": [{"8": "git config core.ignorecase false", "9": [{"0": "#F33232", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 14, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}, {"2": "b"}]}, {"8": "    配置 vscode 修改文件大小写时，当创建新的文件。不会导致 修改大小写时 远程仓库没更新到 。。。", "9": [{"0": "#566573", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 14, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}, {"2": "b"}]}]}]}, {"3": "lRqT-1668499886127", "4": {"version": 1}, "5": [{"2": "2", "3": "lVgY-1668499886085"}]}, {"3": "lauo-1650702454409", "4": {"version": 1}, "5": [{"2": "2", "3": "nkUS-1667182469112", "7": [{"8": "这是 git 内部就支持的功能，不用额外装什么插件显示对应的图标就可以看到对应的提交图标。就提交的时候在提交信息前面加上对应的 "}, {"8": ":xxx:", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": " 语法就可以了（可以装提交选择图标的插件）"}]}]}, {"3": "F3AZ-1663138389419", "4": {"version": 1}, "5": [{"2": "2", "3": "QAWF-1667182469112"}]}, {"3": "R98T-1650702398690", "4": {"version": 1}, "5": [{"2": "2", "3": "eXAF-1667182469112", "7": [{"8": ":recycle: refactor:", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": " 重构 "}]}]}, {"3": "WhrW-1650702406906", "4": {"version": 1}, "5": [{"2": "2", "3": "SXtO-1667182469112", "7": [{"8": ":zap: perf:", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": " 优化 "}]}]}, {"3": "hKmk-1650702406906", "4": {"version": 1}, "5": [{"2": "2", "3": "yHsW-1667182469112", "7": [{"8": ":lipstick: style:", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": " 样式     "}]}]}, {"3": "gZCA-1650702406906", "4": {"version": 1}, "5": [{"2": "2", "3": "oFP7-1667182469112", "7": [{"8": ":memo: docs:", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": " 文档"}]}]}, {"3": "4coc-1650702406906", "4": {"version": 1}, "5": [{"2": "2", "3": "qbmd-1667182469112", "7": [{"8": ":art: chore: ", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": "配置修改"}]}]}, {"3": "qFXB-1650702406906", "4": {"version": 1}, "5": [{"2": "2", "3": "aU7a-1667182469112", "7": [{"8": ":sparkles: feat: ", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": "新功能/特性"}]}]}, {"3": "ZOMs-1650702406906", "4": {"version": 1}, "5": [{"2": "2", "3": "4POz-1667182469112", "7": [{"8": ":bug: fix:", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": " 修改bug"}]}]}, {"3": "7cCl-1654855189323", "4": {"version": 1}, "5": [{"2": "2", "3": "19DX-1667182469112", "7": [{"8": ":bookmark: carry:", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": " 不同仓库（项目）相同代码搬运  (自定义的)"}]}]}, {"3": "lTS0-1663138528878", "4": {"version": 1}, "5": [{"2": "2", "3": "cGsm-1667182469112"}]}, {"3": "M2M2-1650702515357", "4": {"version": 1}, "5": [{"2": "2", "3": "IjWT-1667182469112"}]}, {"3": "bw0q-1753336470764", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12298/WEBRESOURCEab3f76e238b2be844136be32cac76a5c", "w": 718, "h": 1061}, "6": "im"}, {"3": "4xpA-1650556161929", "4": {"version": 1}, "5": [{"2": "2", "3": "dKmn-1667182469112"}]}, {"3": "M9PM-1650702457398", "4": {"version": 1}, "5": [{"2": "2", "3": "QrFA-1667182469112"}]}, {"3": "70Mc-1650702457544", "4": {"version": 1}, "5": [{"2": "2", "3": "vIE5-1667182469113", "7": [{"8": "225、 执行顺序  vue 模板 执行顺序： "}]}]}, {"3": "XhBY-1650708624539", "4": {"version": 1}, "5": [{"2": "2", "3": "xJ2e-1667182469113", "7": [{"8": "  "}, {"8": "render => template => el", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}, {"0": 18, "2": "fs"}]}, {"8": "    el ---->    $refs.idName.$el  （整个Vue实例的 DOM 对象）"}]}]}, {"3": "434K-1650708647671", "4": {"version": 1}, "5": [{"2": "2", "3": "DxrR-1667182469113", "7": [{"8": " "}]}]}, {"3": "q0bG-1753336470765", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12299/WEBRESOURCE992b7dc56b7c6654847a32d2abb75168", "w": 461, "h": 344}, "6": "im"}, {"3": "uOp1-1650702499137", "4": {"version": 1}, "5": [{"2": "2", "3": "gQdu-1667182469113"}]}, {"3": "8VXe-1665285192428", "4": {"version": 1}, "5": [{"2": "2", "3": "7Hip-1667182469113"}]}, {"3": "L1XF-1665285192774", "4": {"version": 1}, "5": [{"2": "2", "3": "Cgky-1667182469113"}]}, {"3": "Fq4l-1665285193198", "4": {"version": 1}, "5": [{"2": "2", "3": "JANQ-1667182469113"}]}, {"3": "py9s-1665285193527", "4": {"version": 1}, "5": [{"2": "2", "3": "A4GV-1667182469113"}]}, {"3": "KcAf-1665285193857", "4": {"version": 1}, "5": [{"2": "2", "3": "IsIL-1667182469113"}]}, {"3": "xMsr-1650702458143", "4": {"version": 1}, "5": [{"2": "2", "3": "XCg2-1667182469113"}]}, {"3": "LV4L-1650556162258", "4": {"version": 1}, "5": [{"2": "2", "3": "wXhB-1667182469113", "7": [{"8": "226、 websocket", "9": [{"0": "#B620E0", "2": "c"}]}, {"8": " 与 后端的通信 "}]}]}, {"3": "uOEA-1650722191224", "4": {"version": 1}, "5": [{"2": "2", "3": "DLx1-1667182469113", "7": [{"8": "1）、与 http 比 ？  服务端可 "}, {"8": "主动", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": " "}, {"8": "推送消息", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": " 到客户端  不用像登录扫二维码那样 不断的发送 http请求 轮询 接口，直到有返回数据才进行下一步的操作（页面跳转）"}]}]}, {"3": "OIjy-1650722292446", "4": {"version": 1}, "5": [{"2": "2", "3": "vUUn-1667182469113", "7": [{"8": "2）、大致使用？"}]}]}, {"3": "W3E7-1650723497567", "4": {"version": 1}, "5": [{"2": "2", "3": "GHgu-1667182469113", "7": [{"8": "⚪ 首先要装一个依赖包 reconnecting-websocket    ws掉线自动重连"}]}]}, {"3": "UpvN-1650723865496", "4": {"version": 1}, "5": [{"2": "2", "3": "imqk-1667182469113", "7": [{"8": "使用： "}]}]}, {"3": "vTjo-1650723918942", "4": {"version": 1, "la": "javascript", "th": "default"}, "5": [{"3": "rqWx-1667182469113", "5": [{"2": "2", "3": "CBKQ-1667182469113", "7": [{"8": "const ws = new ReconnectingWebSocket('ws://....');"}]}], "6": "cl"}], "6": "cd"}, {"3": "0AbB-1650723404283", "4": {"version": 1}, "5": [{"2": "2", "3": "twso-1667182469114", "7": [{"8": "⚪ 然后初始化一个 ws"}]}]}, {"3": "kSSt-1650724023200", "4": {"version": 1, "la": "javascript", "th": "default"}, "5": [{"3": "iQpl-1667182469114", "5": [{"2": "2", "3": "EfCK-1667182469114", "7": [{"8": "import ReconnectingWebSocket from 'reconnecting-websocket'"}]}], "6": "cl"}, {"3": "vB5O-1667182469114", "5": [{"2": "2", "3": "CLsF-1667182469114"}], "6": "cl"}, {"3": "1BBg-1667182469114", "5": [{"2": "2", "3": "glYa-1667182469114", "7": [{"8": "initWebsocket(wsUrl, protocols) {"}]}], "6": "cl"}, {"3": "bdHq-1667182469114", "5": [{"2": "2", "3": "r3od-1667182469114", "7": [{"8": "    const debug = process.env.NODE_ENV === 'development' // ws实例是否打印 debug 信息，默认是 false"}]}], "6": "cl"}, {"3": "k41O-1667182469114", "5": [{"2": "2", "3": "pi5N-1667182469114", "7": [{"8": "    const options = {"}]}], "6": "cl"}, {"3": "J4in-1667182469114", "5": [{"2": "2", "3": "gmxH-1667182469114", "7": [{"8": "        connectionTimeout: 1000, // 设置超时重连时间（没连上的时候一秒请求一次连接）"}]}], "6": "cl"}, {"3": "KRpz-1667182469114", "5": [{"2": "2", "3": "4IrG-1667182469114", "7": [{"8": "        maxRetries: 100, // 最多重连次数"}]}], "6": "cl"}, {"3": "A8ek-1667182469114", "5": [{"2": "2", "3": "wBy7-1667182469114", "7": [{"8": "        debug    "}]}], "6": "cl"}, {"3": "Z0SD-1667182469114", "5": [{"2": "2", "3": "g3y0-1667182469114", "7": [{"8": "    }"}]}], "6": "cl"}, {"3": "Qnz5-1667182469114", "5": [{"2": "2", "3": "MPEL-1667182469114", "7": [{"8": "    "}]}], "6": "cl"}, {"3": "88tB-1667182469114", "5": [{"2": "2", "3": "tmq0-1667182469114", "7": [{"8": "    return new ReconnectingWebSocket(wsUrl, protocols, options)"}]}], "6": "cl"}, {"3": "WuoE-1667182469114", "5": [{"2": "2", "3": "AP3K-1667182469114", "7": [{"8": "}"}]}], "6": "cl"}], "6": "cd"}, {"3": "btI0-1650723967017", "4": {"version": 1}, "5": [{"2": "2", "3": "AXMA-1667182469114", "7": [{"8": "⚪ 在主布局页面 进行 ws 的连接"}]}]}, {"3": "hrOl-1753336470766", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12295/WEBRESOURCE910c60c700047b0e86a53550cb7fcbda", "w": 1038, "h": 480}, "6": "im"}, {"3": "eB1d-1650724488591", "4": {"version": 1}, "5": [{"2": "2", "3": "aWCB-1667182469114", "7": [{"8": "详细代码为："}]}]}, {"3": "vkJB-1650724787589", "4": {"version": 1, "la": "javascript", "th": "default"}, "5": [{"3": "oqtp-1667182469114", "5": [{"2": "2", "3": "F8YB-1667182469114", "7": [{"8": "import store from 'store'"}]}], "6": "cl"}, {"3": "F0rn-1667182469114", "5": [{"2": "2", "3": "18pG-1667182469114", "7": [{"8": "import expirePlugin from 'store/plugins/expire'"}]}], "6": "cl"}, {"3": "BdCR-1667182469114", "5": [{"2": "2", "3": "yNUs-1667182469114", "7": [{"8": "const storage = store.addPlugin(expirePlugin)"}]}], "6": "cl"}, {"3": "Agbw-1667182469114", "5": [{"2": "2", "3": "LGR8-1667182469114"}], "6": "cl"}, {"3": "BEYv-1667182469114", "5": [{"2": "2", "3": "w9W5-1667182469114", "7": [{"8": "websocketConnect() {"}]}], "6": "cl"}, {"3": "xkKr-1667182469114", "5": [{"2": "2", "3": "veT4-1667182469114", "7": [{"8": "    const url = process.env.VUE_APP_WB_URL || null  // ws 的请求 api"}]}], "6": "cl"}, {"3": "rYVc-1667182469114", "5": [{"2": "2", "3": "c7V5-1667182469114", "7": [{"8": "    const token = storage.get(ACCESS_TOKEN)"}]}], "6": "cl"}, {"3": "vAij-1667182469114", "5": [{"2": "2", "3": "HMLJ-1667182469114", "7": [{"8": "    "}]}], "6": "cl"}, {"3": "3P0g-1667182469114", "5": [{"2": "2", "3": "ZMre-1667182469114", "7": [{"8": "    // ...详细代码看下面"}]}], "6": "cl"}, {"3": "dCi5-1667182469114", "5": [{"2": "2", "3": "FyR3-1667182469114", "7": [{"8": "}"}]}], "6": "cl"}], "6": "cd"}, {"3": "a16X-1650722686399", "4": {"version": 1}, "5": [{"2": "2", "3": "UkJy-1667182469114", "7": [{"8": "首先会发送一个 websocket 的请求， 请求与服务端建立连接，状态码 101 表示 升级协议"}]}]}, {"3": "9b0X-1650722326581", "4": {"version": 1}, "5": [{"2": "2", "3": "0ibz-1667182469114", "7": [{"8": "initWebsocket()  方法"}]}]}, {"3": "5Zhf-1753336470767", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12312/WEBRESOURCE147a127aaf6f65d6adb1c2ffbf28af51", "w": 826, "h": 735}, "6": "im"}, {"3": "YS1H-1650722345001", "4": {"version": 1}, "5": [{"2": "2", "3": "6fQn-1667182469114"}]}, {"3": "QFm4-1650722859703", "4": {"version": 1}, "5": [{"2": "2", "3": "qpok-1667182469114", "7": [{"8": "连接服务器的ws地址"}]}]}, {"3": "wUEb-1753336470768", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12297/WEBRESOURCE28ec2429f301372d67e057001a1d4e7b", "w": 1051, "h": 247}, "6": "im"}, {"3": "xG7B-1650556155591", "4": {"version": 1}, "5": [{"2": "2", "3": "zGqu-1667182469114"}]}, {"3": "Jb3D-1650722538911", "4": {"version": 1}, "5": [{"2": "2", "3": "Txx1-1667182469114"}]}, {"3": "uQt0-1650722539102", "4": {"version": 1}, "5": [{"2": "2", "3": "bN04-1667182469115", "7": [{"8": "227、  "}, {"8": "判断字符串是否是yyyy-mm-dd的日期格式", "9": [{"0": "#B620E0", "2": "c"}, {"0": 16, "2": "fs"}]}]}]}, {"3": "afa5-1753336470769", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12305/WEBRESOURCEbc496b550057046ae602e9e56d5f4a98", "w": 782, "h": 68}, "6": "im"}, {"3": "LLxB-1753335565452", "4": {"version": 1}, "5": [{"2": "2", "3": "xOvP-1753335565453"}]}, {"3": "49MK-1753336470770", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12294/WEBRESOURCE0f783eef70873c755b62df7ca81bac2e", "w": 547, "h": 146}, "6": "im"}, {"3": "84wI-1650730336997", "4": {"version": 1}, "5": [{"2": "2", "3": "g5YR-1667182469115", "7": [{"8": "targetLength", "9": [{"0": 14, "2": "fs"}]}]}]}, {"3": "R1Pd-1650730326125", "4": {"version": 1}, "5": [{"2": "2", "3": "dKWo-1667182469115", "7": [{"8": "当前字符串需要填充到的目标长度。"}, {"8": "如果这个数值小于当前字符串的长度，则返回当前字符串本身", "9": [{"2": "u"}]}, {"8": "。"}]}]}, {"3": "n2cy-1650730326125", "4": {"version": 1}, "5": [{"2": "2", "3": "W1Vo-1667182469115", "7": [{"8": "padString", "9": [{"0": 14, "2": "fs"}]}, {"8": " "}, {"8": "可选", "9": [{"0": "#000000", "2": "c"}, {"0": 14, "2": "fs"}]}]}]}, {"3": "FNY3-1650730326125", "4": {"version": 1}, "5": [{"2": "2", "3": "HroP-1667182469115", "7": [{"8": "填充字符串。如果字符串太长，使填充后的字符串长度超过了目标长度，则只保留最左侧的部分，其他部分会被截断。"}, {"8": "此参数的默认值为 \" \"", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": "（U+0020）。"}]}]}, {"3": "nJKe-1650730274466", "4": {"version": 1}, "5": [{"2": "2", "3": "BxER-1667182469115"}]}, {"3": "Qv14-1650730358251", "4": {"version": 1}, "5": [{"2": "2", "3": "DGUi-1667182469115", "7": [{"8": "// 判断字符串是否是yyyy-mm-dd的日期格式 包括日期正确性校验\nconst judgeDate = (() => {\n  function format(d) {\n    return [\n      d.getFullYear(),\n      `${(d.getMonth() + 1)}`.padStart(2, \"0\"),\n      `${(d.getDate())}`.padStart(2, \"0\"),\n    ].join(\"-\");\n  }\n  return s => format(new Date(s)) === s;   "}, {"8": "// 返回值是一个函数 而且这里没有其他代码有调用这个箭头函数 正所谓 函数不调用就不执行   而这里用了立即执行函数 所以就执行了  最终的返回值是这个箭头函数的返回值！！！", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "\n})()"}]}]}, {"3": "IfAU-1650731738680", "4": {"version": 1}, "5": [{"2": "2", "3": "Mqo8-1667182469115"}]}, {"3": "o7Uu-1753336470771", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12296/WEBRESOURCEada057685356a35922047dfe250ac859", "w": 540, "h": 304}, "6": "im"}, {"3": "rquz-1650730372123", "4": {"version": 1}, "5": [{"2": "2", "3": "zIDf-1667182469115", "7": [{"8": "IEFF 立即执行函数 "}]}]}, {"3": "pUKH-1753336470772", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12291/WEBRESOURCE36287e5c5e3faede8e5a05a11d61d562", "w": 599, "h": 334}, "6": "im"}, {"3": "IRWN-1650731074878", "4": {"version": 1}, "5": [{"2": "2", "3": "iOpx-1667182469115"}]}, {"3": "0AJx-1650731412527", "4": {"version": 1}, "5": [{"2": "2", "3": "VF3K-1667182469115", "7": [{"8": "命名空间 - 闭包环境 ？ （", "9": [{"0": "#B620E0", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": "里面可以访问外面", "9": [{"0": "#B620E0", "2": "c"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "，外面不能访问里面，不会造成命名冲突，变量污染）", "9": [{"0": "#B620E0", "2": "c"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}, {"0": "rgb(255, 255, 255)", "2": "bg"}]}]}]}, {"3": "U8hq-1650731476799", "4": {"version": 1}, "5": [{"2": "2", "3": "45k8-1667182469115", "7": [{"8": "闭包： ", "9": [{"0": "#333333", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": "内层的函数可以使用外层函数的所有变量", "9": [{"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}, {"0": "#F33232", "2": "c"}]}, {"8": "，即使外层函数已经执行完毕", "9": [{"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}, {"0": "#333333", "2": "c"}]}]}]}, {"3": "k7tS-1650731412748", "4": {"version": 1}, "5": [{"2": "2", "3": "O4br-1667182469115"}]}, {"3": "INMo-1650731706609", "4": {"version": 1}, "5": [{"2": "2", "3": "9MLo-1667182469116"}]}, {"3": "J3zi-1650731706746", "4": {"version": 1}, "5": [{"2": "2", "3": "XEbt-1667182469116"}]}, {"3": "rVWO-1650730358386", "4": {"version": 1}, "5": [{"2": "2", "3": "YgfE-1667182469116", "7": [{"8": "228、 "}, {"8": "扩展运算符", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "解构对象，"}, {"8": "单独定义相同字段", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "，不影响原对象的该字段。"}]}]}, {"3": "FRmB-1651154234284", "4": {"version": 1}, "5": [{"2": "2", "3": "IX1r-1667182469116"}]}, {"3": "i9fL-1753336470773", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12292/WEBRESOURCE2f701cfbdf87097a56872dde6ad77d69", "w": 1058, "h": 118}, "6": "im"}, {"3": "6JIQ-1650730358516", "4": {"version": 1}, "5": [{"2": "2", "3": "yudt-1667182469116"}]}, {"3": "4Umu-1651154319149", "4": {"version": 1}, "5": [{"2": "2", "3": "qSow-1667182469116"}]}, {"3": "SH1B-1651154319283", "4": {"version": 1}, "5": [{"2": "2", "3": "Syd6-1667182469116", "7": [{"8": "229、"}, {"8": "前端利用 jsencript.js 进行 RSA 加密", "9": [{"0": 16, "2": "fs"}]}]}]}, {"3": "aOvC-1651154319431", "4": {"version": 1}, "5": [{"2": "2", "3": "gTJ3-1667182469116", "7": [{"8": "必须有私钥才能对 对应的公钥进行解密", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "  （有公钥是不可能拿得到私钥的，私钥应该可以获取对应的公钥的）"}]}]}, {"3": "1zBJ-1652283129569", "4": {"version": 1}, "5": [{"2": "2", "3": "0SQQ-1667182469116"}]}, {"3": "guyH-1652283135682", "4": {"version": 1}, "5": [{"2": "2", "3": "qyfi-1667182469116", "7": [{"8": "对称： 通信双方公用同一密钥"}]}]}, {"3": "j9Bc-1652283129717", "4": {"version": 1}, "5": [{"2": "2", "3": "6sZJ-1667182469116", "7": [{"8": "非对称： 公钥和私钥，公钥加密、私钥解密"}]}]}, {"3": "c7K7-1652283129887", "4": {"version": 1}, "5": [{"2": "2", "3": "gP4q-1667182469116"}]}, {"3": "pnFz-1651155568260", "4": {"version": 1}, "5": [{"2": "2", "3": "Suq3-1667182469116", "7": [{"8": "RSA 加密： 非对称加密算法", "9": [{"0": 16, "2": "fs"}, {"2": "b"}]}]}]}, {"3": "N3n8-1651155627281", "4": {"version": 1}, "5": [{"2": "2", "3": "5JU7-1667182469116", "7": [{"8": "使用： 需要一对 ", "9": [{"0": 16, "2": "fs"}, {"2": "b"}]}, {"8": "密钥 ，", "9": [{"0": 16, "2": "fs"}, {"0": "#404040", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": "公钥和私钥 （一般公钥加密，私钥解密）,终端跑命令生成", "9": [{"0": 16, "2": "fs"}, {"2": "b"}]}]}]}, {"3": "xC5C-1651156627900", "4": {"version": 1}, "5": [{"2": "2", "3": "rWqz-1667182469116", "7": [{"8": "公钥和密钥： 一组数字，其二进制长度为1024位 / 2048位", "9": [{"0": 16, "2": "fs"}, {"2": "b"}]}]}]}, {"3": "reRT-1651155568398", "4": {"version": 1}, "5": [{"2": "2", "3": "vCtg-1667182469116", "7": [{"8": "使用库进行加解密"}]}]}, {"3": "xe8N-1651157064390", "4": {"version": 1, "la": "javascript", "th": "default"}, "5": [{"3": "g3Ws-1667182469117", "5": [{"2": "2", "3": "olIy-1667182469117", "7": [{"8": "import JSEncrypt from 'jsencrypt/bin/jsencrypt'"}]}], "6": "cl"}, {"3": "zY6v-1667182469117", "5": [{"2": "2", "3": "WZVX-1667182469117", "7": [{"8": "// 密钥对生成 http://web.chacuo.net/netrsakeypair"}]}], "6": "cl"}, {"3": "bsuL-1667182469117", "5": [{"2": "2", "3": "sksw-1667182469117", "7": [{"8": "const publicKey ="}]}], "6": "cl"}, {"3": "1Ffu-1667182469117", "5": [{"2": "2", "3": "X9tj-1667182469117", "7": [{"8": "  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBtToApvTzfpax/8OyYkVPKiCx0AFG5AXyZheW3CnL8LE0gQSKEpxghx8Odw306Ne1uOR5aNRMjaD4V9q5TDWtyiC28MgPALqOBZGVA3ZV7rx0xeuGu5IKJrtIQyICkRaKH4v+CRZnEAwGrBVxzjBPxUXlkC/TLpLFBSrtmrN99wIDAQAB'"}]}], "6": "cl"}, {"3": "h1wp-1667182469117", "5": [{"2": "2", "3": "He2x-1667182469117", "7": [{"8": "const privateKey = ''"}]}], "6": "cl"}], "6": "cd"}, {"3": "oWSl-1651157064390", "4": {"version": 1}, "5": [{"2": "2", "3": "p1zG-1667182469117"}]}, {"3": "ovYG-1651157029166", "4": {"version": 1, "la": "javascript", "th": "default"}, "5": [{"3": "R19v-1667182469117", "5": [{"2": "2", "3": "EbxH-1667182469117", "7": [{"8": "// 加密"}]}], "6": "cl"}, {"3": "SSeQ-1667182469117", "5": [{"2": "2", "3": "tjcM-1667182469117", "7": [{"8": "export function encrypt(txt) {"}]}], "6": "cl"}, {"3": "5fgu-1667182469117", "5": [{"2": "2", "3": "74dD-1667182469117", "7": [{"8": "  const encryptor = new JSEncrypt()"}]}], "6": "cl"}, {"3": "lSdy-1667182469117", "5": [{"2": "2", "3": "ARl3-1667182469117", "7": [{"8": "  encryptor.setPublicKey(publicKey) // 设置公钥"}]}], "6": "cl"}, {"3": "LVNM-1667182469117", "5": [{"2": "2", "3": "SLvI-1667182469117", "7": [{"8": "  return encryptor.encrypt(txt) // 对需要加密的数据进行加密"}]}], "6": "cl"}, {"3": "a2Xz-1667182469117", "5": [{"2": "2", "3": "J0t3-1667182469117", "7": [{"8": "}"}]}], "6": "cl"}], "6": "cd"}, {"3": "JRoY-1651157048481", "4": {"version": 1}, "5": [{"2": "2", "3": "7eDV-1667182469117"}]}, {"3": "bgtd-1651157048481", "4": {"version": 1, "la": "javascript", "th": "default"}, "5": [{"3": "jkLR-1667182469117", "5": [{"2": "2", "3": "2Nao-1667182469117", "7": [{"8": "// 解密"}]}], "6": "cl"}, {"3": "o9C0-1667182469117", "5": [{"2": "2", "3": "wVfn-1667182469117", "7": [{"8": "export function decrypt(txt) {"}]}], "6": "cl"}, {"3": "2AT6-1667182469117", "5": [{"2": "2", "3": "rw7W-1667182469117", "7": [{"8": "  const encryptor = new JSEncrypt()"}]}], "6": "cl"}, {"3": "cbLo-1667182469117", "5": [{"2": "2", "3": "fUTa-1667182469117", "7": [{"8": "  encryptor.setPrivateKey(privateKey)"}]}], "6": "cl"}, {"3": "D8AG-1667182469117", "5": [{"2": "2", "3": "UvHL-1667182469117", "7": [{"8": "  return encryptor.decrypt(txt)"}]}], "6": "cl"}, {"3": "Wexd-1667182469117", "5": [{"2": "2", "3": "x8tP-1667182469117", "7": [{"8": "}"}]}], "6": "cl"}], "6": "cd"}, {"3": "7DWn-1651157163855", "4": {"version": 1}, "5": [{"2": "2", "3": "E4sx-1667182469117"}]}, {"3": "c4Pj-1651157163855", "4": {"version": 1, "la": "javascript", "th": "default"}, "5": [{"3": "fed8-1667182469117", "5": [{"2": "2", "3": "0pOl-1667182469117", "7": [{"8": "对登录密码进行加密", "9": [{"0": "#838fa7", "2": "c"}]}]}], "6": "cl"}], "6": "cd"}, {"3": "We32-1753336470774", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12293/WEBRESOURCE3d62892e50050e0c2f7d99683432a269", "w": 800, "h": 250}, "6": "im"}, {"3": "TBoZ-1643096939923", "4": {"version": 1}, "5": [{"2": "2", "3": "tYAs-1667182469117"}]}, {"3": "Zlwj-1651157230881", "4": {"version": 1}, "5": [{"2": "2", "3": "Fbk9-1667182469117"}]}, {"3": "u3GZ-1651157231158", "4": {"version": 1}, "5": [{"2": "2", "3": "PKRd-1667182469118", "7": [{"8": "window.btoa(", "9": [{"0": "#F33232", "2": "c"}, {"2": "bg"}]}, {"8": "'字符串'", "9": [{"2": "bg"}, {"0": "#B3B3B3", "2": "c"}]}, {"8": ")", "9": [{"2": "bg"}, {"0": "#F33232", "2": "c"}]}, {"8": " 和", "9": [{"2": "bg"}, {"0": "#000000", "2": "c"}]}, {"8": " window.atob(", "9": [{"2": "bg"}, {"0": "#F33232", "2": "c"}]}, {"8": "'bsae64编码字符串'", "9": [{"2": "bg"}, {"0": "#B3B3B3", "2": "c"}]}, {"8": ")  ", "9": [{"2": "bg"}, {"0": "#F33232", "2": "c"}]}, {"8": " base64编解码 （js原生方法）  -- 对应有 js-base64 的库可使用", "9": [{"2": "bg"}, {"0": "#000000", "2": "c"}]}]}]}, {"3": "cHyo-1651157582468", "4": {"version": 1}, "5": [{"2": "2", "3": "mE0z-1667182469118", "7": [{"8": "作用： 简单加密，bsae64编码兼容特殊字符，对应", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}, {"8": "ASCII 字符串", "9": [{"0": "#1b1b1b", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}]}]}]}, {"3": "5cHB-1651157231416", "4": {"version": 1}, "5": [{"2": "2", "3": "t3Pq-1667182469118", "7": [{"8": "使用：", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "6sMD-1753336470775", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12290/WEBRESOURCE4cc5b6c8b4a4967d84da4c2aac219364", "w": 402, "h": 90}, "6": "im"}, {"3": "x9Wt-1651157562868", "4": {"version": 1}, "5": [{"2": "2", "3": "Zbnp-1667182469118"}]}, {"3": "W5Q4-1651157182300", "4": {"version": 1}, "5": [{"2": "2", "3": "8Gun-1667182469118"}]}, {"3": "iRXn-1651158035847", "4": {"version": 1}, "5": [{"2": "2", "3": "ztQT-1667182469118"}]}, {"3": "D37U-1651158035980", "4": {"version": 1}, "5": [{"2": "2", "3": "CyHr-1667182469118", "7": [{"8": "230、 根据不同的终端设备显示不同的页面（非一套布局方案的响应式），是写两套的, PC + 移动端 mobile", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "XKr9-1651678680904", "4": {"version": 1}, "5": [{"2": "2", "3": "z93w-1667182469118"}]}, {"3": "rMlT-1651678682673", "4": {"version": 1}, "5": [{"2": "2", "3": "9tgw-1667182469118", "7": [{"8": "1、首先，根据 ", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}, {"8": "navigator.userAgent ", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "  判断 当前终端是 PC还是移动端", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "C6ZU-1651678738683", "4": {"version": 1}, "5": [{"2": "2", "3": "DlzQ-1667182469118", "7": [{"8": "2、然后在打包后的dist文件夹中的 index.html 中进行判断，引入不同的静态文件", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "cgQX-1651678879182", "4": {"version": 1}, "5": [{"2": "2", "3": "Skfw-1667182469118", "7": [{"8": "vue打包过后生成dist文件,对比两套生成的dist文件，将共同的css，js写在一起，不同的通过", "9": [{"0": "#555666", "2": "c"}, {"0": "rgb(238, 240, 244)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}]}]}, {"3": "SWMD-1651678925083", "4": {"version": 1}, "5": [{"2": "2", "3": "DjHn-1667182469119", "7": [{"8": "document.write", "9": [{"0": "#F33232", "2": "c"}]}, {"8": " "}, {"8": "写到文档中，当然也可以用响应式的布局，但是响应式的布局加载慢，写法麻烦后期难以维护，小页面还好大的项目的不同点太多了，几乎跟写两套没什么区别", "9": [{"0": "#555666", "2": "c"}, {"0": "rgb(238, 240, 244)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}]}]}, {"3": "H45V-1651678973244", "4": {"version": 1, "la": "javascript", "th": "default"}, "5": [{"3": "P4eI-1667182469119", "5": [{"2": "2", "3": "bSeM-1667182469119", "7": [{"8": "function IsPC() {"}]}], "6": "cl"}, {"3": "MoF9-1667182469119", "5": [{"2": "2", "3": "6qYb-1667182469119", "7": [{"8": "  const userAgentInfo = navigator.userAgent;"}]}], "6": "cl"}, {"3": "URR1-1667182469119", "5": [{"2": "2", "3": "OKXi-1667182469119", "7": [{"8": "  const Agents = [\"Android\", \"iPhone\", \"SymbianOS\", \"Windows Phone\", \"iPad\", \"iPod\"];"}]}], "6": "cl"}, {"3": "aItD-1667182469119", "5": [{"2": "2", "3": "UeYm-1667182469119", "7": [{"8": "  let flag = true;"}]}], "6": "cl"}, {"3": "848u-1667182469119", "5": [{"2": "2", "3": "8qeX-1667182469119", "7": [{"8": "  for (let v = 0; v < Agents.length; v++) {"}]}], "6": "cl"}, {"3": "4hbh-1667182469119", "5": [{"2": "2", "3": "wb4U-1667182469119", "7": [{"8": "    if (userAgentInfo.indexOf(Agents[v]) > 0) {"}]}], "6": "cl"}, {"3": "cAbQ-1667182469119", "5": [{"2": "2", "3": "GNMC-1667182469119", "7": [{"8": "      flag = false;"}]}], "6": "cl"}, {"3": "sBKC-1667182469119", "5": [{"2": "2", "3": "6tTx-1667182469119", "7": [{"8": "      break;"}]}], "6": "cl"}, {"3": "gmu5-1667182469119", "5": [{"2": "2", "3": "rdHc-1667182469119", "7": [{"8": "    }"}]}], "6": "cl"}, {"3": "73q1-1667182469119", "5": [{"2": "2", "3": "pPMt-1667182469119", "7": [{"8": "  }"}]}], "6": "cl"}, {"3": "NWEK-1667182469119", "5": [{"2": "2", "3": "Hszv-1667182469119", "7": [{"8": "  return flag;"}]}], "6": "cl"}, {"3": "JM6F-1667182469119", "5": [{"2": "2", "3": "zZyD-1667182469119", "7": [{"8": "}"}]}], "6": "cl"}, {"3": "EvPp-1667182469119", "5": [{"2": "2", "3": "lIzA-1667182469119"}], "6": "cl"}, {"3": "XEmX-1667182469119", "5": [{"2": "2", "3": "21fR-1667182469119", "7": [{"8": "const flag = IsPC(); //true为PC端，false为手机端"}]}], "6": "cl"}, {"3": "Ugc6-1667182469119", "5": [{"2": "2", "3": "0PTe-1667182469119", "7": [{"8": "if (flag) {"}]}], "6": "cl"}, {"3": "DpN1-1667182469119", "5": [{"2": "2", "3": "Wu15-1667182469119", "7": [{"8": "//pc端引入的link"}]}], "6": "cl"}, {"3": "yxzT-1667182469119", "5": [{"2": "2", "3": "GtPY-1667182469119", "7": [{"8": "  document.write(' <link href=\"css/app.fa43afb7.css\" rel=\"preload\" as=\"style\"> <link href=\"js/app.ae6f97da.js\" rel=\"preload\" as=\"script\"><link href=\"css/app.fa43afb7.css\" rel=\"stylesheet\">')"}]}], "6": "cl"}, {"3": "3y1a-1667182469119", "5": [{"2": "2", "3": "4vf4-1667182469119", "7": [{"8": "} else {"}]}], "6": "cl"}, {"3": "YoUO-1667182469119", "5": [{"2": "2", "3": "sRJY-1667182469119", "7": [{"8": "  document.write('<link href=\"css/app.4646bd09.css\" rel=\"preload\" as=\"style\"> <link href=\"js/app.0b91c116.js\" rel=\"preload\" as=\"script\">  <link href=\"css/app.4646bd09.css\" rel=\"stylesheet\">')"}]}], "6": "cl"}, {"3": "uZRl-1667182469119", "5": [{"2": "2", "3": "ONVD-1667182469119", "7": [{"8": "}"}]}], "6": "cl"}], "6": "cd"}, {"3": "k08d-1651678885924", "4": {"version": 1}, "5": [{"2": "2", "3": "cxKj-1667182469119", "7": [{"8": "引入js ，在这里要注意一个点，当写的是双标签的时候在结束标签的前面要加个转译字符", "9": [{"0": "#4d4d4d", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}]}]}, {"3": "D9kM-1651679118621", "4": {"version": 1}, "5": [{"2": "2", "3": "XjPR-1667182469119", "7": [{"8": "'\\' ", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": "不然无效", "9": [{"0": "#4d4d4d", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}]}]}, {"3": "f1qM-1753336470776", "4": {"version": 1, "fromIdentity": "", "u": "https://note.youdao.com/yws/res/12289/WEBRESOURCE016baf60f974b95add2bd4514962be2f", "w": 773, "h": 206}, "6": "im"}, {"3": "Bz7y-1753335531247", "4": {"version": 1}, "5": [{"2": "2", "3": "qycZ-1753335531248", "7": [{"8": "", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "8Fmx-1753336467813", "5": [{"2": "2", "3": "nUOI-1753336467812", "7": [{"8": "", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "ATqA-1753336468082", "5": [{"2": "2", "3": "uvlH-1753336468081", "7": [{"8": "231、  计算对象的层次 （递归实现）", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "FP10-1651678908742", "4": {"version": 1}, "5": [{"2": "2", "3": "Qx8E-1667182469119"}]}, {"3": "6KCe-1651679170032", "4": {"version": 1}, "5": [{"2": "2", "3": "4XE7-1667182469120", "7": [{"8": "/ "}]}]}, {"3": "J7dM-1651679170289", "4": {"version": 1}, "5": [{"2": "2", "3": "YwF4-1667182469120"}]}, {"3": "Oak5-1651678908949", "4": {"version": 1}, "5": [{"2": "2", "3": "YJu6-1667182469120"}]}, {"3": "aCT2-1651678909148", "4": {"version": 1}, "5": [{"2": "2", "3": "v6kf-1667182469120"}]}, {"3": "zBvQ-1651678909353", "4": {"version": 1}, "5": [{"2": "2", "3": "6onR-1667182469120", "7": [{"8": "232、lass 、 pass 、 sass", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "g46c-1651763902981", "4": {"version": 1}, "5": [{"2": "2", "3": "rXuy-1667182469120"}]}, {"3": "EkMx-1651763933128", "4": {"version": 1}, "5": [{"2": "2", "3": "wBuG-1667182469120", "7": [{"8": "lass", "9": [{"0": "#000000", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}, {"2": "b"}]}, {"8": "  底层应用 cpu / 网络 / 内存 等计算资源", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "Vwzo-1651763907604", "4": {"version": 1}, "5": [{"2": "2", "3": "rpai-1667182469120"}]}, {"3": "NfOJ-1651763907815", "4": {"version": 1}, "5": [{"2": "2", "3": "2IWH-1667182469120", "7": [{"8": "pass", "9": [{"0": "#000000", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}, {"2": "b"}]}, {"8": "  中间件  开发语言 和 开发环境 ", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "oaxI-1651763910448", "4": {"version": 1}, "5": [{"2": "2", "3": "gcTP-1667182469120"}]}, {"3": "ViBC-1651763911023", "4": {"version": 1}, "5": [{"2": "2", "3": "gAHy-1667182469120", "7": [{"8": "sass", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}, {"2": "b"}]}, {"8": "  软件即服务 直接使用开发好的应用软件，只注重运营。", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "RSI6-1651763914263", "4": {"version": 1}, "5": [{"2": "2", "3": "CWCX-1667182469120"}]}, {"3": "DOY0-1651763914453", "4": {"version": 1}, "5": [{"2": "2", "3": "2rlM-1667182469120"}]}, {"3": "Esoe-1651763914639", "4": {"version": 1}, "5": [{"2": "2", "3": "wOK1-1667182469120"}]}, {"3": "GaTx-1651763915128", "4": {"version": 1}, "5": [{"2": "2", "3": "5wOL-1667182469120"}]}, {"3": "1mBR-1651678607690", "4": {"version": 1}, "5": [{"2": "2", "3": "95WO-1667182469120"}]}, {"3": "8Nse-1651678608177", "4": {"version": 1}, "5": [{"2": "2", "3": "QHeU-1667182469120", "7": [{"8": "233、 git flow 工作流  ", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "h7Uf-1651975858955", "4": {"version": 1}, "5": [{"2": "2", "3": "v9ZT-1667182469121", "7": [{"8": "为了更好的管理项目，针对", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}, {"8": "版本发布周期", "9": [{"0": "#eaeaea", "2": "c"}, {"0": "rgb(37, 42, 52)", "2": "bg"}, {"0": 18, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": "进行的一套代码管理，把控项目，降低项目上线的风险和提高可操控度。", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "KDbn-1652009466611", "4": {"version": 1}, "5": [{"2": "2", "3": "eUUk-1667182469121", "7": [{"8": "有一个命令行工具 git-flow，是对 git 命令的封装，简化各分支的繁琐操作。自动将release合到master并打tag和dev，并在本地和远程都删除release，再切换到dev分支。", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "z60r-1652009466864", "4": {"version": 1}, "5": [{"2": "2", "3": "e2sY-1667182469121"}]}, {"3": "qAj1-1652002824032", "4": {"version": 1}, "5": [{"2": "2", "3": "wXo7-1667182469121", "7": [{"8": "五个分支：", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "pBOl-1652002827997", "4": {"version": 1}, "5": [{"2": "2", "3": "Gb3S-1667182469121", "7": [{"8": "1、 master 主分支 运行 / 演示 系统（生产环境）的分支，是确保代码没有Bug时（测试通过的）才可以合并到此分支上", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "vMOA-1652002831601", "4": {"version": 1}, "5": [{"2": "2", "3": "7d19-1667182469121", "7": [{"8": "2、develop 开发分支 测试环境的分支，主要用于日常的需求开发提交代码的分支，代码未经测试员测试，可能存在bug，及不稳定   ", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}, {"8": "基于master创建develop", "9": [{"0": "#000000", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}]}]}, {"3": "9SF6-1652002832463", "4": {"version": 1}, "5": [{"2": "2", "3": "QJHr-1667182469121", "7": [{"8": "3、feature 新特性 包括小版本迭代（2.1.x），一个版本迭代可以开一个feature 分支，", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}, {"8": "基于 develop 分支 创建 feature", "9": [{"0": "#000000", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "，新特性开发完成会合并到 develop 分支，但是不会跟 master 打交道", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "5l8J-1652002833914", "4": {"version": 1}, "5": [{"2": "2", "3": "KG6Y-1667182469121", "7": [{"8": "4、release 发布分支 开发周期时间到了/ 完成了指定的版本任务开发 ，", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}, {"8": "从 develop 创建 release 分支", "9": [{"0": "#000000", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "， 用于代码发布工作。除了 相关的 bug fix 之外，不允许在该分支增加其他功能的代码。最终会合到master分支，顺便会打标签（tag），备注对应的版本号在master上", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "8irJ-1652002834758", "4": {"version": 1}, "5": [{"2": "2", "3": "k4wD-1667182469121", "7": [{"8": "5、hotfix  专门用于打补丁，修改bug。", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}, {"8": " 从master创建hotfix", "9": [{"0": "#000000", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "，待 bug 修改完成后，可以合到其他的分支上，主要是为了不影响其他分支的正常工作，同时又能协同进行", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}, {"8": " 生产环境中", "9": [{"2": "bg"}, {"0": "#F33232", "2": "c"}]}, {"8": "（master分支）bug 的修复工作", "9": [{"2": "bg"}, {"0": "#000000", "2": "c"}]}]}]}, {"3": "lBtM-1651975866867", "4": {"version": 1}, "5": [{"2": "2", "3": "vm9e-1667182469121"}]}, {"3": "IkEq-1651975859144", "4": {"version": 1}, "5": [{"2": "2", "3": "TVzO-1667182469121"}]}, {"3": "L23S-1651975859330", "4": {"version": 1}, "5": [{"2": "2", "3": "nB1T-1667182469121"}]}, {"3": "Jd3D-1651975859516", "4": {"version": 1}, "5": [{"2": "2", "3": "VTUh-1667182469121"}]}, {"3": "6FCu-1651975860513", "4": {"version": 1}, "5": [{"2": "2", "3": "XCZe-1667182469122", "7": [{"8": "234、 文字左右边对齐：  text-align: justify;  ", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "ktby-1652142876239", "4": {"version": 1}, "5": [{"2": "2", "3": "O8W7-1667182469122"}]}, {"3": "3h6U-1652142876595", "4": {"version": 1}, "5": [{"2": "2", "3": "6VjK-1667182469122", "7": [{"8": " ", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "mNh7-1652142877536", "4": {"version": 1}, "5": [{"2": "2", "3": "GAlF-1667182469122", "7": [{"8": "235、 ", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}, {"8": "CORB 警告", "9": [{"0": "#000000", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}]}]}, {"3": "BEKb-1652142890498", "4": {"version": 1}, "5": [{"2": "2", "3": "WWrN-1667182469122", "7": [{"8": "CORB:   ", "9": [{"0": "#000000", "2": "c"}, {"2": "bg"}]}, {"8": "Cross-Origin Read Blocking  "}, {"8": "跨域读阻塞", "9": [{"0": "#FFEE7C", "2": "bg"}]}]}]}, {"3": "tddH-1652198299810", "4": {"version": 1}, "5": [{"2": "2", "3": "LHEG-1667182469122", "7": [{"8": "为哈会有： 防止网络安全漏洞，出现的站点隔离（corb 实现策略之一）"}]}]}, {"3": "Qe0n-1652198300015", "4": {"version": 1}, "5": [{"2": "2", "3": "l7LX-1667182469122", "7": [{"8": "什么时候会有："}]}]}, {"3": "I2hB-1753334384475", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11665/WEBRESOURCE76c358eb285dae80a15603a6b99c8ef9", "w": 781, "h": 163}, "6": "im"}, {"3": "L0xd-1753334384476", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11662/WEBRESOURCEcc0f8b9eaecf86304e7f9acb07c9f704", "w": 941, "h": 150}, "6": "im"}, {"3": "ZOqY-1753334384477", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11656/WEBRESOURCEb0cc1098ac17079e3da032baac0c2db6", "w": 786, "h": 59}, "6": "im"}, {"3": "Zirk-1753334384478", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11664/WEBRESOURCE608f15949d79acb907fe7f5212b3b868", "w": 762, "h": 214}, "6": "im"}, {"3": "etIv-1753334384479", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11660/WEBRESOURCEc99ceff786926e8787159be0dbcc4825", "w": 1280, "h": 93}, "6": "im"}, {"3": "Lyvq-1652198682925", "4": {"version": 1}, "5": [{"2": "2", "3": "K2Qn-1667182469122"}]}, {"3": "7WCq-1652235654371", "4": {"version": 1}, "5": [{"2": "2", "3": "ntLh-1667182469122"}]}, {"3": "x0Ch-1652235673950", "4": {"version": 1}, "5": [{"2": "2", "3": "6FfL-1667182469122"}]}, {"3": "svWs-1652235674216", "4": {"version": 1}, "5": [{"2": "2", "3": "IKHn-1667182469122", "7": [{"8": "236、 rem （"}, {"8": "font size of the root element", "9": [{"0": "#F33232", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "SimHei", "2": "ff"}]}, {"8": "） 和 自适应百分比      追求的是"}, {"8": " ", "9": [{"2": "b"}]}, {"8": "比例 一致", "9": [{"0": "#2b2b2b", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 14, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}, {"2": "b"}]}]}]}, {"3": "1ISW-1652235698995", "4": {"version": 1}, "5": [{"2": "2", "3": "Itbp-1667182469122", "7": [{"8": "fontsize大小自适应  +  font-size大小固定"}]}]}, {"3": "lZ3n-1673747353780", "4": {"version": 1}, "5": [{"2": "2", "3": "O78N-1673747353733", "7": [{"8": "1rem = 根元素（html）的字体大小", "9": [{"2": "b"}, {"0": "#FFEE7C", "2": "bg"}, {"0": "#F33232", "2": "c"}]}, {"8": " （大部分情况下  "}, {"8": "1rem = 100px", "9": [{"2": "b"}, {"0": "#000000", "2": "c"}, {"0": 16, "2": "fs"}, {"0": "Times New Roman", "2": "ff"}]}, {"8": "）  （移动端下是 "}, {"8": "1rem = 16px", "9": [{"2": "b"}]}, {"8": "）"}]}]}, {"3": "bW5B-1673747595552", "4": {"version": 1}, "5": [{"2": "2", "3": "NBdL-1673747595508", "7": [{"8": "设置  "}, {"8": "1rem = 100px", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "，主要是："}]}]}, {"3": "naQM-1673747648329", "4": {"version": 1}, "5": [{"2": "2", "3": "HdyX-1673747648282", "7": [{"8": " (1)、方便计算（设置 100px，相当于 100%， 把 1 分成 100 分，把100当成一个系数而已， 好计算啊）"}]}]}, {"3": "ztKc-1673747682288", "4": {"version": 1}, "5": [{"2": "2", "3": "sJ4Y-1673747682242", "7": [{"8": " (2)、扩大基准值，适配部分限制最小字体的浏览器"}]}]}, {"3": "jpJZ-1673748045259", "4": {"version": 1}, "5": [{"2": "2", "3": "jpFp-1673748045212", "7": [{"8": "       （chrome最小字体12px，华为个别型号浏览器最小字体8px,如果设置为1rem=10px,在12px最小浏览器中会被强制转换为12px，会导致基准不对比例计算出现问题）"}]}]}, {"3": "rAxy-1652235790471", "4": {"version": 1}, "5": [{"2": "2", "3": "591M-1667182469122", "7": [{"8": " (3)、常见分辨率下（375px / 700px / 320px ....）的 1rem 都是设置为 100px"}]}]}, {"3": "BBO5-1673747170941", "4": {"version": 1}, "5": [{"2": "2", "3": "SOQN-1673747170892"}]}, {"3": "9O3p-1673750169335", "4": {"version": 1}, "5": [{"2": "2", "3": "KERX-1673750169287", "7": [{"8": "但是更多时候， html 的 font-size 是动态计算的呀！！！！ （为了不同设备上显示的比例一致）"}]}]}, {"3": "7QPa-1673750169710", "4": {"version": 1}, "5": [{"2": "2", "3": "gx88-1673750169662", "7": [{"8": " ", "9": [{"0": "#6CDFFF", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 14, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": "<HTML>", "9": [{"0": "#6CDFFF", "2": "c"}]}, {"8": "元素 的", "9": [{"0": "#2b2b2b", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 14, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": " ", "9": [{"0": "#6CDFFF", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 14, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": "font-size", "9": [{"0": "#6CDFFF", "2": "c"}]}, {"8": " "}, {"8": "计算公式为：", "9": [{"0": "#2b2b2b", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 14, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": " ", "9": [{"0": "#2b2b2b", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}, {"2": "b"}]}, {"8": "( 用户设备宽度 / 设计稿标准宽度 ) * ", "9": [{"0": "#333333", "2": "c"}, {"0": "rgb(248, 248, 248)", "2": "bg"}, {"0": 16, "2": "fs"}, {"2": "b"}]}, {"8": "100", "9": [{"0": "#008080", "2": "c"}, {"0": 16, "2": "fs"}, {"2": "b"}]}]}]}, {"3": "BjOY-1673747171487", "4": {"version": 1}, "5": [{"2": "2", "3": "4hxr-1673747171440", "7": [{"8": "", "9": [{"0": "#2b2b2b", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 14, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}]}]}, {"3": "t6t2-1673750877357", "4": {"version": 1}, "5": [{"2": "2", "3": "XwnR-1673750877310", "7": [{"8": "", "9": [{"0": "#2b2b2b", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 14, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}]}]}, {"3": "gPaa-1673750877758", "4": {"version": 1}, "5": [{"2": "2", "3": "IzgD-1673750877689", "7": [{"8": "设备像素比  ", "9": [{"0": "#2b2b2b", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 14, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": "device pixel ", "9": [{"0": "#2b2b2b", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 14, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}, {"2": "u"}]}, {"8": "ratio", "9": [{"2": "u"}]}, {"8": " （DPR） =", "9": [{"0": "#2b2b2b", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 14, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": " ", "9": [{"0": "#2b2b2b", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 22, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": "物理像素  (", "9": [{"0": "#333333", "2": "c"}, {"0": "rgb(248, 248, 248)", "2": "bg"}, {"0": 22, "2": "fs"}]}, {"8": "设备像素", "9": [{"0": "#333333", "2": "c"}, {"0": "rgb(248, 248, 248)", "2": "bg"}]}, {"8": ") / 逻辑像素  (css", "9": [{"0": "#333333", "2": "c"}, {"0": "rgb(248, 248, 248)", "2": "bg"}, {"0": 22, "2": "fs"}]}, {"8": "像素", "9": [{"0": "#333333", "2": "c"}, {"0": "rgb(248, 248, 248)", "2": "bg"}]}, {"8": ")  ", "9": [{"0": "#333333", "2": "c"}, {"0": "rgb(248, 248, 248)", "2": "bg"}, {"0": 22, "2": "fs"}]}]}]}, {"3": "EY97-1673747172740", "4": {"version": 1}, "5": [{"2": "2", "3": "Qhsp-1673747172695"}]}, {"3": "bwnY-1652235790741", "4": {"version": 1}, "5": [{"2": "2", "3": "KEkS-1667182469122"}]}, {"3": "7alz-1652235791007", "4": {"version": 1}, "5": [{"2": "2", "3": "kei6-1667182469122", "7": [{"8": "237、 简历  --  包装   --  获得面试机会"}]}]}, {"3": "107p-1652368328845", "4": {"version": 1}, "5": [{"2": "2", "3": "729l-1667182469122"}]}, {"3": "aAfG-1753334384480", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11669/WEBRESOURCE0c53da0520439beebc803cbf65c810f2", "w": 694, "h": 108}, "6": "im"}, {"3": "hi7D-1652235699592", "4": {"version": 1}, "5": [{"2": "2", "3": "J3c0-1667182469122"}]}, {"3": "vSbx-1652235654797", "4": {"version": 1}, "5": [{"2": "2", "3": "gOZQ-1667182469122"}]}, {"3": "mXl9-1652368365814", "4": {"version": 1}, "5": [{"2": "2", "3": "VcSC-1667182469122", "7": [{"8": "238、 window.open / location.href  会自动拼接上域名url，只需要拼上路径 '/path' 就行"}]}]}, {"3": "0j8g-1652368365952", "4": {"version": 1}, "5": [{"2": "2", "3": "vX6b-1667182469122", "7": [{"8": "document.domain", "9": [{"2": "b"}]}, {"8": "   获取 域名（不带端口）"}]}]}, {"3": "keVX-1663057168061", "4": {"version": 1}, "5": [{"2": "2", "3": "Vtfi-1667182469123", "7": [{"8": "location.host    ", "9": [{"2": "b"}]}, {"8": "获取主机名（域名）（带端口号，如果有端口号）    /    "}, {"8": "location.origin  ", "9": [{"2": "b"}]}, {"8": "获取完整的URL（带协议 比如： http://）"}]}]}, {"3": "sdEV-1753334384481", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11653/WEBRESOURCEfd483206909b6c82ff938514f060e4dc", "w": 335, "h": 135}, "6": "im"}, {"3": "gmTA-1753334384482", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11659/WEBRESOURCE570bfad6ab815afba3958a8b2a5de74b", "w": 231, "h": 85}, "6": "im"}, {"3": "sash-1663057103487", "4": {"version": 1}, "5": [{"2": "2", "3": "ethX-1667182469123", "7": [{"8": "（1）、因为会自动拼接，所以要加上 '"}, {"8": " // ", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "'，不然会拼在自己的域名下。"}]}]}, {"3": "FpYd-1753334384483", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11657/WEBRESOURCEc0b6655d10ecd242d55f5a70075e35fa", "w": 868, "h": 76}, "6": "im"}, {"3": "t4I7-1663057472277", "4": {"version": 1}, "5": [{"2": "2", "3": "ZYXi-1667182469123", "7": [{"8": "（2）、还有一种方法就是使用 路由的 resolve 方法，进行跳转。就不用判断哪个环境了。"}]}]}, {"3": "zgvv-1669080340426", "4": {"version": 1}, "5": [{"2": "2", "3": "f5yw-1669080340400", "7": [{"8": "const openUrl = "}, {"8": "this", "9": [{"0": "#F33232", "2": "c"}, {"2": "i"}, {"0": "#FFEE7C", "2": "bg"}, {"0": 18, "2": "fs"}]}, {"8": ".$router.resolve(name: 'xxx', query / params).href", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}, {"0": 18, "2": "fs"}]}]}]}, {"3": "9nzI-1669080436631", "4": {"version": 1}, "5": [{"2": "2", "3": "1NcQ-1669080436597", "7": [{"8": "window.open(", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}, {"0": 18, "2": "fs"}]}, {"8": "openUrl, '_blank'"}, {"8": ")", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}, {"0": 18, "2": "fs"}]}]}]}, {"3": "WLKE-1753334384484", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11651/WEBRESOURCE61487040cda72e7c7822b1d84254b9b9", "w": 1342, "h": 39}, "6": "im"}, {"3": "QIOL-1669080288458", "4": {"version": 1}, "5": [{"2": "2", "3": "4O03-1669080288415"}]}, {"3": "qOTI-1653376718952", "4": {"version": 1}, "5": [{"2": "2", "3": "y1zF-1667182469123", "7": [{"8": "其实直接 / （根路径）也是可以的，主要看你拼的参数能不能跳过去！", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}]}]}, {"3": "iiTO-1753334384485", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11654/WEBRESOURCE00788fbf3949c6f67e67787db79add32", "w": 1034, "h": 285}, "6": "im"}, {"3": "24cN-1663118826027", "4": {"version": 1}, "5": [{"2": "2", "3": "3NsZ-1667182469123"}]}, {"3": "DA9U-1663118826396", "4": {"version": 1}, "5": [{"2": "2", "3": "Sk47-1667182469123"}]}, {"3": "T4xp-1753334384024", "4": {"version": 1, "l": "h1"}, "5": [{"2": "2", "3": "TWun-1667182469123", "7": [{"8": "239、", "9": [{"2": "u"}, {"0": "#FFEE7C", "2": "bg"}, {"0": "#F33232", "2": "c"}, {"2": "b"}, {"0": 20, "2": "fs"}]}, {"8": " 关于路由 一些比较悬的东西", "9": [{"2": "b"}, {"0": 20, "2": "fs"}]}]}], "6": "h"}, {"3": "v84g-1653753237850", "4": {"version": 1}, "5": [{"2": "2", "3": "U974-1667182469123"}]}, {"3": "fgSd-1753334384486", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11658/WEBRESOURCE1ad3ccef56ab61cd021fab4cd8784d41", "w": 889, "h": 365}, "6": "im"}, {"3": "fDMU-1753334384487", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11648/WEBRESOURCEa93da06e912ef03246035bfb2a2dcd3c", "w": 884, "h": 153}, "6": "im"}, {"3": "qc1D-1653753379516", "4": {"version": 1}, "5": [{"2": "2", "3": "DOGZ-1667182469123"}]}, {"3": "9uWA-1653753379715", "4": {"version": 1}, "5": [{"2": "2", "3": "67if-1667182469123"}]}, {"3": "kIoS-1653753379857", "4": {"version": 1}, "5": [{"2": "2", "3": "Sin1-1667182469123"}]}, {"3": "09fi-1653753380015", "4": {"version": 1}, "5": [{"2": "2", "3": "j23B-1667182469123"}]}, {"3": "pCM4-1653753380159", "4": {"version": 1}, "5": [{"2": "2", "3": "FLYh-1667182469123", "7": [{"8": "240、 "}, {"8": "技术的出现都是为了解决问题的、而不是单纯地了解一堆API的", "9": [{"0": "#F33232", "2": "c"}]}]}]}, {"3": "2hBH-1654415843211", "4": {"version": 1}, "5": [{"2": "2", "3": "jxKa-1667182469123", "7": [{"8": "掌握一门技术，要知道其局限性/历史 和 本质"}]}]}, {"3": "kide-1654415791899", "4": {"version": 1}, "5": [{"2": "2", "3": "T5H6-1667182469123"}]}, {"3": "7edq-1654416732480", "4": {"version": 1}, "5": [{"2": "2", "3": "y0zo-1667182469123", "7": [{"8": "学习也是单纯地为了解决问题的，不是为了去记忆一堆API的。"}]}]}, {"3": "JfjK-1654418405801", "4": {"version": 1}, "5": [{"2": "2", "3": "x8cj-1667182469123"}]}, {"3": "irXU-1654418406699", "4": {"version": 1}, "5": [{"2": "2", "3": "71my-1667182469124", "7": [{"8": "软件："}, {"8": " 计算机数据 +  指令", "9": [{"0": "#F33232", "2": "c"}]}]}]}, {"3": "Dkss-1654418422921", "4": {"version": 1}, "5": [{"2": "2", "3": "7VBL-1667182469124"}]}, {"3": "dvE2-1654418423095", "4": {"version": 1}, "5": [{"2": "2", "3": "D2hk-1667182469124", "7": [{"8": "大前端： "}, {"8": "移动端", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": " （unpapp） 和"}, {"8": " web端", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": " （网站 / 后台管理系统 / 手机H5）   小程序端  桌面端  服务器开发"}]}]}, {"3": "wFbq-1654415792036", "4": {"version": 1}, "5": [{"2": "2", "3": "BZmN-1667182469124"}]}, {"3": "Nx1Z-1654415792181", "4": {"version": 1}, "5": [{"2": "2", "3": "rRWA-1667182469124", "7": [{"8": "241、  "}, {"8": "vuex-persistedstate  Vuex的持久化插件（将本来存在state的数据映射到本地存储中，既做到了刷新不丢失又能保证数据还是响应式的）", "9": [{"0": "#4d4d4d", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "SimHei", "2": "ff"}]}]}]}, {"3": "Wb21-1654656556318", "4": {"version": 1}, "5": [{"2": "2", "3": "kqe2-1667182469124"}]}, {"3": "pWWk-1654656556644", "4": {"version": 1}, "5": [{"2": "2", "3": "0jgD-1667182469124"}]}, {"3": "5VF7-1654656556972", "4": {"version": 1}, "5": [{"2": "2", "3": "kQFK-1667182469124", "7": [{"8": "242、js 完全搞不懂系列： = ="}]}]}, {"3": "njQw-1655298523914", "4": {"version": 1}, "5": [{"2": "2", "3": "BM3p-1667182469124", "7": [{"8": "{} + ''", "9": [{"0": "#0091FF", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "   结果是转数字 ----->  0", "9": [{"0": "#4d4d4d", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "SimHei", "2": "ff"}]}]}]}, {"3": "OAQo-1753334384488", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11649/WEBRESOURCE0ef44cb2ea1609ed26b7273a2d344010", "w": 219, "h": 57}, "6": "im"}, {"3": "xvb3-1655298526987", "4": {"version": 1}, "5": [{"2": "2", "3": "XpfW-1667182469124"}]}, {"3": "b5D2-1753334384489", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11655/WEBRESOURCE91f43ba29df725878320086fde7c931a", "w": 820, "h": 312}, "6": "im"}, {"3": "gyaA-1753334384490", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11647/WEBRESOURCE932f1978b4a98bbc565f3a1701bbd8a0", "w": 389, "h": 237}, "6": "im"}, {"3": "t9Vm-1652142891225", "4": {"version": 1}, "5": [{"2": "2", "3": "DxRT-1667182469124"}]}, {"3": "3yxw-1655299204218", "4": {"version": 1}, "5": [{"2": "2", "3": "knfl-1667182469124"}]}, {"3": "xRhR-1655299204475", "4": {"version": 1}, "5": [{"2": "2", "3": "R990-1667182469124"}]}, {"3": "fwLT-1655299204691", "4": {"version": 1}, "5": [{"2": "2", "3": "pCZT-1667182469124", "7": [{"8": "243、"}, {"8": " 循环 push ，如果把 字典 dict 写在 循环外面, 辉出现相同key 覆盖的问题, 解决方案是写在循环里面，确保每次都重新初始化，保证数据不给覆盖", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}]}]}, {"3": "SAuI-1753334384491", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11644/WEBRESOURCE2a34faf1fce14e6f9723069f0d4c6a8f", "w": 827, "h": 594}, "6": "im"}, {"3": "R7UV-1655299208165", "4": {"version": 1}, "5": [{"2": "2", "3": "6CNS-1667182469124", "7": [{"8": "分析原因：", "9": [{"2": "b"}]}]}]}, {"3": "8bDg-1655304481189", "4": {"version": 1}, "5": [{"2": "2", "3": "iETd-1667182469124", "7": [{"8": "可以发现每次 for 循环添加到字典中，都会覆盖掉上次添加的数据，并且内存地址都是相同的，所以就会影响到列表中已经存入的字典。", "9": [{"0": 18, "2": "fs"}]}]}]}, {"3": "aOur-1655304491993", "4": {"version": 1}, "5": [{"2": "2", "3": "nJYU-1667182469125", "7": [{"8": "因为字典的增加方式dict['aaa] = bbb,这种形式如果字典里有对应的key就会覆盖掉，没有key就会添加到字典里。", "9": [{"0": 18, "2": "fs"}, {"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}]}]}, {"3": "JaMi-1753334384492", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11652/WEBRESOURCEd308464e2bd917a3ae88dff4294e2def", "w": 820, "h": 611}, "6": "im"}, {"3": "8yzA-1655304326513", "4": {"version": 1}, "5": [{"2": "2", "3": "w0da-1667182469125"}]}, {"3": "0ehf-1655914729419", "4": {"version": 1}, "5": [{"2": "2", "3": "Cs20-1667182469125"}]}, {"3": "QeNV-1655304318155", "4": {"version": 1}, "5": [{"2": "2", "3": "t5DJ-1667182469125"}]}, {"3": "VviR-1655304318390", "4": {"version": 1}, "5": [{"2": "2", "3": "pYNB-1667182469125", "7": [{"8": "245、 关于表单校验的动态绑定校验值 动态绑定 prop 值"}]}]}, {"3": "tFbD-1655914731335", "4": {"version": 1}, "5": [{"2": "2", "3": "azpz-1667182469125", "7": [{"8": "  关键点：", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}, {"0": 18, "2": "fs"}]}, {"8": "    template循环该数组（绑定在form中的） + prop取值（   "}, {"8": "'prices.' + index + '.title' ", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "  组件识别这种写法 ）+ v-model 帮值 "}, {"8": " 三者缺一不可", "9": [{"0": "#FFEE7C", "2": "bg"}, {"0": "#F33232", "2": "c"}]}]}]}, {"3": "TfWR-1655914732136", "4": {"version": 1}, "5": [{"2": "2", "3": "ekwo-1667182469125"}]}, {"3": "CPhF-1753334384493", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11650/WEBRESOURCEcde4394d9e3229ce623c31326045b621", "w": 856, "h": 494}, "6": "im"}, {"3": "u4a4-1655914703313", "4": {"version": 1}, "5": [{"2": "2", "3": "A6Mr-1667182469125", "7": [{"8": "主要就是  循环 "}, {"8": "绑定 model 中的 form 的数组 ", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "+ "}, {"8": "自定义prop", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": " + "}, {"8": "自定义rules ", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": "+ v-if 判断  其他的rules那个字段对应的校验函数 一样的写法的"}]}]}, {"3": "ujEJ-1753334384494", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11645/WEBRESOURCE4d6662c913236151297513f38a665fac", "w": 851, "h": 323}, "6": "im"}, {"3": "1nFi-1655914657114", "4": {"version": 1}, "5": [{"2": "2", "3": "hiuK-1667182469125"}]}, {"3": "xErJ-1655914657331", "4": {"version": 1}, "5": [{"2": "2", "3": "GdjU-1667182469125", "7": [{"8": "246、 vue路由跳转的方式"}]}]}, {"3": "BIRG-1656257831750", "4": {"version": 1}, "5": [{"2": "2", "3": "1sUp-1667182469125", "7": [{"8": "（1）、 router-link 标签 （"}, {"8": "a标签跳转会重新渲染即刷新页面， router-link", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "跟 this.$router.push 那几个方法就"}, {"8": "不会", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "，会"}, {"8": "守卫点击事件，让浏览器不再重新加载页面", "9": [{"0": "#393939", "2": "c"}, {"0": "rgb(250, 247, 239)", "2": "bg"}, {"0": 14, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": " 只会更新不一样的地方。 所以使用了keep-alive的话就不要用a标签了，会重新刷新页面，使缓存失效的） \t\t\t           "}, {"8": "router-link 是 只会", "9": [{"0": "#F33232", "2": "c"}, {"2": "u"}]}, {"8": "更新变化的部分从而减少DOM性能消耗", "9": [{"0": "#F33232", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 13, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}, {"2": "u"}]}]}]}, {"3": "LvWt-1656258279305", "4": {"version": 1}, "5": [{"2": "2", "3": "rvFG-1667182469126", "7": [{"8": "                           \t\t  a标签的"}, {"8": "rel", "9": [{"0": "#d19a66", "2": "c"}, {"2": "i"}]}, {"8": "=", "9": [{"0": "#abb2bf", "2": "c"}]}, {"8": "\"noopener\"属性 ", "9": [{"0": "#98c379", "2": "c"}]}, {"8": "即 no opener 的意思，就是在同时设置了target=\"_blank\"时打开的新页面中的"}, {"8": "window.opener值为null，安全起见，获取不到原有的window对象", "9": [{"0": "#4d4d4d", "2": "c"}, {"0": "rgb(255, 255, 255)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "SimHei", "2": "ff"}]}]}]}, {"3": "M6hD-1753334384495", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11646/WEBRESOURCE3db436461f1ebac27cd76eb1ca756a8e", "w": 858, "h": 252}, "6": "im"}, {"3": "9wZz-1656257921764", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "eCLP-1667182469126"}]}, {"3": "bmyZ-1656257971806", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "pTie-1667182469126", "7": [{"8": "（2）、this.$router.push / replace / go /back /forward / 方法"}]}]}, {"3": "jWc3-1656258036412", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "q30b-1667182469126"}]}, {"3": "zGMx-1656381393691", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "Yhtg-1667182469126", "7": [{"8": "  router 的两种传参方式 ： "}]}]}, {"3": "4AnM-1656381406650", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "MS1x-1667182469126", "7": [{"8": "query: { id }   问号拼接", "9": [{"0": "#F33232", "2": "c"}]}]}]}, {"3": "fvJA-1753334384496", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11643/WEBRESOURCE4b7c60677b032655b71a47252ed8ce63", "w": 619, "h": 46}, "6": "im"}, {"3": "lDUv-1656258036993", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "ygYl-1667182469126", "7": [{"8": "params: { id }   斜杠拼接", "9": [{"0": "#F33232", "2": "c"}]}]}]}, {"3": "LnyC-1753334384498", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11642/WEBRESOURCEd13b6b57b41ddb7a5746e1662efb6916", "w": 641, "h": 48}, "6": "im"}, {"3": "OIxU-1656381477907", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "B7kl-1667182469126"}]}, {"3": "iRrR-1656381697631", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "IZkL-1667182469126", "7": [{"8": "this.$router.push('/biz/production/edit' + id)", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": "  params  拼在  url 上 （刷新页面有）"}]}]}, {"3": "OWhP-1656381806038", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "3fhE-1667182469126", "7": [{"8": " 等价于  "}]}]}, {"3": "CeHu-1656381761699", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "xh6B-1667182469126", "7": [{"8": "this.$router.push({ ", "9": [{"0": "#FFEE7C", "2": "bg"}]}, {"8": "                params 在 body 上 （刷新页面无）  "}]}]}, {"3": "9wwi-1656381752858", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "5oaQ-1667182469126", "7": [{"8": "  name: 'productionEdit',", "9": [{"0": "#FFEE7C", "2": "bg"}]}]}]}, {"3": "OZxE-1656381780816", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "cxrT-1667182469126", "7": [{"8": "  params: { id }", "9": [{"0": "#FFEE7C", "2": "bg"}]}]}]}, {"3": "BKNa-1656381766659", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "qRHS-1667182469126", "7": [{"8": "})", "9": [{"0": "#FFEE7C", "2": "bg"}]}]}]}, {"3": "1N1v-1656381694215", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "0VE9-1667182469126"}]}, {"3": "2vU9-1656381694954", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "4Eta-1667182469126"}]}, {"3": "z11h-1656258038018", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "eECM-1667182469126", "7": [{"8": "247、 keep-alive 的 使用： "}]}]}, {"3": "uKiF-1656259632291", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "GIls-1667182469126"}]}, {"3": "WwDL-1656259632575", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "eu3i-1667182469126", "7": [{"8": "跳转的时候如果都是用的vue-router提供的方法（router-view或者$router.push等），那么这个keep-alive会自动帮你缓存，就你请求过的再次请求就不会再请求接口了，除非你手动再刷新页面。"}]}]}, {"3": "hbci-1656259804526", "4": {"version": 1}, "5": [{"2": "2", "3": "ym9O-1667182469126", "7": [{"8": "浏览器的前进后退功能也是一样的不会刷新页面的，"}]}]}, {"3": "rWbv-1656259816366", "4": {"version": 1}, "5": [{"2": "2", "3": "RLMX-1667182469127", "7": [{"8": "那如果你是点击切换请求接口的话，那keep-alive就做不了了。每次都重新请求接口，就不会帮你缓存了 。"}]}]}, {"3": "3uRg-1656258048930", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "gWtS-1667182469127"}]}, {"3": "WFjD-1656258049917", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "vkhH-1667182469127"}]}, {"3": "UUMj-1656258050117", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "3Wk8-1667182469127"}]}, {"3": "lbvh-1656258050313", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "eSj4-1667182469127", "7": [{"8": "248、"}, {"8": "算法 ", "9": [{"0": "#F33232", "2": "c"}, {"0": "#D4FE7F", "2": "bg"}, {"0": 22, "2": "fs"}]}, {"8": "  计算/解决问题的步骤。 "}]}]}, {"3": "anAY-1656601008717", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "Hrgl-1667182469127", "7": [{"8": "线性结构（数据对象一对一）： 数组、链表、栈、队列 "}]}]}, {"3": "JtOr-1656601029544", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "ntVa-1667182469127", "7": [{"8": "非线性结构：  二维/多维数组、 广义表、树结构、图结构"}]}]}, {"3": "BHyg-1656600704131", "4": {"version": 1}, "5": [{"2": "2", "3": "iJfY-1667182469127"}]}, {"3": "Uhw6-1656600705883", "4": {"version": 1}, "5": [{"2": "2", "3": "Agja-1667182469127"}]}, {"3": "nI9d-1656600706047", "4": {"version": 1}, "5": [{"2": "2", "3": "JRhI-1667182469127", "7": [{"8": "249、"}, {"8": "控制台获取element元素的dom对象", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}, {"0": 18, "2": "fs"}]}]}]}, {"3": "WCpY-1753334384499", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11641/WEBRESOURCE26f4d2a7d9976e9f2b96e47da31c2d97", "w": 524, "h": 589}, "6": "im"}, {"3": "JPTb-1656600849025", "4": {"version": 1}, "5": [{"2": "2", "3": "d7G2-1667182469127", "7": [{"8": "dom元素和组件实例的区别：", "9": [{"0": 18, "2": "fs"}, {"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}]}]}, {"3": "bPNA-1753334384500", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11638/WEBRESOURCE367139356d55f251f50f83abe584d576", "w": 920, "h": 77}, "6": "im"}, {"3": "MV7i-1656600884485", "4": {"version": 1}, "5": [{"2": "2", "3": "tqmB-1667182469127", "7": [{"8": "dom元素可以当做ref得到的实例对象中的$el属性", "9": [{"0": 18, "2": "fs"}, {"0": "#F33232", "2": "c"}, {"2": "bg"}]}]}]}, {"3": "3qc5-1753334384501", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11639/WEBRESOURCE25cd95571d46cd9f16a105a1539193c4", "w": 739, "h": 420}, "6": "im"}, {"3": "IPsL-1656602309169", "4": {"version": 1}, "5": [{"2": "2", "3": "kJmo-1667182469127"}]}, {"3": "GY0Q-1656602309393", "4": {"version": 1}, "5": [{"2": "2", "3": "pNMX-1667182469127", "7": [{"8": "控制台获取element元素的dom实例", "9": [{"0": 18, "2": "fs"}, {"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}]}]}, {"3": "0ke2-1753334384502", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11637/WEBRESOURCE8b4cfa9595b3907f59e80c58e4ed69bc", "w": 694, "h": 427}, "6": "im"}, {"3": "M1Zn-1656600710670", "4": {"version": 1}, "5": [{"2": "2", "3": "cpl6-1667182469127", "7": [{"8": "vue组件可以分3种"}]}]}, {"3": "BBHI-1656602122720", "4": {"version": 1}, "5": [{"2": "2", "3": "jTgv-1667182469128", "7": [{"8": "根组件： 最顶层 id=\"#app\""}]}]}, {"3": "F9BF-1656602126495", "4": {"version": 1}, "5": [{"2": "2", "3": "UVVF-1667182469128", "7": [{"8": "子组件： 根组件下面的组件（可以多层嵌套）"}]}]}, {"3": "2fAT-1656602128759", "4": {"version": 1}, "5": [{"2": "2", "3": "5HnY-1667182469128", "7": [{"8": "琉璃组件： 挂载的 全局 $xxx 属性"}]}]}, {"3": "iLyM-1656602247095", "4": {"version": 1}, "5": [{"2": "2", "3": "A2au-1667182469128"}]}, {"3": "3tbp-1656602267067", "4": {"version": 1}, "5": [{"2": "2", "3": "EF7a-1667182469128", "7": [{"8": "一个组件就是一个实例", "9": [{"0": "#F33232", "2": "c"}, {"0": "#D4FE7F", "2": "bg"}, {"0": 16, "2": "fs"}]}]}]}, {"3": "KwVn-1656602267305", "4": {"version": 1}, "5": [{"2": "2", "3": "EJMp-1667182469128"}]}, {"3": "9sJj-1753334384503", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11640/WEBRESOURCE0b766c123f1e763294ffe35bcdfcfe0c", "w": 759, "h": 177}, "6": "im"}, {"3": "1UND-1656600710845", "4": {"version": 1}, "5": [{"2": "2", "3": "CnHg-1667182469128"}]}, {"3": "VguU-1656600711815", "4": {"version": 1}, "5": [{"2": "2", "3": "V7m0-1667182469128", "7": [{"8": "250、  项目难点：", "9": [{"0": "#FFEE7C", "2": "bg"}, {"0": "#F33232", "2": "c"}, {"0": 22, "2": "fs"}]}, {"8": " "}]}]}, {"3": "hRYJ-1656636616528", "4": {"version": 1}, "5": [{"2": "2", "3": "H9mu-1667182469128", "7": [{"8": "（1）、公用组件的抽取（抽象功能，进行组件的封装    插槽 / prors / emit  ），避免很多重复冗余的代码，同时也提高了开发效率。"}]}]}, {"3": "kmPi-1656636695244", "4": {"version": 1}, "5": [{"2": "2", "3": "keQq-1667182469128", "7": [{"8": "（2）、框架的自定义配置 （递归显示菜单 menus 模块）"}]}]}, {"3": "Uawl-1656637497131", "4": {"version": 1}, "5": [{"2": "2", "3": "5Rtz-1667182469128", "7": [{"8": "（3）、系统管理模块"}]}]}, {"3": "YV0F-1656637609538", "4": {"version": 1}, "5": [{"2": "2", "3": "PhXk-1667182469128", "7": [{"8": "（", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "会员中心"}, {"8": "）", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "用户（账号）管理   ---    "}, {"8": "（", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "管理中心/管理后台"}, {"8": "）", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "人员（管理员） 管理"}]}]}, {"3": "7h9m-1656637623399", "4": {"version": 1}, "5": [{"2": "2", "3": "U1Ff-1667182469128", "7": [{"8": "角色（权限）管理"}]}]}, {"3": "4H2N-1656637672458", "4": {"version": 1}, "5": [{"2": "2", "3": "Lv6V-1667182469128", "7": [{"8": "资源（菜单）管理"}]}]}, {"3": "Hq9u-1656638679692", "4": {"version": 1}, "5": [{"2": "2", "3": "iNeC-1667182469128"}]}, {"3": "pqlM-1672039505139", "4": {"version": 1}, "5": [{"2": "2", "3": "ZxJX-1672039505089", "7": [{"8": "（4）"}, {"8": "超时退出重新登录回到原先操作的页面  ", "9": [{"0": "#F33232", "2": "c"}, {"2": "u"}]}]}]}, {"3": "He36-1672040620389", "4": {"version": 1}, "5": [{"2": "2", "3": "pjEY-1672040620339", "7": [{"8": "（   点击确认退出按钮的时候 disptch 到 logout 接口，然后成功后的.then中  this.$router.push("}, {"8": "'/login?redirect_uri='", "9": [{"0": "#F33232", "2": "c"}]}, {"8": " +", "9": [{"0": "#000000", "2": "c"}]}, {"8": "  this.$route.fullPath) 保存当前操作页面路由，"}]}]}, {"3": "2KOj-1672041030387", "4": {"version": 1}, "5": [{"2": "2", "3": "IAtK-1672041030323", "7": [{"8": "       然后在登录页 this.$route.query 获取到 redirect_uri 直接 this.$router.push 过去就可以了，然后没有  redirect_uri 就默认跳到主页 ）"}]}]}, {"3": "DFX7-1672039503421", "4": {"version": 1}, "5": [{"2": "2", "3": "Rk1D-1672039503362"}]}, {"3": "9Itk-1656637991774", "4": {"version": 1}, "5": [{"2": "2", "3": "Apql-1667182469128", "7": [{"8": "1、资源显示当前账号下拥有的所有资源（菜单）列表，超级管理员默认拥有显示系统全部菜单的权限（可以操作菜单，排序，显示隐藏，更换菜单图标/菜单排序，是否缓存，组件路径等...）"}]}]}, {"3": "fT14-1753334384504", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11635/WEBRESOURCE6eb1f15b90cdbd28199bd8495d974de5", "w": 780, "h": 389}, "6": "im"}, {"3": "znNx-1753334384505", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11636/WEBRESOURCE3b49f861d88f4a91f9617fc521e7d9c2", "w": 387, "h": 501}, "6": "im"}, {"3": "fvq3-1656638366569", "4": {"version": 1}, "5": [{"2": "2", "3": "C8m6-1667182469128", "7": [{"8": "2、角色管理可以为系统创建角色（给用户分配角色），分配角色菜单"}]}]}, {"3": "BlRa-1753334384506", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11629/WEBRESOURCEe4c7a34c05af7566e66ae2ff1752fab7", "w": 701, "h": 318}, "6": "im"}, {"3": "q07j-1656637989074", "4": {"version": 1}, "5": [{"2": "2", "3": "7675-1667182469128", "7": [{"8": "3、账号管理是登录系统用的账号名，可以新增 / 修改密码，分配角色，是否启用该账号，删除账号 等"}]}]}, {"3": "IYkj-1753334384507", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11634/WEBRESOURCEb6b8639fe7e902b1efc95c963309716b", "w": 771, "h": 176}, "6": "im"}, {"3": "jbKP-1656638550572", "4": {"version": 1}, "5": [{"2": "2", "3": "83Y6-1667182469128"}]}, {"3": "8ZBV-1656636617303", "4": {"version": 1}, "5": [{"2": "2", "3": "EhFd-1667182469128"}]}, {"3": "JkS5-1656636617625", "4": {"version": 1}, "5": [{"2": "2", "3": "DL72-1667182469129", "7": [{"8": "251、 input 的 type 为 number 时，设置的 maxlength 的写法："}]}]}, {"3": "TSCq-1656914989905", "4": {"version": 1}, "5": [{"2": "2", "3": "GbgY-1667182469129", "7": [{"8": "<input type=\"number\"", "9": [{"0": "#333333", "2": "c"}, {"0": "rgb(248, 248, 248)", "2": "bg"}, {"0": 18, "2": "fs"}]}, {"8": " oninput=\"if(value.length>5)value=value.slice(0,5)\"", "9": [{"0": 18, "2": "fs"}, {"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "  />", "9": [{"0": 18, "2": "fs"}, {"0": "#333333", "2": "c"}, {"0": "rgb(248, 248, 248)", "2": "bg"}]}]}]}, {"3": "SMoH-1656914955843", "4": {"version": 1}, "5": [{"2": "2", "3": "UGHy-1667182469129"}]}, {"3": "xgak-1656914956285", "4": {"version": 1}, "5": [{"2": "2", "3": "jPbv-1667182469129"}]}, {"3": "UX19-1656914956616", "4": {"version": 1}, "5": [{"2": "2", "3": "pBdb-1667182469129", "7": [{"8": "252、 ssh （secure shell）安全外壳  ，。 连接远程服务器用的一种 网络安全协议。 默认端口号是22 "}]}]}, {"3": "mBXQ-1657362644486", "4": {"version": 1}, "5": [{"2": "2", "3": "g52G-1667182469129"}]}, {"3": "SWnu-1657362644790", "4": {"version": 1}, "5": [{"2": "2", "3": "QUot-1667182469129"}]}, {"3": "38hv-1657362644994", "4": {"version": 1}, "5": [{"2": "2", "3": "ljOn-1667182469129", "7": [{"8": "253、 eslint   代码 "}, {"8": "质量 / 风格", "9": [{"2": "b"}]}, {"8": "  检测工具      linting    检测？  （eslint 代码规范？ ts 类型规范？） （更强调 逻辑/命名 ？）              语法/变量/错误等\t\t"}, {"8": "JavaScript代码检查工具", "9": [{"0": "#374151", "2": "c"}, {"0": "rgb(247, 247, 248)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}]}]}, {"3": "UhE5-1657592166163", "4": {"version": 1, "s": {"ti": 28}}, "5": [{"2": "2", "3": "Dfmy-1667182469129", "7": [{"8": "   prettier  代码风格格式化工具  \t\t\t\t\t\t\t\t\t\t\t           （更强调 缩进/格式化代码 ？）   缩进/换行/分号/引号\t"}, {"8": "代码格式化工具", "9": [{"0": "#374151", "2": "c"}, {"0": "rgb(247, 247, 248)", "2": "bg"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}]}]}, {"3": "0OOG-1663148807393", "4": {"version": 1, "s": {"ti": 28}}, "5": [{"2": "2", "3": "P2oQ-1667182469129"}]}, {"3": "Dakz-1663148812324", "4": {"version": 1, "s": {"ti": 28}}, "5": [{"2": "2", "3": "n8BM-1667182469129", "7": [{"8": "lint-staged 限制代码提交规范（提交 "}, {"8": "（", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "git add"}, {"8": "）", "9": [{"0": "#F33232", "2": "c"}]}, {"8": " 的时候自动跑 lint 命令）"}]}]}, {"3": "f5h6-1753334384508", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11630/WEBRESOURCE862970fc261e17172dbfa9e12b0b690d", "w": 435, "h": 223}, "6": "im"}, {"3": "JFs4-1753334384509", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11632/WEBRESOURCE204d4129f5090b91589c43198c2c99a5", "w": 484, "h": 79}, "6": "im"}, {"3": "9G6j-1663148807778", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "Z73S-1667182469129", "7": [{"8": "这样配置就可以了"}]}]}, {"3": "o3Pj-1663148949861", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "juan-1667182469129"}]}, {"3": "1K3x-1663148950191", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "piZs-1667182469129"}]}, {"3": "CBVS-1657592227722", "4": {"version": 1, "s": {"ti": 28}}, "5": [{"2": "2", "3": "3oQ5-1667182469129"}]}, {"3": "1Fsl-1657592231669", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "Gf8P-1667182469130", "7": [{"8": "254、 QQ浏览器首页的"}, {"8": "视差滚动效果", "9": [{"2": "b"}, {"0": "#F33232", "2": "c"}]}, {"8": "：   "}, {"8": "background-attachment: fixed", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "   和  js控制 "}, {"8": "background-position-y", "9": [{"0": "#c7254e", "2": "c"}, {"0": "rgb(242, 242, 242)", "2": "bg"}, {"0": 16, "2": "fs"}]}, {"8": " 产生位移"}]}]}, {"3": "SnKV-1657768526912", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "l2KO-1667182469130"}]}, {"3": "zO0j-1657768527096", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "MzA3-1667182469130", "7": [{"8": "255、  判断对象是否为空 ( 1. Object.keys().length   2. JSON.stringify({}) === '{}'   3. for ... in 判断   4. Object.getOwnPropertyNames({}).length === 0 )"}]}]}, {"3": "Pfm6-1753334384510", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11631/WEBRESOURCEd458798b8b52ddc2eac799f2fe2414cf", "w": 436, "h": 181}, "6": "im"}, {"3": "aPdv-1753334384511", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11624/WEBRESOURCEe5b62f1e97ba979179d7b22cf95fe7ac", "w": 388, "h": 135}, "6": "im"}, {"3": "fipG-1662024036501", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "ymdD-1667182469130"}]}, {"3": "wK4d-1662024037837", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "GUER-1667182469130"}]}, {"3": "6qcv-1753334384512", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11621/WEBRESOURCE7d08f6ffd28f8cde34eb7d2f10ae1177", "w": 373, "h": 320}, "6": "im"}, {"3": "SrKB-1657686060314", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "ynNT-1667182469130"}]}, {"3": "AWc4-1657892510937", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "BOwx-1667182469131", "7": [{"8": "256", "9": [{"0": "#FFEE7C", "2": "bg"}, {"0": "#F33232", "2": "c"}]}, {"8": "、请求接口的方法："}, {"8": " form表单（action属性", "9": [{"2": "b"}]}, {"8": "）"}, {"8": "提交", "9": [{"2": "b"}]}, {"8": "（会引起页面跳转） 、  XMLHttpRequest、 Ajax、Axios   "}]}]}, {"3": "jHtT-1655304318650", "4": {"version": 1}, "5": [{"2": "2", "3": "Gg9Y-1667182469131", "7": [{"8": "   "}]}]}, {"3": "PFtE-1655304319784", "4": {"version": 1}, "5": [{"2": "2", "3": "x9s6-1667182469131"}]}, {"3": "aMFF-1658116154023", "4": {"version": 1}, "5": [{"2": "2", "3": "SIwW-1667182469131", "7": [{"8": "257、代码要每天都提交一次！不要按功能完成再提交，确保 服务器能备份到最新的代码，确保本地不会因为硬盘坏了而丢失代码（即使是万分之一的概率）。"}]}]}, {"3": "12Rx-1658118151304", "4": {"version": 1}, "5": [{"2": "2", "3": "Y5JE-1667182469131", "7": [{"8": "提交的时候只要确保基本的编译不会出错就可以了，只是提交到你的 feature 分支，不会影响到 master 分支的完整性的。 到时候一个功能开发完成了再通过测试后"}]}]}, {"3": "c1bS-1658118256923", "4": {"version": 1}, "5": [{"2": "2", "3": "5nqn-1667182469131", "7": [{"8": "再合并到主分支就可以了。"}]}]}, {"3": "6Sdc-1655304319997", "4": {"version": 1}, "5": [{"2": "2", "3": "dbUi-1667182469131", "7": [{"8": "参考 "}, {"8": "devOps", "9": [{"2": "b"}, {"0": "#F33232", "2": "c"}]}, {"8": " 的操作流程。"}]}]}, {"3": "3nSh-1658118327451", "4": {"version": 1}, "5": [{"2": "2", "3": "a3It-1667182469131"}]}, {"3": "ZWbd-1658118429370", "4": {"version": 1}, "5": [{"2": "2", "3": "F0xN-1667182469131", "7": [{"8": "CI / CD 持续集成 / 持续部署（交付） continuous integration"}]}]}, {"3": "fmnK-1658118346202", "4": {"version": 1, "s": {"ti": 224}}, "5": [{"2": "2", "3": "cnq1-1667182469131", "7": [{"8": "  continuous deployment（delivery）"}]}]}, {"3": "BMAc-1658118283858", "4": {"version": 1}, "5": [{"2": "2", "3": "EZKK-1667182469131"}]}, {"3": "4ypS-1658126968159", "4": {"version": 1}, "5": [{"2": "2", "3": "kYgp-1667182469131"}]}, {"3": "fPYx-1658126968325", "4": {"version": 1}, "5": [{"2": "2", "3": "ugYU-1667182469132"}]}, {"3": "6qeA-1658126968528", "4": {"version": 1}, "5": [{"2": "2", "3": "jgCb-1667182469132"}]}, {"3": "sJ7V-1658126968681", "4": {"version": 1}, "5": [{"2": "2", "3": "FZm8-1667182469132", "7": [{"8": "258、命名： 数组： menu"}, {"8": "s", "9": [{"2": "b"}, {"0": "#F33232", "2": "c"}]}, {"8": "   menu"}, {"8": "List   ", "9": [{"2": "b"}, {"0": "#F33232", "2": "c"}]}, {"8": "Nodes records"}]}]}, {"3": "Lq9n-1658153292771", "4": {"version": 1, "s": {"ti": 84}}, "5": [{"2": "2", "3": "fnpz-1667182469132", "7": [{"8": "对象："}, {"8": " ", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "data", "9": [{"0": "#F33232", "2": "c"}, {"2": "b"}]}, {"8": "  menu"}, {"8": "Info ", "9": [{"2": "b"}, {"0": "#F33232", "2": "c"}]}, {"8": "menu"}, {"8": "Dict ", "9": [{"2": "b"}, {"0": "#F33232", "2": "c"}]}, {"8": "  "}]}]}, {"3": "JFlT-1658153372370", "4": {"version": 1, "s": {"ti": 84}}, "5": [{"2": "2", "3": "N8zR-1667182469132", "7": [{"8": "Map: menu"}, {"8": "Map", "9": [{"2": "b"}, {"0": "#F33232", "2": "c"}]}]}]}, {"3": "1qG7-1658153482758", "4": {"version": 1, "s": {"ti": 84}}, "5": [{"2": "2", "3": "rjS5-1667182469132", "7": [{"8": "Set: menu"}, {"8": "Set", "9": [{"2": "b"}, {"0": "#F33232", "2": "c"}]}]}]}, {"3": "mbvK-1658153517993", "4": {"version": 1, "s": {"ti": 84}}, "5": [{"2": "2", "3": "7ItA-1667182469132"}]}, {"3": "GQAK-1658153521827", "4": {"version": 1, "s": {"ti": 84}}, "5": [{"2": "2", "3": "6y1a-1667182469132"}]}, {"3": "lOgY-1658153522064", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "HJix-1667182469132", "7": [{"8": "259、用  img标签的 "}, {"8": "onerror（@error）事件  ", "9": [{"2": "b"}]}, {"8": "解决图片的src地址访问不到报错（或者图片裂开）的问题"}]}]}, {"3": "tX2r-1658154019823", "4": {"version": 1, "s": {"ti": 0}}, "5": [{"2": "2", "3": "ihi7-1667182469132"}]}, {"3": "GRYL-1753334384513", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11633/WEBRESOURCE44b8aafaf97ee981e94d45b5879f066d", "w": 1066, "h": 110}, "6": "im"}, {"3": "Qn1d-1658118288396", "4": {"version": 1}, "5": [{"2": "2", "3": "lcuf-1667182469132", "7": [{"8": "原生："}]}]}, {"3": "uFaG-1753334384514", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11623/WEBRESOURCE534526d7b28ae224ede9959de78e5c16", "w": 1066, "h": 65}, "6": "im"}, {"3": "jFtU-1753334384515", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11620/WEBRESOURCE7b651d99a2c84784b8d523d9b954caa9", "w": 1064, "h": 211}, "6": "im"}, {"3": "mzuF-1658154263697", "4": {"version": 1}, "5": [{"2": "2", "3": "3y97-1667182469132", "7": [{"8": "VUE:"}]}]}, {"3": "BWGX-1753334384516", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11622/WEBRESOURCE6f78ea3e0179976b08682669773dadc6", "w": 1216, "h": 29}, "6": "im"}, {"3": "JoaC-1753334384517", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11619/WEBRESOURCEa5ac524d82fcf2ef1981f47b5456a634", "w": 1045, "h": 102}, "6": "im"}, {"3": "vJMd-1658154111802", "4": {"version": 1}, "5": [{"2": "2", "3": "etLU-1667182469132"}]}, {"3": "aiIq-1658154111973", "4": {"version": 1}, "5": [{"2": "2", "3": "Wpa3-1667182469132"}]}, {"3": "ZOPP-1658154326061", "4": {"version": 1}, "5": [{"2": "2", "3": "yevb-1667182469132", "7": [{"8": "260、 可拖拽的视口"}]}]}, {"3": "RFPp-1753334384518", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11625/WEBRESOURCE570cf8fa1076c81c0f7c1674e197d938", "w": 579, "h": 583}, "6": "im"}, {"3": "zZBj-1658154326233", "4": {"version": 1}, "5": [{"2": "2", "3": "vZ4e-1667182469133", "7": [{"8": " "}]}]}, {"3": "dl3H-1658154581922", "4": {"version": 1}, "5": [{"2": "2", "3": "bB7B-1667182469133", "7": [{"8": "cursor: col-resize;", "9": [{"0": "#F33232", "2": "c"}, {"0": 26, "2": "fs"}]}]}]}, {"3": "eVHk-1661846462593", "4": {"version": 1}, "5": [{"2": "2", "3": "5d3X-1667182469133", "7": [{"8": " "}]}]}, {"3": "xB4Q-1658154582150", "4": {"version": 1}, "5": [{"2": "2", "3": "2zcF-1667182469133"}]}, {"3": "CiPQ-1651157182639", "4": {"version": 1}, "5": [{"2": "2", "3": "KIFJ-1667182469133"}]}, {"3": "iRYW-1658154546629", "4": {"version": 1}, "5": [{"2": "2", "3": "qm7s-1667182469133", "7": [{"8": "261、 获取 "}, {"8": "外部样式", "9": [{"2": "b"}, {"0": "#F33232", "2": "c"}]}, {"8": " 的 方法"}]}]}, {"3": "fteJ-1658327844554", "4": {"version": 1}, "5": [{"2": "2", "3": "cXmY-1667182469133"}]}, {"3": "H0Pz-1658327845516", "4": {"version": 1}, "5": [{"2": "2", "3": "Lmh6-1667182469133", "7": [{"8": "window.getComputedStyle( ele )  获取该元素的所有外部样式对象 （有width属性 ......）\t  // 返回值是 整数"}]}]}, {"3": "f97q-1658327922134", "4": {"version": 1}, "5": [{"2": "2", "3": "5pGx-1667182469133", "7": [{"8": "ele.getBoundingClientRect( ).top / bottom / left / right   返回一个矩形对象，包含那4个属性    // 返回值是 "}, {"8": "精确 的", "9": [{"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": " 带小数点的"}]}]}, {"3": "iDmQ-1667791283175", "4": {"version": 1}, "5": [{"2": "2", "3": "AjwA-1667791283126", "7": [{"8": "ele.offsetWidth /  offsetHeight / offsetLeft / offsetTop        \t\t\t\t\t\t           // 返回的是 整数"}]}]}, {"3": "T4nf-1667791640554", "4": {"version": 1}, "5": [{"2": "2", "3": "XYMB-1667791640505"}]}, {"3": "x0b3-1753334384519", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11618/WEBRESOURCE8943404653a798a795caf3d300c0177c", "w": 863, "h": 213}, "6": "im"}, {"3": "l6UV-1658328050530", "4": {"version": 1}, "5": [{"2": "2", "3": "39yK-1667182469133"}]}, {"3": "Xmw3-1658327894085", "4": {"version": 1}, "5": [{"2": "2", "3": "GJ0A-1667182469133", "7": [{"8": "262、 package.json 文件中的 dependencies"}, {"8": "（项目的依赖）", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "  和 devDependencies"}, {"8": "（开发所需要的模块）", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "  和 peerDependencies 的区别："}]}]}, {"3": "hrMt-1658413122379", "4": {"version": 1}, "5": [{"2": "2", "3": "Fx7a-1667182469133", "7": [{"8": "（1）、如果没有发布 npm 包， 那么依赖放在哪里没有区别；"}]}]}, {"3": "Mv65-1658413158201", "4": {"version": 1}, "5": [{"2": "2", "3": "3m6A-1667182469133", "7": [{"8": "（2）、为了规范，如果是一直使用的包（项目中会一直用，如 ant-design-vue、day.js等），放到 dependencies 中; （这里的依赖是一定会被下载的）"}]}]}, {"3": "TnnB-1658413224550", "4": {"version": 1, "s": {"ti": 112}}, "5": [{"2": "2", "3": "gEyz-1667182469133", "7": [{"8": "  如果是开发时需要用到，上线后（线上环境）不会用到的，如 webpack、eslint、prettier、各种-loader、"}, {"8": "stylelint等...，放到 devDependencies 中", "9": [{"0": "#000000", "2": "c"}]}]}]}, {"3": "ysoP-1671678743162", "4": {"version": 1, "s": {"ti": 112}}, "5": [{"2": "2", "3": "u7Qw-1671678743108", "7": [{"8": "", "9": [{"0": "#000000", "2": "c"}]}]}]}, {"3": "PVTl-1658154551387", "4": {"version": 1}, "5": [{"2": "2", "3": "Jwx5-1667182469133", "7": [{"8": "（3）、"}, {"8": "peerDependencies", "9": [{"0": "#FFEE7C", "2": "bg"}, {"0": "#F33232", "2": "c"}]}, {"8": "    "}, {"8": "解决核心库被下载多次，统一核心库的版本问题", "9": [{"0": "#FFEE7C", "2": "bg"}, {"0": "#F33232", "2": "c"}]}, {"8": "   （项目依赖了 vuex 和 vant 这两个子库，而这两个依赖又都同时依赖了 vue 这个框架。在字库中分别声明 peerDependencies 防止重复安装 vue）"}]}]}, {"3": "S7wW-1753334384520", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11617/WEBRESOURCEf98e4f92ac73375e113c494b0c9c0e04", "w": 358, "h": 265}, "6": "im"}, {"3": "uSoo-1753334384521", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11615/WEBRESOURCE4ce7192defacb09498277de177f74847", "w": 334, "h": 337}, "6": "im"}, {"3": "OylQ-1671678724519", "4": {"version": 1}, "5": [{"2": "2", "3": "OmEs-1671678724466"}]}, {"3": "qa5w-1671678732607", "4": {"version": 1}, "5": [{"2": "2", "3": "spIr-1671678732553", "7": [{"8": "（4）、"}, {"8": "peerDependenciesMeta   对 ", "9": [{"0": "#333333", "2": "c"}, {"0": 16, "2": "fs"}, {"0": "<PERSON><PERSON>", "2": "ff"}]}, {"8": "peerDependencies 的修饰， 增加一些可选项的配置 。"}]}]}, {"3": "tFBD-1671676040178", "4": {"version": 1}, "5": [{"2": "2", "3": "AGrh-1671676040123"}]}, {"3": "3lSf-1658154551771", "4": {"version": 1}, "5": [{"2": "2", "3": "7HaS-1667182469133"}]}, {"3": "eALC-1658413395772", "4": {"version": 1}, "5": [{"2": "2", "3": "bobr-1667182469133", "7": [{"8": "263、 关于 css 字体 的一些学问  "}, {"8": "font-weight", "9": [{"2": "b"}, {"0": "#F33232", "2": "c"}, {"0": "#FFEE7C", "2": "bg"}]}, {"8": "  属性在不同操作系统（常见win和mac）上的显示效果的不同"}]}]}, {"3": "7moX-1659156289324", "4": {"version": 1}, "5": [{"2": "2", "3": "LMgj-1667182469133", "7": [{"8": "（0）、关于 win 和 mac 的默认字体 （不设置font-family属性，会自动读取操作系统的默认字体来显示的）"}]}]}, {"3": "WPik-1753334384522", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11628/WEBRESOURCEaab271755fc170602a984523504d59e1", "w": 635, "h": 68}, "6": "im"}, {"3": "uGif-1659154181062", "4": {"version": 1}, "5": [{"2": "2", "3": "37NG-1667182469133", "7": [{"8": "（1）、win上 ："}, {"8": "600为分界线。600往前一个效果，600往后一个效果", "9": [{"0": "#F8D2FF", "2": "bg"}]}]}]}, {"3": "FPYj-1659154248235", "4": {"version": 1, "s": {"ti": 28}}, "5": [{"2": "2", "3": "JIZZ-1667182469134", "7": [{"8": "      mac上："}, {"8": "每一个值显示的效果都不一样", "9": [{"0": "#F8D2FF", "2": "bg"}]}]}]}, {"3": "XdYr-1753334384523", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11614/WEBRESOURCEfd6b965fc1720ec4dcd4f829a016b7c5", "w": 839, "h": 705}, "6": "im"}, {"3": "Nl1N-1659154105674", "4": {"version": 1}, "5": [{"2": "2", "3": "j1Ij-1667182469134", "7": [{"8": "（2）、因为操作系统和浏览器的原因，字体在不同操作系统设备上显示的粗细会有所不同。这是 font-weight 这个属性存在的兼容性问题"}]}]}, {"3": "nxeT-1753334384524", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11627/WEBRESOURCE8b12d2be91e25235cd424269ef4c617a", "w": 843, "h": 226}, "6": "im"}, {"3": "osbs-1659154106058", "4": {"version": 1}, "5": [{"2": "2", "3": "UP63-1667182469134", "7": [{"8": "（3）、使用 @font-face 引入自定义字体，font-family 使用该字体名称   （字体后缀名 .ttf  .otf  .fnt ）"}]}]}, {"3": "2up9-1659155881926", "4": {"version": 1}, "5": [{"2": "2", "3": "sw1O-1667182469134"}]}, {"3": "atYD-1753334384525", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11610/WEBRESOURCEd4886c5e4bc8fcfcd5a03944bc1931d1", "w": 860, "h": 112}, "6": "im"}, {"3": "1m88-1659155975599", "4": {"version": 1}, "5": [{"2": "2", "3": "tSLq-1667182469134"}]}, {"3": "1S1x-1753334384526", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11612/WEBRESOURCE1a8b78a0ecfcff65f7906cd634d67a3a", "w": 808, "h": 362}, "6": "im"}, {"3": "xB3c-1659154626655", "4": {"version": 1}, "5": [{"2": "2", "3": "IznV-1667182469134"}]}, {"3": "HpxK-1753334384527", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11613/WEBRESOURCEfc481f60b542e6b59f224ebb45b37c22", "w": 940, "h": 426}, "6": "im"}, {"3": "XgFt-1659154106220", "4": {"version": 1}, "5": [{"2": "2", "3": "7UTy-1667182469134"}]}, {"3": "rcMG-1659156225852", "4": {"version": 1}, "5": [{"2": "2", "3": "R8Pi-1667182469134"}]}, {"3": "tiBi-1659156227335", "4": {"version": 1}, "5": [{"2": "2", "3": "RE2B-1667182469134", "7": [{"8": "264、vue 中 多个组件使用  window.onresize，只有一个生效，导致其他的给覆盖。"}]}]}, {"3": "3nwj-1753334384528", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11609/WEBRESOURCE294eeb8c958e4104fa385c8ed5dd68a0", "w": 529, "h": 328}, "6": "im"}, {"3": "Avk2-1753334384529", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11611/WEBRESOURCE27ec36a98eefc3c7daaa2feccbcf0d83", "w": 834, "h": 172}, "6": "im"}, {"3": "c8nf-1659233422332", "4": {"version": 1}, "5": [{"2": "2", "3": "UNgt-1667182469134"}]}, {"3": "Qwe2-1659233422728", "4": {"version": 1}, "5": [{"2": "2", "3": "UJPA-1667182469134"}]}, {"3": "AJPF-1659321165240", "4": {"version": 1}, "5": [{"2": "2", "3": "bxql-1667182469134", "7": [{"8": "265、js引擎（spiderMonkey、V8 ...）  （js解析器） 和  js编译器 （"}, {"8": "babel ", "9": [{"0": "#B620E0", "2": "c"}]}, {"8": "（", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "es6 转 es5，转化 jsx 语法，兼容旧版浏览器，ts语法"}, {"8": "）", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "、"}, {"8": " tsc", "9": [{"0": "#B620E0", "2": "c"}]}, {"8": " "}, {"8": "（", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "typescript compiler"}, {"8": "）", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "、 "}, {"8": "swc （", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "speedy web compiler"}, {"8": "）", "9": [{"0": "#F33232", "2": "c"}]}, {"8": "）"}]}]}, {"3": "zgoS-1753334384530", "4": {"version": 1, "u": "https://note.youdao.com/yws/res/11607/WEBRESOURCE64d31e1e38ae447de7847a88186ef0fa", "w": 935, "h": 108}, "6": "im"}], "__compress__": true, "title": ""}