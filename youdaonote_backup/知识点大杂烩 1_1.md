JavaScript 是 基于 原型 （对象）的语言  ？                 面向对象的JvaScript （OOJS）                                    

      **JavaScript 中的面向对象      数据属性 + 访问器（getter/setter）属性**

 

## **  ****!!     强制转为 布尔值**

![](images/WEBRESOURCE8ace5c6b11bcc81851282504b0f198b4截图.png)

![](images/WEBRESOURCE18eeb54f8252e9236d1e174b1d3b892a截图.png)

## **让 (a == 1 && a == 2 && a == 3) 返回 true       （****==****  存在**** 隐式类型转换****）**

![](images/WEBRESOURCE6b8fb50062955fd04a62d288cb737ea5截图.png)

一、js 中的 **toString 方法**

**(1)、返回 [表示对象] 的 [****字符串****]**

![](images/WEBRESOURCE2b1ef652e91ba9e8fff3adb06842c205截图.png)

**(2)、检测对象的类型**

![](images/WEBRESOURCEecdd90f8be3d29d1510aff8bec0e6d50截图.png)

**(3)、返回 数字 对应进制 的 字符串**

![](images/WEBRESOURCE2fc7654ae4e079369a591d5e20e72288截图.png)

二、**==****  的**** 隐式类型转换    **（类型不同的前提下才会转， 像同类型的 '' == '0' 结果就是 false）

1、**有 NaN 的，返回 false** （NaN 表示一个范围 不是具体的值/数字）A不是一个数字 B不是一个数字 但 A不一定就等于B涅   所以 NaN == NaN 返回值是 false

![](images/WEBRESOURCE23a38f9bbd0edc4520613d67ee1d22a5截图.png)

2、有 字符串/布尔， 字符串/布尔转数字 （false转0，true转1）

3、null == undefined  返回 true

4、有对象 （数组/对象/函数...），先调用 valueOf()方法 (转数字)  比较（如果不等）， 再调用 toString() 方法(转字符) 进行比较  （Date 对象 相反）    

 [**Symbol**.toPrimitive] （内置的 [[ToPrimitive]] 函数）  >  valueOf      >    toString

![](images/WEBRESOURCE3469f7f6feac9fde47c3d54bc292aeec截图.png)

通过重写 对象的 valueOf /  toString 方法 ，就可以比较了  a==1 && a == 2 && a== 3 .....

**++i ** ： 变量自加1，表达式返回变量值        

                                                 	      	    +在前，就先加1      

**i++**  ： 表达式返回原值，变量自加1

1、考验js基础

nodeList 类数组

打开淘宝网，怎么在console中获取当前网页最多的3个标签是啥，并且打印出来

思路： 先统计所有的标签， 把类数组转数组， 再遍历对象中的3个出现次数最多的标签

![](images/WEBRESOURCE965e3392ef7522f977e145b0fbc4805a截图.png)

优化版：

![](images/WEBRESOURCEa58f711aa6851ddb54a04d482ad094fb截图.png)

**常见的类数组**（有数组索引从0开始 / 有 length 属性 / 无数组内置方法 stash、pop 等）：  

**字符串**、**arguments**、**NodeList****、****类数组对象**(   {  length: 2  }  )

2、多个 await 异步请求 可能不会按顺序执行，要在触发时进行调用，防止获取不到数据

插入：        数值方法

1）、  toFixed( n )  方法 ： 将 数字 转为 字符串 

![](images/WEBRESOURCEf8cb8745fd726e24e36686e9b0230621截图.png)

![](images/WEBRESOURCE1c91aa8c155e1de86e4350a2813e3a9a截图.png)

**3、数组转对象**

(1) Object.assign( { }, [ 1, 2, 3 ] )

(2)  { ...[ 'a' , 'b', 'c' ] }

**4、对象转数组**

(1) 对象 for...in   + 数组 push                   for...of...  遍历数组 ！！

![](images/WEBRESOURCE6cb15339a833a814bc219e5444bf975b截图.png)

![](images/WEBRESOURCE7dd7cc8c12e89dbeb83dae836a957500截图.png)

两数之和优化：

![](images/WEBRESOURCE8235de443036ac5f59e9f1e4f82448ba截图.png)

var a = []

var o = {1: '淦', 2: '鈤'}

for (var i in o) {

  a.push(o[i])

}

console.log(a)   // [ '淦', '鈤' ]

(2)  Object.keys() / Object.values() / Object.entries()  / Object.getOwnPropertyNames(obj)  **包括 enumerable 对象**   => 返回数组

(3)  Array.from()  浅拷贝   伪数组  （有length属性，索引）  / 可迭代 （Set / Map 等）  为    数组

(1) 转 String  为  数组            //           String.split(' , ')      

Array.from('foo')              

// ['f', 'o', 'o']  

(2) + Set 去重 

const set = new Set( ['foo', 'bar', 'baz', 'foo'] )

Array.from(set)

// ["foo", "bar", "baz"]

**Array.from() 非数组对象转数组**

构造 100 个 0 的 数组

Array.from({length: 100}, () => 0)

new Array(100).fill(0)

Array.from({length: 100}).fill(0)

![](images/WEBRESOURCE3499bfd0e42788f8bed415485b0b3853截图.png)

![](images/WEBRESOURCE2714f5ec53d333fda8ec8ed380467d00截图.png)

![](images/WEBRESOURCE6e5ea1581e6e080c88555eebe5399265截图.png)

(4)  Array.of( )  用中括号 [  ] 将目标对象（可以是{} / [] / ' ' / number / boolean .... 很多都可 ）包裹起来        转为    数组

![](images/WEBRESOURCEa6c067de1e177ceac1ae7bf0104990fa截图.png)

**Array.of() 主要是 填补**** new Array( ) **** 创建数组的缺陷（ new Array(3) 创建的是一个 [empty, empty, empty]， 不能创建长度为1的数组 [3]，Array.of(3) 创建的就是 [3]  ）**

所以 创建数组 就可以有 4种 方法了： 字面量( [ ] )、new 关键字 ( new Array() )、Array.of()、Array.from()

5、数组外面只能拿循环内数组最后一项的解决：

直接声明成数组，然后push到数组中，再将数组转字符串即可

var arr = [1, 2, 3]

var newArr = [ ]

arr.forEach(item => {

  newArr.push(item)

})

newArr = newArr.join(' ')

console.log(newArr)      / /        1  2  3

数组转字符串： join()  /  toString()  /  toLocalString()

6、TS中使用lodash中的防抖函数debounce  （知道原理，自己实现）

![](images/WEBRESOURCE5d77ffc58c4d7e31c9a86eccaf58aa10截图.png)

小程序中使用防抖函数，注意不要用箭头函数形式，不然会报错 this指向问题找不到this          防抖节流函数 不使用 箭头函数 使用 function() { }

![](images/WEBRESOURCEdbb2fed552ccee19d49632f27689c713截图.png)

![](images/WEBRESOURCE193a7c96b06d5f9803b53d92d4518593截图.png)

![](images/WEBRESOURCEe4cc26d9e88c0bcacd0b19b6cc1b5016截图.png)

![](images/WEBRESOURCE72b029e6877b4b08e78ca2075d40cbb5截图.png)

7、查表法：

![](images/WEBRESOURCE0cde11397aae4ffee37cde02fbf5dded截图.png)

![](images/WEBRESOURCE3669ded38bbc9747901813ab0a12876e截图.png)

8、关于 Date() 内置对象

1)、默认是UTC格式的时间

对象形式： 

![](images/WEBRESOURCE1aa3fb30ffb3aafc7e8696c4acbcd78a截图.png)

字符串形式：

![](images/WEBRESOURCEa712408b8f67029c7fee536151938b52截图.png)

2)、转换为ISO 8601格式

Date.prototype.toISOString()

![](images/WEBRESOURCE18fad360e39515a952a380dd532d4bfc截图.png)

![](images/WEBRESOURCEbe1f0bd29995ed6ccbcf29cbbb091f84截图.png)

UTC 是时间标准；ISO-8601 是表示时间的一种标准格式

![](images/WEBRESOURCE43d5e537466a195287d7c7b2d50fa593截图.png)

![](images/WEBRESOURCEb24e9440b52f016a75c29831e5691ad6截图.png)

3)、常见方法：

getTime()  // 返回时间按戳

![](images/WEBRESOURCEe139d89b00a48ea3c615bd97482fbd76截图.png)

setTime()  // 返回时间戳

![](images/WEBRESOURCE823474f9d63ec24bdfadcb5c927da6d3截图.png)

// UTC格式

![](images/WEBRESOURCE862eacad2b93475909a2d1d2dd9c5074截图.png)

UTC 转 ISO-8601 存在一定的时间差 ！

![](images/WEBRESOURCEfa4e72bd4e0ae567000145209335640f截图.png)

9、delete（js方法）：删除对象的某个元素，被删除元素变成 empty / undefined，其余元素键值不变， 数组的长度不变

     Vue.delete（vue方法）： 直接删除数组元素，改变数组的键值， 改变数组的长度

![](images/WEBRESOURCEe9beb6d0e00aa483cb654ff03dffba94截图.png)

1、 删数组

![](images/WEBRESOURCE81652dd845f7b961b60da4111facc955截图.png)

2、删对象

![](images/WEBRESOURCE50e743ce45e40f95ae9f9dc6d0cad891截图.png)

Vue.delete(target, key)

target: 要删除的对象/数组/字符串..

key: 被删除对象的键/数组的下标/字符串的index 

![](images/WEBRESOURCEc47a05cc01f1b26bbeb01dd650bb71f3截图.png)

![](images/WEBRESOURCE19df8900468af5e590b980b7e92a8d4f截图.png)

*// 页面创建时绑定 监听页面刷新的事件*

    window.addEventListener('beforeunload', *this*.beforeunloadFunc)

设置addEventListener监听 beforeunload 事件不生效

![](images/WEBRESOURCEdbf680f9aae87a7845c087ea52c116c8截图.png)

*// 页面实例销毁 在 destroyed 钩子中卸载 监听页面刷新的事件*

  *private* destroyed() {

    window.removeEventListener('beforeunload', *this*.beforeunloadFunc)

  }

![](images/WEBRESOURCE2276e4b04c9e3f778be37241d188a4b2截图.png)

  *// 页面刷新触发 beforeunload 事件，对应的方法*

  *private* beforeunloadFunc() {

    *delete* *this*.form.date

  }

window.addEventListener(ele, event, boolean)

document.addEventListener(ele, event, boolean)

[*Event*Target.*addEventListener*(  )](https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener)

*const** **img** **=** document.**querySelector**(**"img"**)*;

*img*.addEventListener("mousedown", start);

*function* start(e) {

   consoel.log(121212)

  }

最后一个参数表示是 **捕获阶段（true）** 还是 **冒泡阶段（false），**默认是冒泡阶段（false）

捕获阶段（从上到下）：window 的先触发

冒泡阶段（从下到上）： document 的先触发

默认是先出发document，再触发window的

        ‘优雅’的删除对象中的元素：

![](images/WEBRESOURCEb5c0d10418851ceb043e696568eab027截图.png)

1、

```javascript
用JSON.stringify，看着还算优雅。
var d=JSON.parse(JSON.stringify(data,function(key,value){
    if(key=='created_at'||key=='deleted_at'||key=='updated_at'){
      return undefined;
    }else{
      return value;
    }
  }))
```

2、

要优雅的话，使用 [Lodash 的 omit 方法](https://link.segmentfault.com/?url=https%3A%2F%2Flodash.com%2Fdocs%2F4.17.5%23omit)移除不要的属性：

```javascript
const object = { 'a': 1, 'b': '2', 'c': 3 };
 
const result = _.omit(object, ['a', 'c']);
// => { 'b': '2' }
```

或者用 [pick 方法](https://link.segmentfault.com/?url=https%3A%2F%2Flodash.com%2Fdocs%2F4.17.5%23pick)只留下需要的属性：

```javascript
const object = { 'a': 1, 'b': '2', 'c': 3 };
 
const result = _.pick(object, ['a', 'c']);
// => { 'a': 1, 'c': 3 }
```

3、

自己实现一个 omit 也是可以的

```javascript
// 投机取巧式
const omit = (obj, uselessKeys) =>
  uselessKeys.reduce((acc, key) => {
    return {...acc, [key]: undefined}
  }, obj)

```

4、

特别粗暴的方法：

```javascript
delete obj.created_at
delete obj.deleted_at
delete obj.updated_at
```

5、

 Vue.delete 方法

![](images/WEBRESOURCEca3cd3ef8297fb1e51c8ab503e926a37截图.png)

10、params 和 query  路由传参

params传参要跟 name           参数不会显示在url上，即跳转页面或刷新页面参数会丢失（放在请求体中）

![](images/WEBRESOURCEa3464d2cf95ba94c8c010c2fd7a6c28c截图.png)

![](images/WEBRESOURCE622989357cc59d423a5789cbb218de63截图.png)

参数接收：

![](images/WEBRESOURCEa31062fa64a278411b600022b0aec768截图.png)

query传参要跟 path    （也可以跟 name）        参数直接拼接在url上，刷新页面参数依然存在

![](images/WEBRESOURCE12b605a1ec98fc2c477b69ff283f887c截图.png)

参数接收：

![](images/WEBRESOURCE3593774a03c8ac8e7c181b7ce07f52d7截图.png)

url上显示拼接过来的id

![](images/WEBRESOURCE4df16ce6b7abc6b85ad5b4404fe4d24c截图.png)

![](images/WEBRESOURCEb6fa5cdfdd4f12be91b848370f14bd4e截图.png)

11、数组每次push后要清除，不然每次循环都会push一次，每次点击都会循环显示多一次

12、调接口刷新数据，不用也不能强制刷新浏览器来实现刷新。如果中间有啥卡壳的，那就是方法或者其他地方的问题，（不是刷新数据就可以解决的问题），把关注点放在其他地方。

13、兼容小程序：  用$consts.get('AUDIT_STATUS').REJECT     不用 $consts.AUDIT_STATUS.REJECT

14、点击取消按钮报错 Error in v-on handler (Promise/async): "cancel"  found in

![](images/WEBRESOURCE6d7a77951d19d7640a6bd8308ccf97f4截图.png)

![](images/WEBRESOURCE947bce2684b18d3b7e12dfb231f1d227截图.png)

解决： 使用 async 和 await 异步请求接口数据，需要进行错误捕获 使用.catch 方法即可

直接return err 代表删除

![](images/WEBRESOURCE0b5d45cda6dab183171a95364a3595a9截图.png)

console.log()试讲错误信息打印输出

![](images/WEBRESOURCEdc930df35a43e33dc4b2de9e56921265截图.png)

15、template中使用插件的方法

![](images/WEBRESOURCE9972ae88f6baf1531eb0a39cf403ab29截图.png)

![](images/WEBRESOURCEd5b5795f37f4dfe9b3c0b9fce0df24af截图.png)

ts中：

![](images/WEBRESOURCEf0b9515622990e1a9cb02365cb537294截图.png)

![](images/WEBRESOURCE13b8a4779e551cae88b8b4e475c08ee0截图.png)

16、vuex刷新页面数据会丢失（可以存sessionStorage或者localStorage中），通过组件通信父传子的话，子组件会拿不到父组件传递过去的数据，所以做法是要在子组件里面监听该props值的变化，而不能只是单纯做判断

   

 1）子组件中单纯的打印this.details会取不到值

![](images/WEBRESOURCEec4fe782ab18ae71d273e7748547e4ad截图.png)

  

![](images/WEBRESOURCE94023b48c62afa23ea5efd12ed569779截图.png)

![](images/WEBRESOURCEcd7954bb622ea1f0dfae6a8d10af5b01截图.png)

![](images/WEBRESOURCE173f7ba32872bd1d04b2f4e2c2357d84截图.png)

    

2） 父组件中：

![](images/WEBRESOURCEa116a65710d124002dd5db85e69b0181截图.png)

   3） 子组件中：

![](images/WEBRESOURCE0a1e6087a5a4e961d841d82a428aa3eb截图.png)

 

![](images/WEBRESOURCE9c08c0f886f108911e29240c608d40e0截图.png)

 

 17、async/await 方法是异步的，无法保证在异步之后/之前 就 能够  按照想法 按顺序执行代码  所以要使用监听或者设置定时器进行延时。

18、页面和组件是不一样的 ！ 

19、html元素宽度不具有继承特性。块级元素(block)的宽度会占据一整行，所以看似继承了，实则不是。

让一个元素的宽度根据内容撑开，只要设置其display不为块级元素，不设置宽度就可以了，比如float,inline,position为absolute,fixed等等等等(很多，不是块元素，不要设置宽度)

20、箭头函数的this指向问题

指的是 往上的第一个普通 function 的上下文 （往上的箭头函数都不算）

**优先级**： new 构造函数  >   call / bind / apply 绑定(this, ...)    >   obj.foo    >    foo  

![](images/WEBRESOURCE5e8e2bc8779ee5829e25236d7225602c截图.png)

![](images/WEBRESOURCEd3036b3dc2c2b47c5e9c3232e7f17c62截图.png)

21、要么通过赋值 shareformid =  ,要么通过 onLoad 参数传递 

22、小程序获取当前页面路由  不支持vue的 $route.path 方式

![](images/WEBRESOURCE58a855375ff8f70720c49afe9e4e6cbe截图.png)

23、小程序中用 piui 组件的话 样式都用 custom-style 来写，用 style /  :style 的话小程序模拟器和真机会包多一层， 用view标签的话可以用style写样式，用 :style 的话 用 对象的形式来写方便些，或者可以用数组来写

![](images/WEBRESOURCEad96e5a1cab092c16dce5964450377ba截图.png)

24、[1, 2, 3, 6].includes(2)      ===         [1, 2, 3, 6].indexOf(2) > -1

25、 计算属性 / data 中的数据 都可以 :属性名 传给子组件，  子组件props接收

组件里定义的 props，都是**单向数据流**，也就是只能通过父级修改，组件自己不能修改 props的值，只能修改定义在 data 里的数据。

修改只能通过自定义事件通知父级，由父级来修改。

方法 / 计算属性 / watch 等 中定义的东西， **不分先后顺序**，也就是说第一个定义的计算属性里的的变量是在第二个中才定义的，这时候放第二个上面或者下面都一样，不影响。。。   vue 会  处理，， 做好相互之间的依赖的。

26、绑定取值

![](images/WEBRESOURCEbe73584f18d8c2575c6b68441a018695截图.png)

27、小程序用 :value + @change  代替  v-model

![](images/WEBRESOURCEa94867201e23d8afbcc16f3c74b4f3db截图.png)

**动态校验：**

方法一：  this.$set    或者  

方法二：  直接赋值

28、this.$set （Vue.set）方法： 

 

描述 ： 给    对象     （是对象 或 数组）添加一个属性，但是没有更新到视图上，this.$set方法 确保 新添加的属性是响应式的，并且可以触发视图更新

![](images/WEBRESOURCE421cac1b2dc083f08ee03fc085aa0822截图.png)

![](images/WEBRESOURCE57579a37ba53c85e8c148e1d233ce7ec截图.png)

//  响应式更新数组 （[ { name: '', phone: '', sort: '', userId: '' } ]）

![](images/WEBRESOURCE63bf173789a1daec2e50af50376d48eb截图.png)

用法：  this.$set(target, key, value)

            target：要更改的数据源 （可以是对象或者数组）

     key：要更改的数据

     value：重新赋的值

![](images/WEBRESOURCE2846a682a0c9615807d918822aa186c0截图.png)

（一） input 输入框

1）、

![](images/WEBRESOURCE9d3e03fa776f2b7c5066f33213c02f08截图.png)

2）、

![](images/WEBRESOURCE18889931db2b4e69fe839bfd2aee5975截图.png)

3）、

![](images/WEBRESOURCEdd82e491810b1a3532c9537a801cb85c截图.png)

![](images/WEBRESOURCE662d80b7b265d655770e5b801431238a截图.png)

有效：

![](images/WEBRESOURCE9d6a3d084aa2cdc7b9907dcfe2a9b035截图.png)

直接赋值 方法：（input输入框无效）， 得用 this.$set（上面写的方法）

![](images/WEBRESOURCE2dd29c76ba5d55ebc9601395354ec84a截图.png)

![](images/WEBRESOURCE66fa714385e5da7429a536c8f2ca5905截图.png)

无效：

![](images/WEBRESOURCEe046d7f0b638f707d41fbae6f3b87f6a截图.png)

（二）、 picker 选择器

this.$set （Vue.set）方法： 

![](images/WEBRESOURCE57c3c1bc95bee6c0832ba9711d5fe721截图.png)

选择点中有效：

![](images/WEBRESOURCEd4539910e0da01e67d787cb7093079c0截图.png)

直接赋值 方法：（picker选择器有效）

![](images/WEBRESOURCEefc4d178372e1c4271d48b8f2e9306d6截图.png)

选择点中有效：

![](images/WEBRESOURCEd4539910e0da01e67d787cb7093079c0截图.png)

29、

使用 flex 布局时，会没有宽度  和  高度，内容自动撑开的宽度  和   高度   为默认宽度

![](images/WEBRESOURCEa7aa1e8017708289f25fd99bea45acd7截图.png)

结果就显示：

![](images/WEBRESOURCE95002abe79be738e7ce98d5ba6de29e7截图.png)

把框的样式去掉就好了：

 <pi-card

      v-for="cardItem in cardInfo"

      :key="cardItem.id"

      margin="0 16rpx 16rpx"

      :custom-style="{

        height: '180rpx',

        borderRadius: '24rpx'

      }"

    >

      <template slot="body" class="pi-mg-top-12">

        <pi-list :border="false" hover-class="none">

          <pi-list-item :title="cardItem.text">

            <pi-img

              slot="left"

              width="100"

              height="100"

              :src="

                $consts.get('STATIC_IMG_URL') +

                  (cardItem.id === 'parent' ? 'account_ic_parents.png' : 'face_ic_teacher.png')

              "

            />

          </pi-list-item>

        </pi-list>

      </template>

    </pi-card>

![](images/WEBRESOURCE57369d3802615fd5d3180dd037c19c50截图.png)

30、 uniapp的几个路由跳转

1）、普通跳转  uni.navigateTo( )   :   保留当前页面，跳转到应用内的某个页面，使用 uni.navigateBack 可以返回原页面

![](images/WEBRESOURCE825a3dfb7ba64b4c2e9d82541c51cb12截图.png)

2）、无记录跳转  uni.reLaunch( )  :  关闭所有页面，打开到应用内的某个页面

![](images/WEBRESOURCEd0932dced45bc961a3e365c5b98afbdd截图.png)

3）、跳转 tabBar 页面 （首页）  uni.switchTab( )  :  跳转到tabbar 页面，并关闭其他所有非 tabBar 页面

![](images/WEBRESOURCEbe2d06587a31d4d61496020cbd523e59截图.png)

![](images/WEBRESOURCE4955bd1200a7404b8789792a7df7b8d6截图.png)

![](images/WEBRESOURCE29636afc44dcef15d4426f2eb398d86c截图.png)

4）、返回上一页面或多级页面  uni.navigateBack( )  :  关闭当前页面，返回上一页面或多级页面。可通过 getCurrentPaes( ) 获取当前的页面栈，决定需要返回几层。

![](images/WEBRESOURCE5752b034b5d99112344bf522f8be4b48截图.png)

![](images/WEBRESOURCEc8453982d914f79e8650320e056e631e截图.png)

![](images/WEBRESOURCE6c19840221ada18d817fc7c7a653d598截图.png)

判断每次进入该页面设置的路由跳转都生效，要在 onShow ( ) 页面生命周期使用， 在 onLoad( ) 使用只有在第一次的时候生效 （相当于vue的导航守卫功能）

![](images/WEBRESOURCEb348c54caa0a07729743ba382445220d截图.png)

![](images/WEBRESOURCEd19cd5447fa2912853c71e6ce00c3dab截图.png)

5）、关闭当前页，跳转指定页面 uni.redirectTo ( ) ： 

![](images/WEBRESOURCE14fb4b2e942e510a99fb3dd6a73b8be1截图.png)

31、  reduce 的 几种用法 

32、 浏览器控制台cdn引入外部js文件

<script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>

(1)、 DOM 方法：

在console中输入：

var script = document.createElement('script');

script.src = "https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js";

document.getElementsByTagName('head')[0].appendChild(script);

则在控制台中引入sockjs.min.js文件成功


![](images/WEBRESOURCEada982bd0f3cb027293a5bce711ab0b8截图.png)

（2）、console控制台插件方法： 

![](images/WEBRESOURCE894dddfa87ca0b19ffea0b917f7f6e99截图.png)

![](images/WEBRESOURCEb74b97abe1d60039930ca36459d29e36截图.png)

![](images/WEBRESOURCEa1028f0f22063f928d9c2df0d83f88f8截图.png)

groupBy  只是 分组， 排序不准确的

肯定就是你写的代码的位置那里有问题，不用怀疑，就硬摁着那几行代码看和注释调试就好!

33、uni-app修改本地存储中key的单个值

let _userInfo = uni.getStorageSync('userInfo');
_userInfo.nickName = res.data.nickName;
uni.setStorageSync('userInfo',_userInfo);

![](images/WEBRESOURCE4b38aed918c745adf16144625c8e71a4截图.png)

34、  Watch 只会监听一次 ！

![](images/WEBRESOURCEd82d0263cf184b77d0648b3d35f5014f截图.png)

![](images/WEBRESOURCE76fc3b4951397bb11605b3e2aad1d5fe截图.png)

![](images/WEBRESOURCEb76afa75044d62022fe6ee76ffb6f323截图.png)

35、find 找到（返回）满足条件的第一项         循环中的该项是什么数据结构，就返回什么

        findIndex 找到（返回）满足条件的第一项的下标     返回一个索引值

        some  只要有一个存在（成立），那就返回 true，其余情况都返回fasle

        every  所有条件都满足（每一种都为true）时返回 true，其余情况都范湖false

  1）    .map 映射  将一种数据类型转换为另外一种数据类型   （举例： 将字符串数组转为对象数组）

      

![](images/WEBRESOURCEd61ba941837ccf37b94a55c2411b02c2截图.png)

 

![](images/WEBRESOURCE21d00970435825fd75b4d0383fa9277c截图.png)

![](images/WEBRESOURCE3317fa0cd458a39cfa96ed31fcc25155截图.png)

       

 2）    .forEach  遍历  无返回值    遍历数组，对元素做操作，改变元素的一些东西  （举例： 遍历对象数组，当满足条件时改变每一项对象元素的value值）

![](images/WEBRESOURCE54f49424a8688716ae4c6ba4d14ce262截图.png)

 3）    .filter 过滤出满足条件的元素，最终组合成一个数组返回  （举例： 筛选出数组中满足选中条件的元素的个数）

![](images/WEBRESOURCE7aa72979ea2c948e82b8b8efe738042b截图.png)

.filter  最终无论怎么变化 都是不可能会改变原数组的结构的，返回值都是在原数组中进行筛选符合条件的选项， 不可能会变成另外一种数据格式的

.map  就是改变数据结构 用的

  join / Array.from

![](images/WEBRESOURCE8b6ff3438376f5c55f18d2efbacb4fbc截图.png)

4）    .reduce((pre, cur)) 自定义返回的数据类型 （很灵活， pre值可以有  [ [ ], [ ], [ ] ] ,   [ 0, 0, 0 ] ,   0,  { } ,  [ ]  ........  ）

pre的默认值为空的话不会加入计算的，默认当没有      

坏代码

![](images/WEBRESOURCE886967dd11a96378d45433e683bd6298截图.png)

优化 1   reduce  大法

![](images/WEBRESOURCEbbab17f75bdde50f5b1d6423cb970790截图.png)

优化 2      先塞个排序字段（要什么就自己往里塞什么）  ，再排序

![](images/WEBRESOURCE607305321c797dfb3faeaf26c807d51d截图.png)

优化 3    可以直接在排序里面判断 （这个最简化 牛逼）

![](images/WEBRESOURCE79833105d08b1527e5731756cad3e11d截图.png)

36、uniapp 页面跳转传参

1）、传单个 / 多个 参数

![](images/WEBRESOURCE913ed89ad451b60b4a5c481e90c76a53截图.png)

2）、传对象 / 数组

![](images/WEBRESOURCEd7152591724029fbfd1d1990723aae9e截图.png)

使用时 需要二次解密 

![](images/WEBRESOURCE3d85dfe3ae6b1cd0c0a27c3648de1fe8截图.png)

37、接口数据处理尽量都写在js（<script>标签）中，html（template模板）中一般只做显示，这样便于阅读和维护

![](images/WEBRESOURCE94565c2ba83a88e80282da1aaf88c55b截图.png)

![](images/WEBRESOURCE3418fff2408f80bc1a2c06485c73c97f截图.png)

![](images/WEBRESOURCE192553e8cf9781b41868815648ef3509截图.png)

![](images/WEBRESOURCEf3b73215ddf2a7b9be1fc956fc440a5f截图.png)

![](images/WEBRESOURCE06ef908e7438e4e8b9df512eb96aabc6截图.png)

38、 lodash 的 几种排序方法 

groupBy  会转数组为对象

![](images/WEBRESOURCE74fc09b20987d867b793a933e405194c截图.png)

![](images/WEBRESOURCEa6b3a9be42ef4d7355564fd03ada5a23截图.png)

groupBy 之前 ：

![](images/WEBRESOURCE2fedc3bc427f6ba320e2138fb7d50611截图.png)

 

groupBy 之后 ：

![](images/WEBRESOURCE26993cf58b59ad138dd6df8a197626c3截图.png)

orderBy 保留原来的数据结构

![](images/WEBRESOURCEc4be5538f5d1f322685b0f04680aecaa截图.png)

![](images/WEBRESOURCEbfe5d5ce02fdda6f410964537e6e2526截图.png)

其他方法

![](images/WEBRESOURCE518ecf52c30b22b65edbb29be4c0ab22截图.png)

![](images/WEBRESOURCE6b28437af9b9c657589e5014570261a0截图.png)

38、vue父子组件渲染过程     和    uniapp  的 页面声明周期比较：

加载渲染： 子组件的生命周期在 父组件的 beforeMount 和 mounted 之间

![](images/WEBRESOURCE9ee758ae2a17259f62833a81ee55acc1截图.png)

![](images/WEBRESOURCE48f35b8dc0238c88b592e76391d592f9截图.png)

39、 pi-upload-img 的 img-field 

不支持这样写

![](images/WEBRESOURCEb49d117aba9b65ed7454c58a4da0d743截图.png)

只能这样写

![](images/WEBRESOURCE3c27d9874b2aa63572e8a10a88511417截图.png)

40、字符串传参要解码

![](images/WEBRESOURCE289296c3af1e3a6e22b656d48b7931df截图.png)

  

41、 循环 ？ 理解 ？          findIndex      返回值是该项的索引值

![](images/WEBRESOURCEa61acf7c94686438d7d043d253b0aa93截图.png)

![](images/WEBRESOURCEfb13a5a20f07285c4f3bb22912a83b41截图.png)

![](images/WEBRESOURCEc083a0083acafe19f42e801035bb2b8a截图.png)

     find        返回值是   该项的数据结构

![](images/WEBRESOURCE49999dced08a078a9e388219aa4bef9e截图.png)

42、不要用这种路由写死的写法去判断，没有普适性（增加路由或者路由有所改动就会出问题）且 ios 不适用

接口没返回标识的，在页面跳转时自己直接加个状态值，去判断 就好了 ！

![](images/WEBRESOURCE8eb634584bcfbe4c607fe6c7b56fc3ca截图.png)

43、读代码

看代码的时候一定不要光看，要首先结合接口文档，根据返回的数据（格式），对着返回值参照代码写法，理解需求，知道其中的含义，

再去日志打印，再debugger调试，将组件/代码块 拆分开来看，首先把能看懂的先理解好，一定不要慌，都是那些东西，业务需求理解好了，就那样..

44、 做功能前先把数据结构设计好，然后再按照需求，接口想要的格式进行开发

![](images/WEBRESOURCEd8836156def2e8fdc476d8b2886cb53f截图.png)

45、  有时候样式不生效 调起来 比 写 逻辑 还 费劲   giao  !!!!

46、遇到问题，先不要慌（无论见没见过，不管什么报错，）， 试着去分析， 将问题拆解开来， 一小步一小步得来，慢不要紧，能做出来就行。 要分析和拆解，然后问题拆开后就很简单了，最关键还是心态要好！ 问题不大，不要慌。

47、排查问题（注释代码，自己增加的代码后出现的问题，就注释掉嫌疑最大的部分，把所有增加的代码都注释到排查完，就找到问题了，暴力循环）

![](images/WEBRESOURCEa12f483ef776fcddb25c8974d24ff777截图.png)

48、 过滤假值              arr.filter(Boolean)         

![](images/WEBRESOURCE63bafa374e5494fa06708c64eca9ca94截图.png)

Boolean()  返回一个true/false 的布尔值 ！

49、 不要在data中赋值computed的值，因为computed在初始化

的时候就会执行，而data是在mounted之后才执行，所以第一次会拿不到值

![](images/WEBRESOURCEdd331f9c08177da249c3a7cef237e7b7截图.png)

![](images/WEBRESOURCEd9a44d94e30d87e5f352bedc58e7d660截图.png)

![](images/WEBRESOURCE25531a3983abc56825cb07a415c5979b截图.png)

![](images/WEBRESOURCE08af773c7fd7fa3e41b7d269e1a56cc8截图.png)

![](images/WEBRESOURCE5bf0c2aef17d8c122032a9e851548b51截图.png)

直接 watch 监测下就好了

![](images/WEBRESOURCE7365e9e337010ad04ddaebbffb886f4d截图.png)

![](images/WEBRESOURCE618192a933beba8abec7811a9284148a截图.png)

50、写代码要注意兼容性   h5和小程序和移动端的

51、   只有小程序才会有shadow-dom(shadow-root类似)  所以要加条件判断兼容不同平台 

                                 https://uniapp.dcloud.io/platform  （ [条件编译](https://uniapp.dcloud.io/platform) ）

![](images/WEBRESOURCE8877af161861d594ab57b4e9437f0776截图.png)

52、       修改列表项（表单项）的最后一个元素的样式的写法：

![](images/WEBRESOURCEb33a3fa18b62359deefb5bee04f8fa70截图.png)

![](images/WEBRESOURCEc090a3ba85ce638f6277c4627998b987截图.png)

用  标签名 拿到 该列表的最后一项， 然后该项中有个同标签名的类名，选中该项， 再将该项的伪元素选择器选中，然后对其样式进行处理！！！

![](images/WEBRESOURCE9b2d56c04497c9cecdcfeba2e5523f8f截图.png)

 

52、  

![](images/WEBRESOURCE77f67f74581967cf1f3a9f4c0fce4d34截图.png)

53、数组扁平化？   就不用.map再push了

Array.prototype.flat( )   

![](images/WEBRESOURCE8622759616e00ae72ffb96a4a6fc77a3截图.png)

54、 npm install 报错 

**1）、**

![](images/WEBRESOURCEd91ed4cfafb62a4039a88e0f8d89cc3f截图.png)

**2）、**

![](images/WEBRESOURCE9984a60eff7db0ba530be2504cd8eede截图.png)

**3）、**

![](images/WEBRESOURCE6e786f02bc5576f87e6f670b4024fac2截图.png)

    yarn add / cnpm install / npm install 各种切换   少的包一个一个装

yarn install 装全部  /  yarn add 装单独 

添加（升级/降级）依赖    yarn add xxx@latest（最新版本）  /     yarn add vue-router@3.2.0 （降低/指定版本） /  yarn (global) upgrade xxx （ (全局)升级 xxx ）

移除（全局）依赖   yarn (global) remove xxx 

55、  子传父再传子， 要用 watch  不能直接在 mounted 里打印

56、  get 传参  （ post 换 get ）

没 JSON.stringfy() 之前

![](images/WEBRESOURCEe9af1eb384e70232d461650d3c4ec4eb截图.png)

![](images/WEBRESOURCEf975be7edd91961021ee0f966070823b截图.png)

不符合格式：

![](images/WEBRESOURCE00edf3af69635025abcd1adcf64810c5截图.png)

JSON.stringfy() 之后：

![](images/WEBRESOURCE28a743455c17ec99aa51cffbf6e91dc8截图.png)

![](images/WEBRESOURCEb7d543e92d2259835d5657f8cf5a2b33截图.png)

符合格式：

![](images/WEBRESOURCE0fe69a9d7c4cbe33b34a659209221731截图.png)

或者可以这么写   （不用JSON.stringify的话）

![](images/WEBRESOURCE35cdbbbc66f3ed6cc866a1a1d34a82af截图.png)

 同样可以i请求成功 : 

![](images/WEBRESOURCEa287c02d81e44250ebbf7ca1760ef4be截图.png)

57、改变对象的key - value（往对象添加字段）可以先转数组再使用 reduce

将下面该对象改编成   {

                                    date': '2021-09-12,

menuList: {a: '你好啊'} 

                                                                        }

var o = {'2021-09-12': {a: '你好啊'}}

(1)、先转数组再拍平

![](images/WEBRESOURCEbcb158797bad6691797218b98444422f截图.png)

（2）、使用 reduce 进行对象格式转换

![](images/WEBRESOURCEe83ab6791335a6681e3111cd4277009b截图.png)

58、 数据格式转换 ： 

![](images/WEBRESOURCE2058e9c1d49309758ce22ba120d0ea7d截图.png)

![](images/WEBRESOURCE232a63a43cef4b3b90d2065ab11bc0cf截图.png)

![](images/WEBRESOURCEe1e143af2b7f82cc714bbc53706ed480截图.png)

59、  v-for 循环

v-for in 数字  

<div  v-for="i in 5" :key="(i + 9).toString(36) + i"> 表示 i 为不为0的正整数开始 取值（1、2、3、4、5）共循环出 5 个值

![](images/WEBRESOURCEe10517ff4784cb5c1e83357c3c6c5c07截图.png)

![](images/WEBRESOURCEf694d049a0d135ba5aaeb223a5843999截图.png)

![](images/WEBRESOURCE65fea23288aa2886b761154594d38f7b截图.png)

![](images/WEBRESOURCEe4eac7b6e2e161d3d57c3f231cc1e956截图.png)

60、管理后台vue + ts  子组件绑定 v-model 值  

![](images/WEBRESOURCE35d8c22c8b291735cd8ab3e4e53d5fb8截图.png)

![](images/WEBRESOURCE8861c428cbad45ee9b8a671323c544a1截图.png)

![](images/WEBRESOURCEa732f2beb7c365a98970c3418d9af960截图.png)

61、 对象数组排序

![](images/WEBRESOURCE3d90d6af59c71d56e8d8fed61aeaeeb6截图.png)

![](images/WEBRESOURCE8c5b03990ed10250655dd81c47edc371截图.png)

62、 v-for undefined 会有问题，在请求接口拿到数据做处理的时候记得给默认值  [ ]  

![](images/WEBRESOURCEa8e6c9483f497de3453c966d231d689e截图.png)

![](images/WEBRESOURCE63b9880d5055bd288858c86349055bab截图.png)

不然会报错   In order to be iterable, non-array objects must have a [Symbol.iterator]() method报错

63、git commit 规范

64、 vw/vh  与 100%  的区别

（1）、 vw/vh  是  相对于  视口 宽度/高度   （只与可视化窗口的宽高有关、与父级元素无关）    

**100vh**  在 移动端 出现的问题 （移动端地址栏有时可见，有时隐藏。导致视口大小因此而变化）

![](images/WEBRESOURCE8dc1e23a903fbd2774f52755acc3c563截图.png)

解决： 使用 window.innerHeight   将高度正确设置为窗口的可见部分 。显示内容高度不受地址栏的影响

（2）、100% 是 相对于 最近一级的父元素的宽高

65、高度自适应引起的问题： 页面首次加载的时候会窄（缩一下）？    

CDN 有缓存，图片没法及时更新

![](images/WEBRESOURCE2d843fa8440ed674bcb6ddfba2ceed11截图.png)

66、el-form里面如果有且只有一个el-form-item里面是el-input的话

你在input里面输入完毕 按回车默认会自动提交 导致整个页面刷新

解决： 在 el-form 中加 @submit.native.prevent

<el-form :*inline*="true" :*model*="form" @*submit*.*native*.*prevent*>

67、 el-radio  单选可取消                    自己写个组件呗。

![](images/WEBRESOURCEf8cbc9919ed521c36d2e8f5c89ae3f30截图.png)

![](images/WEBRESOURCEff5044e6bda6caf11bd17ec6e72beb5e截图.png)

![](images/WEBRESOURCE56c473540af9815ff17874ee5fe37dab截图.png)

![](images/WEBRESOURCE2257448fdad14e0b1cf4a04824c22626截图.png)

使用：

<radio-checkbox>

   <el-checkbox-button>

  //

    </el-checkbox-button>

</radio-checkbox>

![](images/WEBRESOURCE897651911393036baa10d15375695008截图.png)

1)、单选

![](images/WEBRESOURCE8ed32a81a05aff6ed3af35f87ddbdeef截图.png)

![](images/WEBRESOURCE3fec1937043a613574f240f4a2d052b9截图.png)

2）、可取消选中

![](images/WEBRESOURCE97326a3bc2f50a48f8b234873c3dcd0e截图.png)

68、     更改正式/测试环境域名（包括静态服务器域名和api域名，小程序发版还涉及更换appid），管理后台和小程序

  1）、管理后台发布命令：一般都是 npm run build + gulp upload:dev （测试环境）/   npm run build + gulp upload:release （正式环境）

   或者直接在package.json中配置了发布命令 ，那就 直接 npm run fabu 就行了

![](images/WEBRESOURCEb2be2d3e6d4d628df8c3c2d1a955df64截图.png)

  2)、小程序发布命令

  2.1）、发布h5：跟管理后台一样，npm run build + gulp upload:release (upload:static)

  2.2）、发布微信小程序（上传微信公众平台）npm run dev:mp-weixin / npm run build:mp-weixin ，分别会生成两个静态文件，如下

![](images/WEBRESOURCEcdd507f358e111106f594599f172f546截图.png)

一般发版的话会选择用  build:mp-weixin  这个来 run， 然后再在开发工具中点上传

![](images/WEBRESOURCE8631c518101c3eedc11485ff0c5ea041截图.png)

然后就能看到体验版和正式版了：

![](images/WEBRESOURCEab36159c2bacd8eef888deed74688e63截图.png)

![](images/WEBRESOURCEc70075942058748d8ff8b6579bad1401截图.png)

![](images/WEBRESOURCE1007906bc4a55034ea35b413cfa61e8c截图.png)

![](images/WEBRESOURCE22e7d260c9904f0bbf1bdb56097fd06b截图.png)

 

小程序改了api域名的话，在开发者工具中运行，相对应的appid也要对应的上，要改回去

（1）、sadaiscloud环境： VUE_APP_BASE_API = 'https://api-canteen.sadaiscloud.com'            WX_APPID: 'wxb31658291f32d474', // 微信APPID

（2）、ipon-group环境：  VUE_APP_BASE_API = 'https://api-canteen.ipon-group.com'               WX_APPID: 'wx6d830d0c4b543083', // 微信APPID

 

![](images/WEBRESOURCEb2f14fa24f9337efc184b21958a2ec20截图.png)

![](images/WEBRESOURCE155b08e816dd81750e94347fd1d52040截图.png)

使用 HBuilder 打包 公众号 H5 步骤： 

![](images/WEBRESOURCE8fda639a5389d05ec86d369f529f7ca6截图.png)

![](images/WEBRESOURCE15e98ea6d8d56e78bbdbd73db02acbdd截图.png)

![](images/WEBRESOURCE61f9a9e96202cd894526f25f9646995a截图.png)

![](images/WEBRESOURCEb6f81bddda375c9de681cecbe2286cf4截图.png)

![](images/WEBRESOURCEef47b3ebdb7e497642bfcea1cdc03fc2截图.png)

![](images/WEBRESOURCE8010a690e20cbb106fe4d87ecbf693ed截图.png)

![](images/WEBRESOURCEeda6d6feee0c945b5bd32b61f3bfa5f5截图.png)

![](images/WEBRESOURCE7179d01037bd7550d8290b985fc4540d截图.png)

69、可选链** ?.  **(ES2020)代替 &&  进行判空处理

使用规则：**?.** 只检查左边部分是否为 null/undefined，如果不是则继续运算 

不会检查右边

![](images/WEBRESOURCEc3b9e71884161e4f157c781a693a7cbf截图.png)

![](images/WEBRESOURCEf7c83eaff0ddcafd8154316989b12e01截图.png)

      [javascript info 可选链](https://zh.javascript.info/optional-chaining#bu-cun-zai-de-shu-xing-de-wen-ti)

70、使用 Vue v-model number 修饰符可以实现让输入框输入的内容，自动转换为 number 类型。

71、

![](images/WEBRESOURCE7f9840c8ff746f76e1f6b91e34c5f336截图.png)

72、记一下数组的 splice 的用法：  **返回被删除的元素数组**

1）、删除

```javascript
var myFish = ['angel', 'clown', 'mandarin', 'sturgeon'];
var removed = myFish.splice(2);

// 运算后的 myFish: ["angel", "clown"]
// 被删除的元素: ["mandarin", "sturgeon"]
```

```javascript
var myFish = ['angel', 'clown', 'drum', 'mandarin', 'sturgeon'];
var removed = myFish.splice(3, 1);

// 运算后的 myFish: ["angel", "clown", "drum", "sturgeon"]
// 被删除的元素: ["mandarin"]
```

2）、替换

```javascript
var myFish = ['angel', 'clown', 'drum', 'sturgeon'];
var removed = myFish.splice(2, 1, "trumpet");

// 运算后的 myFish: ["angel", "clown", "trumpet", "sturgeon"]
// 被删除的元素: ["drum"]
```

3）、插入

```javascript
var myFish = ['angel', 'clown', 'mandarin', 'sturgeon'];
var removed = myFish.splice(2, 0, 'drum', 'guitar');

// 运算后的 myFish: ["angel", "clown", "drum", "guitar", "mandarin", "sturgeon"]
// 被删除的元素: [], 没有元素被删除
```

73、（1）判断对象是否为空：

Object.keys / JSON.stringify() === '{}' / 

（2）判断对象是否相等：   最好使用工具库，比较可靠和边界情况考虑比较完全

简单比较 ：   引用数据类型指向不同的内存地址，不可直接 == 或者 ===

                       浅拷贝可以试一下

 const a = { name: 'Hbin' }   const b = { name: 'Hbin' }

（1），a == b /  a === b     ===>    结果都为 false

（2），const c = a          c === a  true               c === b  false 

（3），循环遍历简单实现下（对属性值为复杂类型，属性值为null       或者         一个属性是undefined一个没有该属性）

### **浅比较
**

> 只做第一层数据的查询，跳过数组、对象、方法





利用es6的every函数做最优处理


```js
// 2. shallow compare
function isObjShallowEqual(obj1, obj2) {
    const keys1 = Object.getOwnPropertyNames(obj1);
    const keys2 = Object.getOwnPropertyNames(obj2);
    if (keys1.length !== keys2.length) {
        return false;
    }
    const flag = keys1.every(key => {
        const type = typeof obj1[key];
        // do not check function, array, object
        if (['function', 'array', 'object'].includes(type)) {
            return type === typeof obj2[key];
        }
        // if unequal, return true
        if (obj1[key] !== obj2[key]) {
            return false;
        }
        return true;
    });
    // if found unequal, then return false, which means unequal
    return flag;
}
```

74、（1）判断数组是否为空：

1、 array.length === 0  /  2、  var found = array.find(i => i), if (!found) console.log('数组为空!') / 3、 var foundIndex = array.findIndex((i, idx) => idx > -1) , if (foundIndex === -1) console.log('数组为空!')

（2）判断数组是否相等：  最好使用工具库，比较可靠和边界情况考虑比较完全

简单比较 ：

（3）拷贝数组：

1、 var a  = []

75、Array.prototype.sort() 用法

1）、arr.sort() :  数字 => 从小到大排序    字符串数字  => 按照unicode顺序排（字符大到小排序）

2）、 Function:     

                    arr.sort((a,b) => a>b ? -1 : 1)      从小到大               -1: a在b前  （从小到大排）  1: b在a前   （从大到小排）

                    arr.sort((a,b) => a>b ? 1 : -1)      从大到小

     或者：

         arr.sort((a,b) => (a.auditStatus - b.auditStatus) )     正序    从小到大 排

         arr.sort((a,b) => (b.auditStatus - a.auditStatus) )     倒序   从大到小 排

3）、eg: var a = [

                             {date: '2021-10-15', visitor: 'zs'},

                             {date: '2021-08-25', visitor: 'ls'},

                             {date: '2021-12-24', score: 'ww'} 

                           ]

按照时间降序排（利用dayjs工具库）： a.sort((a,b) => ( this.$dayjs(a.date).isBefore( this.$dayjs( b.date)) ? -1 : 1 ) )

dayjs 获取时间戳 （**+dayjs()**  /  **dayjs().valueOf()**）

![](images/WEBRESOURCE511cb0e0474b4d4a83243072b2341ce1截图.png)

76、Array数组去重   12种方法 ：

Array.from(new Set(arr))

[...new Set(arr)]

77、 vue 中 操作 DOM .  **须在页面数据加载完成后对DOM进行操作  **

**！！！！！！！！！！！！     须在 ****this.$nextTick()**** 回调函数中执行**

如何在页面渲染后操作dom, 而且只执行一次 ？

在接口请求成功的回调中使用！

可以在mounted中$nextTick, 也可以在计算函数中$nextTick.

![](images/WEBRESOURCEef08122ba902071f60e6ed441ac07f09截图.png)

78、

饭堂小程序开发总结：

周期： 7.30-8.25   3周时间

完成功能：学生绑定/管理员模块/订餐模块/我的模块

79、 如果是 html 中 不会用到的属性， 可以不放到 data 或者 computed 中，  直接在 created 中 this.xxx 就可以了， 性能考虑？ （字符串 -->  对象 ）

data 监听  每个 {{ }}  对应一个watcher 监听器

                 一个组件  =》 一个 watcher

80、git submodule 

81、移动端（小程序/ APP）滚动容器选择

![](images/WEBRESOURCE5a0848c5f476df806d796b2f3add3de0截图.png)

 

![](images/WEBRESOURCE610df04b8a4291268262e988c1d74b0e截图.png)

      （1）、纵向滚动      scroll-view   (pi-scroll  scroll-y)

![](images/WEBRESOURCEbfcb89d6a74e1e6fa54aea83a2f198d3截图.png)

       （2）、 swiper +  纵向滚动         swiper   (pi-scroll)

  

![](images/WEBRESOURCE81937debd3968214c4c7bd5abd2c9e76截图.png)

       （3）、 mescroll-uni  +  纵向滚动     (pi-scroll-container +  pi-scroll  +  mescroll-uni 上拉刷新/下拉加载)

![](images/WEBRESOURCEa53957242e2d271e6391d997a9b220a4截图.png)

82、js 清除浏览器缓存方法

1），用随机数   URL 参数后加上 "?ran=" + Math.random();             ?ran=Math.random()

2），用随机时间    在 URL 参数后加上 "?timestamp=" + new Date().getTime();

83、dom 获取 labei for属性名对应的标签

![](images/WEBRESOURCE19062f502f1c86d67dbf46d69635eab3截图.png)

84、   git  变基

![](images/WEBRESOURCEfb96c99b6cfc915e905e28073c567e8f截图.png)

85、 写代码不仅要考虑可读性， 还要考虑兼顾性能优化方面的问题 !!!!  减少重复代码的使用和出现 

**性能优化 ：**

1、**懒加载**： 在需要的时候加载，随载随用（路由、图片、滑动触发、虚拟列表）

![](images/WEBRESOURCE990a044d040fdbc4107afa3dfe47b784截图.png)

异步组件、组件 import () => {} 动态引入、webpack splitChunk

2、**按需加载： **根据需要去加载资源（常用 UI 组件库）

3、**不生成.map文件：**配置里productionSourceMap设置成为false，能差不多减少一半的体积。

4、**通过cdn方式引入：**

![](images/WEBRESOURCEdffb8d2576f0c34a7535b4ff27b97f84截图.png)

5、**图片压缩：**

利用一些网站对大体积图片进行压缩，例如：[tinypng](https://link.segmentfault.com/?enc=%2B0gRkOoj%2F3PVB33o3JRWFg%3D%3D.fpBXfpq1CWqLejpIqIkxB6x2XOdSeLuR%2FYNSp5oMB5I%3D)

86、pi-checkbox    的   :value +  @input   @change   都没有   .stop   阻止冒泡事件   ，得  包多 一层  view 标签 ，用 tap 事件来 代替 

![](images/WEBRESOURCEf709a2cceb6b32b428a114fc8b83aa81截图.png)

![](images/WEBRESOURCEc523728c79c823f816327e4ef3441b95截图.png)

87、导出不调接口 分页数据 /  按照查询条件筛选的数据 无法查询   只能拿当前页面数据 

![](images/WEBRESOURCE10549c6629dd525578c019653d2dba54截图.png)

![](images/WEBRESOURCEbb8cb2d887f2eab120637da4ec7aff1c截图.png)

88、父组件传个boolean控制  子组件dialog弹窗  是否显示    用  v-model  !!!!!!

父组件：

![](images/WEBRESOURCEdbbc2f63aea64dc0d6f4e166529bc358截图.png)

子组件:

![](images/WEBRESOURCE448a40c68c1cce5a81ad4a93fc0286c5截图.png)

![](images/WEBRESOURCE233e066c605c6a41bb5d0f5e03c751fb截图.png)

常规的js - vue 是 定义在 model 上 的

model: {

  prop: value,

  event: change

}

![](images/WEBRESOURCEabc6b43d3efc0cdae0752c6bf54b3c2c截图.png)

引用  60、

![](images/WEBRESOURCEa463f7c18eae1ff0ffe3764051d4f7cb截图.png)

注意 ：  

小程序中没有  v-model  ，所以只能在管理后台使用，小程序 还是使用  :visible="visible"  和   this.$emit('val', val)  来代替了 ！

89、  踩坑：  el-input   同时设置  type  和  maxlength ，   maxlength  不生效  ， 需自己改成  oninput

![](images/WEBRESOURCE80e152a772367e81c424d75b7d6a21c9截图.png)

![](images/WEBRESOURCEdf28f672343b0cc3d71604bc2022f2ed截图.png)

90、 el-form-item 不可以离开  el-form 单独使用 ！

91、

![](images/WEBRESOURCEe37d117a54f7bd88f4fa6d4c8d1c3f50截图.png)

92、   reduce  用法  ：    慢慢积累     可以返回你想要的东西 （自己来定义要返回什么东西）

删除数组对象中的指定对象      reduce（该对象元素的prop字段是 'tag_bodyAge'的时候不添加到该数组中， 也就是删除该元素的意思了）

![](images/WEBRESOURCEcd5bbf5433c99d1b7153f6085e6a77fd截图.png)

93、 数组的 重组  /  去重  /  排序  /  分组  

94、@Watch($route)  只能在外面（全局使用），在单独的页面内监听不生效 （页面内当前路由不会变）

![](images/WEBRESOURCE5fa5de2bed0b53a27f65588b6b54086a截图.png)

![](images/WEBRESOURCE2cbfeab49ec5108eaf88bc38a80769f6截图.png)

![](images/WEBRESOURCE12ee2a5b27dc4590fa1bbfc39269792d截图.png)

95、 拉起app 地图  deeplink  文档

96、  管理后台  创建 和 保存  区别  ：  创建没传id， 保存需要传id （没 id 会新增一条 ------   相当于创建了）

97、顺序执行异步代码

![](images/WEBRESOURCEf102dedb2cca97b2b921335dffbc781c截图.png)

98、每个公司都有自己的规范，熟悉完公共模块有哪些，项目蓝湖设计图看下来大概确定哪些组件是大概率会封装的，去项目里面components 里面找，现成的组件拿来就用，避免重复造轮子，和自己写的不全；

        涉及到vuex的数据管理的，要清楚流程，确定好修改不会影响到其他的地方时才进行更改（一般新增不会大影响，都是修改和删除就要很注意）;

        可以尝试自己从0搭建后台系统的框架（初始化项目），但是要按照规范来，避免出现本来就是模板化的东西的bug

99、后台管理系统 upload 组件  包括很多的类型，现在是  uploadImage 和 uploadFile 图片归一种，其他的文件格式都归 uploadFile,传对应的参数进行不同格式的类型的显示

100、 @1、空值合并运算符   ??   只有 左边为 undefined 或 null 时，返回右边   0  ??  24  =====>  0       null  ??  'youngG'  ======> 'youngG'

          @2、逻辑或    | |    是左边为假（ false / null / undefined / 0 / ' ' / NaN ）时，返回右边       0  ||  24  =====>  24       null  ??  'youngG'  ======> 'youngG'

          @3、逻辑空赋值    ??=    当 左边为 undefined 或 null 时， 为右边赋值  const a = { duration: 300 },     a.duration  ??=  50       =====>     300

                                                                                                                                                                           a.speed  ??=   600       =====>     600

101、 

![](images/WEBRESOURCEdc7cea57dbbb52f15143ef66e70c31f0截图.png)

![](images/WEBRESOURCE385db03d60b37ad211e630ad7c95f79c截图.png)

102、**vue 中的 .sync 语法**

**vue 中的 .sync 语法** 相当于不用在子组件中 this.$emit 就可以直接修改 子组件的值了， 相当于一个自动更新父组件属性的监听器

直接在子组件中直接修改 @PropSync 的值， 会自动更新到父组件中

**父组件：**

![](images/WEBRESOURCE559c5d3bef4613b86fcc7c4c49d9aee5截图.png)

**子组件：**

![](images/WEBRESOURCEfbd2af6b21b83f54c7956efeecb3d996截图.png)

‘show’ 是在父组件定义的传过来的要这么写， syncedShow 是子组件接收到'show'这个值后重新命名的（相当于syncedShow = show 赋值操作）

![](images/WEBRESOURCEdb9c58e88c677fa99ec866c95e5c711d截图.png)

![](images/WEBRESOURCEc29ac22a1da1d3adc76c36cec7806896截图.png)

直接在子组件中直接修改 @PropSync 的值， 会自动更新到父组件中

相当于： 

**父组件：**

![](images/WEBRESOURCE78404c223484a08ecd1be34be8b13ed3截图.png)

![](images/WEBRESOURCE98b44993fefef26872d5009da67dda85截图.png)

**子组件：**

![](images/WEBRESOURCE2caef2c628262a6b77990fed8cacdd6d截图.png)

![](images/WEBRESOURCEdbc674003744087e42311b8a76873525截图.png)

节省绕来绕去的代码 。。。

**@Prop传过来的值 不可直接修改**

**@PropSync  可以**

**@Model  不可以直接修改**

**@ModelSync 同理 可以**

![](images/WEBRESOURCE0f90f16671eadbcc82701d8d4d989e9c截图.png)

‘selectedNodes’ 是在父组件定义的传过来的要这么写， syncedSelectedNodes 是子组件接收到'show'这个值后重新命名的（相当于syncedSelectedNodes = show 赋值操作）, 'change' 是 @Model装饰器 (绑定父组件的 v-model  值) 在子组件的写法，相当于值发生改变同步接收的意思

**一、@Model装饰器**

**父组件：**

![](images/WEBRESOURCE5e40a3718ca8854d32414fde70ab14ed截图.png)

**子组件：  **

1、@Model 装饰器

![](images/WEBRESOURCE538abb0a7f0aff6c96ce3fe094fec420截图.png)

2、监听值的变化：

![](images/WEBRESOURCE99413a427f03eef262ae7da32974d249截图.png)

3、最后还需要 $emit

![](images/WEBRESOURCE9f358ec99405ba8be64b0c6f29584d26截图.png)

**二、@ModelSync 装饰器    （父组件 v-model  +  子组件 ModelSync）**

![](images/WEBRESOURCEd9a77f1360ae181b06df1db29a9d44c8截图.png)

**父组件：**

![](images/WEBRESOURCE3be9d0018f51b7ce4200d1c5ff6a2573截图.png)

**子组件：**

直接modelSync， 少去了 $emit 这一个步骤     （直接这样就行了）

![](images/WEBRESOURCE32f431f79fbe6747cc245ccf005a1643截图.png)

103、随机获取10位无序字符串 （可做 id 值 使用）  guid

Math.random()

      .toString(36)

      .substring(2, 12)

![](images/WEBRESOURCEf6c286cecb1eb00670c985476fef8f1d截图.png)

![](images/WEBRESOURCE1ed0efa1e10fe1b1a3b1a7aad797783b截图.png)

Math.random() 返回 0-1（大于0 小于1） 的随机小数，一般是小数点后16位（偶尔17）

Number.prototype.toString() （  eg:  (12).toString() ）参数 radix 代表进制数，没写默认是0，参数 2-36 可选， 36进制 则是 数字 0-9（10个） 和 字母 a-z（26个）组成    同理  32进制 则是 0-9 + a-v 共 32个组成

String.prototype.substring() （ eg: Math.random().toString(36).substring(2, 12) ）下标为2（从0开始算）起，取到下标为11 ，共 10个数

104、setTimeout 定时器  clearTimeout()

105、数据模型转换   

最终想要的格式 ：  [ { id1: '', text: '' }, { id2: '', text: '' }, { id3: '', text: '' } ]

已知：var knowList =   [ { package: {}, partitionDto: { name: 'aaa', id: '111a' } }, { package: {} }, { package: {}, partitionDto: {} }, { package: {}, partitionDto: { name: 'bbb', id: '222b' } }, { package: {}, partitionDto: { name: 'ccc', id: '333c' } } ]

求解如何得到想要的结果？

过程： 先 过滤掉没有 partitionDto 的字段，然后自己组想要的格式，拿id作为唯一值

var ids = [ 'id1', 'id2', 'id3' ]      =====>      [ { 'id1': { name: 111 } }, { 'id2': { name: 222 } }, { 'id3': { name: 333 } } ]   （拿个变量接收  var a =  [ { 'id1': { name: 111 } }, { 'id2': { name: 222 } }, { 'id3': { name: 333 } } ]  ）

先构造个临时对象变量  var tempObj = { }

knowList.forEach( i => {

  tempObj[i.partitionDto.id] = i.partitionDto 

} )

  Object.keys(ids).map(i => {

    return {

      id: i,

      text: tempObj[i].name

    }

})

自己  转换下数据格式  (构造出想要的格式)  就好了  =====>   要什么，就定义什么。

106、el-popover el-tooltip el-popconfirm    防止父元素冒泡：  直接在外层嵌套个 div， 然后  直接加 @click.stop

![](images/WEBRESOURCE65c83b3e9d4818ae63a3785b519c46eb截图.png)

![](images/WEBRESOURCEe2818a8f3c0a8e9f70e4372f3a7415a8截图.png)

107、 **参数作用域**：   当函数的参数有默认值时，会形成一个新的作用域，这个作用域用于保存参数的值。

参数就是为函数服务的，首先会找到参数的值，没有参数再回去找函数中有没有声明

![](images/WEBRESOURCE02ffcbd5ae537b5bb18c7741eb6b8c1e截图.png)

108、

![](images/WEBRESOURCEbb47af2eda3be435faff933a2cf91848截图.png)

109、  

![](images/WEBRESOURCE5f048bff548b8e66024462e3b9cbc964截图.png)

110、 懒加载  （动态加载） ：  点选的时候才调用接口加载数据，而不是  用 循环调用接口（递归  /  套娃）的方式 去 一次性调多次接口把数据一次性显示出来 ！！ （不然的话都可以直接让后端一次性把所有的数据都返回给你好了）

                   每点一次的时候才会 调用一次接口 获取（该层级level）的信息， 不会 一次性给你返回  ！！！！  （点击的时候才发起的请求 ！）

 如  Tree 树形组件    Cascader  级联选择组件  ...      都有动态加载的模式 （传  node 和  data，  不用 按照默认模式的数据格式去展示       参数有    lazyLoad (node, resolve)   ）...  

目前我找到的对递归最恰当的比喻，就是查词典。

我们使用的词典，本身就是递归，为了解释一个词，需要使用更多的词。

当你查一个词，发现这个词的解释中某个词仍然不懂，于是你开始查这第二个词，可惜，第二个词里仍然有不懂的词，于是查第三个词，这样查下去，直到有一个词的解释是你完全能看懂的，那么递归走到了尽头，

然后你开始后退，逐个明白之前查过的每一个词，最终，你明白了最开始那个词的意思。

![](images/WEBRESOURCE0c05276f2181f51e9535bf6d5475c437截图.png)

![](images/WEBRESOURCE68658daaa59f5e4fc373809c2b2af698截图.png)

111、            关于    slice  splice  substr  substring  split   replace  concat

   

  spilce:  

![](images/WEBRESOURCEfc9feeb103e09753117607918075f456截图.png)

slice:     

   

![](images/WEBRESOURCE935dce940dc2329cdd29daa455a5d895截图.png)

(substr （即将废弃的属性）   /      substring )  

slice   下标为0开始， 到下标为几， 最后一个不取   var str = "abcdefghij";   str.slice(1,4)       //  从下标1开始，取3个     bcd  

substr （即将废弃的属性） begin 起始位置 也是从 0 开始 算起  第一个位置为0，取几个  var str = "abcdefghij";   str.substr(1,4)       //  从位置1开始，取4个                          bcde

substring  strat开始下标 也是从 0 开始 算起 第一个下标为0， 到下标为几的前一个   var str = "abcdefghij";   str.substring(1,4)  // 从下标1开始，到下标3（下标4的前一个） bcd

![](images/WEBRESOURCE7436f36a02caec85563c50a637f37df9截图.png)

![](images/WEBRESOURCE5d0b328e9fe5b1ba2eeb7924dcb43760截图.png)

![](images/WEBRESOURCE1d59ca493f28a0148ed2670bdf8f9f7b截图.png)

112、  父组件请求接口返回数据后，赋值给data中的属性然后传给子组件，子组件 刷新页面，在 created 生命周期中打印就会获取不到，而在template模板中却可以获取的到！

    与 父子组件生命周期的触发顺序 有关    又是要 watch ？ 

113、  既有 key  ， 又有 value  ， 只要有出现map/对象/{}  的低昂，都要有敏感的反应用 map去映射 ！        data:  [  {  name: '', label: { key: ' Back', val: '后退 '  } }  ]     

 { key: ' Back', val: '后退 '  }    页面只要 val,  而接口只要 key， 这部很明显 直接定义一个map 完事了吗

KEY_VAL_MAP() {

   return {

     Back: '后退'

  }

}

页面取的时候直接 KEY_VAL_MAP [ xxx ] ， xxx  就是接口对应的变量了   !!!

114、  状态值  boolean - true - false

当  **事件/内容**** ** 有冲突/需要按条件显示 时，当找不到已知的存在的判断对象的时候，记得想起来 自己定义一个 状态值变量 进行判断 ！！！！ 

115、     灵活

116、  Array.prototype.fill()

![](images/WEBRESOURCE18d0327b323b612243175972c8339975截图.png)

117、  Vue.$set （ this.$set ） 的 使用场景 ？？？

（1）、     在 data 中 没有定义的初始化的字段，然后后面在 template 中直接用到 xxx.该字段  /  页面初始化 对该字段操作  /  页面保存对该字段操作 的时候，

                  响应式系统监测不到，这时候就要用 this.$set(obj(object), targetProperty(string), value) 将该字段加入响应式系统中。

（2）、     在 data 中 定义了该字段， 但是初始化的时候将包含该字段的对象在请求接口后做了重新赋值，但是接口又没有该字段， 等于重新操作后原先包含该字段的对象现在又没有了该字段，

                  所以这个时候又要重新赋值给该字段对应的值， 所以这时候就要用 this.$set(obj(object), targetProperty(string), value) 将该字段加入响应式系统中。

118、    toLocaleString()   返回特定语言环境下的字符串格式

 e.g.  保留千分符位数：

![](images/WEBRESOURCE0e54ff3197675f753933b3292eb58fca截图.png)

![](images/WEBRESOURCE7486ee16aa404f0e3b3c6be9b02eb968截图.png)

e.g. new Date()使用 toLocaleString()

![](images/WEBRESOURCE523ba333fdbd4153f3aa38e7b8be8c80截图.png)

直接 toString()

![](images/WEBRESOURCEdb4b952621aa866d72db15eb5f42f563截图.png)

119、  OSS  对象存储服务     Object  Storage  Service     海量/可靠/安全/低成本 的 云存储服务 。 适合存放任意类型的文件。

120、 深拷贝 JSON.stringfy() 的 缺点  

（1）、JSON.stringify 的 三个参数  （**数据，过滤，缩进**）

         （**object,   ****Array | function****,   number | string**）

第二个参数，过滤用 ：

![](images/WEBRESOURCE3a6b6d8a7215a99ba987a709a9b13d97截图.png)

是 function 时， 接收两个参数 function (key, value) { } 

第三个参数，用于缩进（默认是4）。**字符串会以该字符向前填充，数值则按照tab键个数填充**

![](images/WEBRESOURCE220677337213192fbbdda659341e3153截图.png)

（2）、JSON.parse（text, [reviver]）

第二个参数可以是函数，修改原数据。

![](images/WEBRESOURCE2a80ebc316921818f33a162e9c932e12截图.png)

JSON.parse + JSON.stringify 实现深拷贝

const origin = { name: 'MDN' }  

const deepCopy = JSON.parse(JSON.stringify(origin))   

deepCopy  //  { name: 'MDN' } 

![](images/WEBRESOURCE476e9763470674ed855a34e167c87065截图.png)

![](images/WEBRESOURCE896a5dd91ad44f777bfbe4c645e769d2截图.png)

![](images/WEBRESOURCEcc536954a4509b480695ff2ae8e9d025截图.png)

![](images/WEBRESOURCEa1c00379cdc94eb902e24c1682cf0251截图.png)

最好就是自己实现一个深拷贝（开销最小最安全）     深克隆 数组 / 对象  是 一样的  （相同的功能）

![](images/WEBRESOURCEd5e11c44f0d5e1942424f9438b820a15截图.png)

![](images/WEBRESOURCEa2c1532085bf4dbd5be746c3a911a15c截图.png)

  

121、 用 computed 而不是 watch ？

父组件传给子组件的props，子组件中显示该 props 变化前后的值 （变化前即是 默认传过来的 default 值， 变化后即是 父组件中改变了该值），

如果直接 watch （newVal, OldVal） 该 props 然后 子组件中直接赋值 data 中的 xxx 为 OldVal ，则无法达到预期效果 （因为watch无缓存，

当父组件传的值变化时， 子组件中直接watch到的该props中的newVal 是 ===  OldVal的），所以要在 computed 中 先缓存默认传过来的props，

然后 watch 的是 对应的 computed 值，这样的话 OldVal、newVal 就都拿得到了。

子组件 ：

![](images/WEBRESOURCE7340f46714e253cf8aec2acb59edf79b截图.png)

![](images/WEBRESOURCE3ac608ad3efeeeee943999ddae8f3a2d截图.png)

![](images/WEBRESOURCEa56d99558874a2f3866fcbe63372ba05截图.png)

![](images/WEBRESOURCE1930714b34bf5a3687ca077f117b5221截图.png)

watch  的 deep 属性  在  对象 嵌套 的层级 很深 的情况下 就需要 开启 （深度监听）了

123、  过滤器不能直接用  ||    

这样不行的话 ：

![](images/WEBRESOURCE6569347fa0004edd2e88ba7cd8644038截图.png)

那就换种写法：

![](images/WEBRESOURCE45cded549582dadebdfed62d2f991e72截图.png)

124、 HTTP 常用状态码 ：  200 300 400 500

![](images/WEBRESOURCE66108676e41a8a9508cbf63e3c92f439截图.png)

125、 标准盒模型  和  怪异盒模型  （IE盒模型）border-box

怪异的 content 包括 border + padding  

标准的话 content 就是 content （不会包括其他的）

box-sizing: border-box;  怪异盒模型

box-sizing: content-box;   标准盒模型

box-sizing: inherit;  继承父元素的 box-sizing

126、  **  越简单，改动越小，越是正确改bug的方式  **

**          越迷惑（感到难以解决）的问题，解决方式往往是 越简单 的！**

**           是不需要去想那么复杂的。**

127、el-dialog 中 使用** iframe** （内联标签）会因没有 设置固定的宽高而 整个iframe 元素变大       要有固定（写死）的宽高

正常使用 iframe 标签 ，直接 整个 src 和 frameborder="0"  就行了，然后就要么 iframe width 和 height 都 100% 继承 来自父元素（固定宽高）的宽和高。 

跨域报错：

![](images/WEBRESOURCE96a18bf7c2452d01d4327242bc73dc71截图.png)

![](images/WEBRESOURCEac698e0ef6ddf3467308ddb978306f81截图.png)

**X-Frame-Options** （[HTTP](https://developer.mozilla.org/zh-CN/docs/Web/HTTP) 响应头）用来告知浏览器该网页（iframe的src）是否可以放在 iframe 中

常见属性：

 deny  ---  不允许

sameorigin ---  可以在相同域名页面的 frame 中展示

allow-from xxx ( 例如 https://www.baidu.com ) ---  iframe 只能放在 www.baidu.com 这个域名下

allowall ---  允许所有站点内嵌

![](images/WEBRESOURCEba74435ad11eb723f7062af1e2780209截图.png)

![](images/WEBRESOURCE7af461560ce9aad54746589a2308d151截图.png)

常见的就是在 nginx 配置 

![](images/WEBRESOURCE704174cf5ff18f800135a673bd1009c2截图.png)

没有给 el-dialog 设置宽高前 ： 

更改前： 

![](images/WEBRESOURCEba830c53a0dba3c70e803385ea6711af截图.png)

![](images/WEBRESOURCE799a05e0caf0340477304ec83eca19b5截图.png)

![](images/WEBRESOURCEb39657ffa88cdd69d67566061f9d58ce截图.png)

根本原因是 

把 el-dialog 单独放到父元素上面， 不要 抽成一个组件（页面），让其父元素的宽高固定， 就不会有那样的问题了 !

 更改后：

![](images/WEBRESOURCE2f9a1731c1260e798e632739a9462a32截图.png)

![](images/WEBRESOURCE5a1722159139bb324805aa9f798ebbcf截图.png)

![](images/WEBRESOURCE21a3270added8955c6cccdca40db8cad截图.png)

![](images/WEBRESOURCEb4de1707d0cfaa092f8ecb6345305b6c截图.png)

128、   用户体验友好

129、 CSS 设置文字溢出省略号显示的固定搭配 ： 

 

条件： 需要包含文字的盒子的宽高是已知（固定）的

overflow: overlay  和  overflow: scroll  区别：

![](images/WEBRESOURCE758633ffddcbc773beefb571f982b441截图.png)

  overflow: hidden;

  text-overflow: ellipsis; *// 文字溢出省略号*

  display: -webkit-box; *// 必要搭配*

  -webkit-line-clamp: 1; *// 显示的文本行数*

  -webkit-box-orient: vertical; *// 必要搭配  盒对象子元素排列方式*

![](images/WEBRESOURCE703bb14d5218abb0d3518f7063c73e7b截图.png)

130、 连续调两次接口，第一次达不到数据怎么解决？   

           用 async / await  异步处理

131、     CONCAT   concat   数组连接数组的方法  别给我拼错了  ！！！ 

132、  ** 数据格式转换 2**

1）、已知数据格式： 

const gradeList = [

    { grade1: '一年级' },

    { grade2: '二年级' },

    { grade3: '三年级' }

]

const studentList = [

    { grade1: ['小明', '小张', '小赵'] },

    { grade2: ['张三', '李四', '王五'] },

    { grade3: ['tom', 'lilei', 'mary'] }

]

2）、目标数据格式：

students = [

  { name: '小明', grade: '一年级' },

  { name: '小张', grade: '一年级' },

  ...

]

3）、格式处理 ：

封装个函数： 用到三层循环 

const students = (gradeInfos, studentInfos) => {

  let targets = []

  for (let i = 0; i < studentInfos.length; i++) {

     for (const j in gradeInfos[i]) {     //  这里是重点 （找得到这点规律，基本就破解了）

       const mapList = studentInfos[i][j].forEach(item => {

         targets.push({

           name: item,

           grade: gradeInfos[i][j]

         })

       })

     }

  }

  return targets

}

students(gradeList, studentList)

![](images/WEBRESOURCE472b79c7d92ca2e47a390872e8ec1b48截图.png)

查找字符串中每个字符出现的次数 ： 

![](images/WEBRESOURCEbd87ddfe67daecc6b91d518eae1a93db截图.png)

133、   el-date-picker  只支持 指定格式的字符串， 不在指定范围的话 是显示不出来 （或者显示错误的） 

![](images/WEBRESOURCE44b6c88104fd51041d8f781e3bafefbe截图.png)

134、 无后缀名 图片 查 后缀格式 ： 

![](images/WEBRESOURCE4b6a15735fdb6c7afe799ecb3444781f截图.png)

135、  更新视图方法   

this.$set()

this.$update()

this.$nextTick()

setTimeout()

136、 两个数组中删除相同项目： 

![](images/WEBRESOURCE30b9ea8930e46c6b269d64a001ec5916截图.png)

137、 饿了么 的 rules 属性 有 required 、 message 、 trigger 、 type ...   等

138、   常见布局： 百分比（流式）布局   rem   flex          响应式      双栏/三栏布局   双飞翼/圣杯 布局     

             百分比布局 + flex  可以很常见的适配  登录页 的布局  （px都用%进行替换 ！）

139、    以下  ：：  基础 ： 

![](images/WEBRESOURCE101ba0877d6c04b6978207cc0c9c2e2c截图.png)

140、    真相大白了！！！！！！！   原因：     初始化的时候赋值有问题导致的报错 

![](images/WEBRESOURCE807b26c2eee0cbf7aad894718f0d3568截图.png)

![](images/WEBRESOURCE6f186f9b100806b47c84a8e59553fde3截图.png)

141、  套娃函数  自身调用不会执行 ！！

 

![](images/WEBRESOURCE1dc527dfc95d1803e0ce08d31cf641ea截图.png)

![](images/WEBRESOURCE64865606ccf1086d802e17bf702ba055截图.png)

142、   版本回退，但是不能提交 (git push)

![](images/WEBRESOURCE4d4b81388a6522950360fcba1a8376ec截图.png)

143、 keep-alive 组件 

![](images/WEBRESOURCE316ceef71e961c27c2df404dec52e7f6截图.png)

<keep-alive>   （该组件主要使用 LRU 算法的缓存机制）   包裹动态组件时，会缓存不活动的组件实例，而不是销毁它们，防止重复渲染DOM。和 <transition> 相似，<keep-alive> 是一个抽象组件：它自身不会渲染一个 DOM 元素，也不会出现在组件的父组件链中。

当组件在 <keep-alive> 内被切换，它的 activated 和 deactivated 这两个生命周期钩子函数将会被对应执行。

在 2.2.0 及其更高版本中，activated 和 deactivated 将会在 <keep-alive> 树内的所有嵌套组件中触发。

主要用于保留组件状态或避免重新渲染。

 

![](images/WEBRESOURCE552297a3d015963adc530fe03e87e621截图.png)

当组件第一次创建的时候 

activated 方法是在 

mounted 方法之后执行。

当页面被隐藏的时候会触发当前页面的 

deactivated 方法

当前vnode 节点被销毁的时候，会判断当前节点是不是有 

keepAlive 标记，有的话就不会直接调用组件的 

destroyed 了，而是直接调用组件的 

deactivated 方法。

LRU （ least recently used ） 最近最久未使用 ---  最近最少使用

是 常用的 页面置换算法 中的一种

![](images/WEBRESOURCE9d136699365a75c7e2c3e3c4a078db11截图.png)

keep-alive 包裹下的 router-view 中的 **key ** 属性 （取值： $route.path  ----  只有 path 路径    /    $route.fullPath  ----  带query内容）

![](images/WEBRESOURCE74e423a54ba7eb39bd350f06dfa4944e截图.png)

（一般都是 transition  --->   keep-alive   --->   router-view   这三个组件连着 使用的）

![](images/WEBRESOURCE91b436dba968a261c88702926174d149截图.png)

![](images/WEBRESOURCE23c294997b13ec7316d5a17e6c04919d截图.png)

![](images/WEBRESOURCE553da244601500dfc4af90549a06efce截图.png)

![](images/WEBRESOURCE7388c75ba2b3c3fab17d4e20fed50a99截图.png)

**router-view 设置了 key 的缺点:**

加了路由的key值，Vue就会认为这不是同一个组件，update的时候会删除这个组件再重新加载一个新的组件，有严重的性能问题。

#####   **Vue  的  key  属性**：   key 的 type 是  String | Number

![](images/WEBRESOURCEed231c81873216c3e86bf935fe1aee96截图.png)

![](images/WEBRESOURCE526499c864877211084f82503b96b6d2截图.png)

![](images/WEBRESOURCE95a700717e5400312b8e02228dd3639c截图.png)




144、   完整的后台模板，自己按照命令整一套 

1)、引入piui不香吗，按规范来

vue create -p sadais-org/uni-preset-vue my-project

2)、管理后台模板  工程创建指令

vue create -p sadais-org/sadais-admin-preset-vue my-project

![](images/WEBRESOURCE7e628ff1f8044cb0d7d57999efa75fac截图.png)

145、  管理后台权限管理（系统设置）： 

一般都是有 角色、用户（账号）、资源 这三部分组成

![](images/WEBRESOURCE36cd53efd317e51aa30866a4f5defe97截图.png)

然后是 先给角色分配资源（权限），然后再给用户分配角色，资源控制路由菜单的显示内容及顺序

![](images/WEBRESOURCE1ab10380ff51b77e283a0298fbfebb7b截图.png)

![](images/WEBRESOURCE599e9d01cffe08bcd998cb53bf474800截图.png)

![](images/WEBRESOURCEd28cf265bb8186de9c92733516f3bfa8截图.png)

![](images/WEBRESOURCE0a347b8da64a1c6ca9290b8f7cf41066截图.png)

146、 beforeRouter  导航守卫中的 next() 回调函数属性

next() 直接放行，跳到下一个路由

next('/login') 或  next({ path: '/login' })  强制跳转到 login路由的页面

![](images/WEBRESOURCEef34f7100c19c8bb49c9ae79065fea28截图.png)

![](images/WEBRESOURCEdfaccf0cd8a435858bcf4a51dcd948a9截图.png)

147、  el-table-column 的 sortable 属性   （**根据prop设置的字段进行排序的**）

![](images/WEBRESOURCE472e4ceb2d2e9d526f2581435a1f20f8截图.png)

![](images/WEBRESOURCE006e7d91fb843b7b118438818e1ba5e3截图.png)

148、 git 代码回滚/撤销 

revert ： 恢复/还原           refolg

![](images/WEBRESOURCEe81d65c18ff29201ec5e1762d79e8f91截图.png)

git reflog 查看 commit 操作历史 ：         reflog 查看所有分支 （包括被删除的commit 和 reset的操作）

![](images/WEBRESOURCE235ad52218bf87b2914bdf226cbed8ed截图.png)

fast-forward（快进）合并

![](images/WEBRESOURCE5a7883e7cb66c659c4e00f0b70affa37截图.png)

git log:                                             log 查看已有的提交记录 （被删除的查不出）       更详细

手动修改提交记录的信息和时间， git log 认你修改的那个记录， 但是远程仓库里应该还是你之前的那个提交。

![](images/WEBRESOURCE8fb5cac362af073e17ad470998861c60截图.png)

![](images/WEBRESOURCE4bdde1751c3763dec7f7e1c0d23d26e9截图.png)

149、 图片地址     src地址   的  require    可 动态 .

用 原生  img  标签  : 

![](images/WEBRESOURCE1f2345faef169441f3b0dd4523252c68截图.png)

150、   浅拷贝  ？？？      操作 params 对象  不会影响 this.form 对象 ！     **（改变的是对象里面的对象里面的值，就有影响了）**

0)、改变了第二层（及以上）就会有影响

![](images/WEBRESOURCEe55d9dbba6baaa6895d417f16a2175a4截图.png)

1)、

![](images/WEBRESOURCE43d2175ee3c7e30c670524375e824620截图.png)

![](images/WEBRESOURCEced9dbf3c3df125c33357517917206d3截图.png)

2）、

![](images/WEBRESOURCEb9f88b24d8fed9a4ad217fe2f5762318截图.png)

![](images/WEBRESOURCEbcbc1affa4d8eea7fd7773a25c6daadf截图.png)