vue1.x  --->  vue2.x    引入虚拟DOM

vue2.x  ---->  vue3.x    **更好的ts支持**、**废弃一些为了兼容保留的API**、**数据量大带来的渲染更新问题**（性能消耗）、**更好的编程体验**（逻辑相关的写一起，而不用三足鼎立跳来跳去）

# **Part 1**

# 一、源码优化

monorepo   +     typescript   （flow 类型支持 不友好，复杂类型推导有问题）

reactivity  可单独于 vuejs 使用，  可以实现脱离框架本身，只体验其响应式功能

vue2.x:

![](images/WEBRESOURCE99d53947c59fe5278fc49641781e768f截图.png)

vue3.x:

![](images/WEBRESOURCE89c3dae05aa995881dd8b3b66b573e5d截图.png)

# 二、性能优化

减少源码包体积（网络请求减少，js引擎解析包速度越快）  优化静态资源体积

**移除一些冷门的 feature **   +     **引入 tree-shaking 的技术 **

**tree-shaking：**依赖 es6 的 import 和 export ,  通过编译阶段的静态分析， 找到没有引入的模块并打上标记

utils 模块里定义了 没有用到的 export（模块外没有 import 过），webpack 打包时会一起打包的（未被引入会给打上标记），然后在压缩阶段会利用一些库像 uglifyjs / terser-webpack-plugin 进行删除

如果项目中没有引入 Transition、KeepAlive 等组件，那么他们对应的代码就不会打包。这样也间接达到了减少项目引入的vue.js包体积的目的

# **三、数据劫持优化**

实现DOM功能，必须劫持数据的访问和更新

当数据改变后，为了自动更新DOM，必须劫持数据的更新 （数据发生改变后能自动执行一些代码去更新DOM）

vue1.x、vue2.x：  删除( $delete ) /  添加( $set ) 无法监听 （() 为 vue 提供的解决方案）  **对于深层次的对象需要递归回去到内层属性进行响应式的设置（影响性能）  { a: b: { c: { d: 1 } } }**

**Object.defineProperty(obj, 'key', {**

**  set() {**

**    **//  trigger

**  }**

**  get() {														//     劫持的是一个key，（对象的一个属性） 自然无法检测到 新增的 key；  也不能删除自己（只是监听setter 和 getter）**

**    **//  track

**  }**

**})**

vue3.x：Proxy API

**observed = new Proxy(data, {**

**  get() {**

**    //  **

**  },**

** set() {													     //     劫持的是一整个对象，自然对象中的属性删除和新增都可以监听到  **（不早点用是因为有兼容性问题，现在不考虑IE了，所以无所吊谓，随便用）

**   //   **

** }														     //     ****!!!  但是只能监听对象的属性（一层）  ****如果属性又是嵌套的对象，那这个嵌套的对象就监听不了了    解决：是 ****在  getter中 递归响应式**

**})														     //     这样好处是在真正访问到内部对象的时候才去递归变成响应式  大大提升了性能**

# **四、编译优化**

![](images/WEBRESOURCE0ffc983aae0653694f12f0e9072efd1c截图.png)

响应式过程在 init 阶段 

**在运行时 patch 阶段 优化**

vue3 diff  **block tree    /           slot 编译优化     **重写 diff 算法

# 五、语法API优化： Composition API

**options api **：逻辑分散，相关功能代码一分为三，不集中 （data, methods, computed ...） **|**   mixin 逻辑复用 （命名冲突 数据来源不明）

**composition api **：将某个逻辑关注点的相关代码全都放在一个函数中  **|**  hook 函数  逻辑复用  **|**  更好的类型支持（函数导出知道类型，options 只能用 this）  

# Part 2

## 核心组件实现

组件渲染过程：

1、 创建 vnode

2、 渲染 vnode

3、 生成 DOM