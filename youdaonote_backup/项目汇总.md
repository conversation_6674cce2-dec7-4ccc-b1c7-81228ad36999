![](images/WEBRESOURCE8065c9cc416058569abba787a3d7e1e7截图.png)

- 路由 路由 路由

(一)、路由注册/使用

1. 前端路由： 映射表 - url （路由path） 和 spa页面 （组件路径component） 的对应关系 （映射关系）       

                           改变url页面不刷新  （ location.hash / history.pushState({}, '', url) ）

                                                               history.replaceState({}, '', url) 的话  浏览器不能前进后退

1. 阶段：         后端路由（服务端渲染）- 前后端分离（ajax不刷新页面请求，输入url地址返回静态资源服务器文件，api请求返回接口服务器文件）- 前端路由（spa 单 html文件 页面， 根据 path 去 与 组件的 路径 进行 匹配，显示对应的内容）

1. 使用：装包 - 安装 - 使用

![](images/WEBRESOURCE440135ab39bb6a628448cf1a85ceed22截图.png)

![](images/WEBRESOURCEfedbfda8d8fb31be382859497758cb58截图.png)

![](images/WEBRESOURCE458985b5602489358433c012bf500da3截图.png)

![](images/WEBRESOURCE26a8b41d013ff456db0f6ac156fe3973截图.png)

![](images/WEBRESOURCE16a7e0fff26b150ac10c347f0d1a2918截图.png)

path 必须带 /  不然会 显示不出来 

![](images/WEBRESOURCE1eea0b7f73a961331aee077bf72324ee截图.png)

不带/ 的警告

![](images/WEBRESOURCE4e37789c9aca043cf74bcb80121dea9a截图.png)

路由的默认路径（缺省值）：

```
{
    path: '', // 默认路由（缺省值 '' 或 '/'）第一次进去显示的路由
    redirect: '/hello-world'
  },
```

 