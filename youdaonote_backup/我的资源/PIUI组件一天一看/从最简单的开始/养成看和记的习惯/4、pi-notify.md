1、uView 该组件 通过 css3 的  opacity + transform: translateY(-100%);  和 transform: translateY(0);  来控制 topTip 的隐藏和显示，



![](images/WEBRESOURCE98e6aa08f5da1e8be50ba2da857349de截图.png)



![](images/WEBRESOURCEce8dd248e9a63c22aac0a01b93fdeac9截图.png)

使用自定义的导航栏可能会出现兼容问题，需要单独再传 navbar-height  属性。

```javascript
<template>
	<view class="wrap">
		<u-navbar title="文章列表"></u-navbar>
		<u-top-tips ref="uTips" :navbar-height="statusBarHeight + navbarHeight"></u-top-tips>
		<u-button @click="showTips">弹出Tips</u-button>
	</view>
</template>
```

我直接简单粗暴  通过 show 来显示，直接把他那个通过css控制的给去了



有一个不好的地方，用起来不太方便，就是每次都得设置 ref 和 this.$refs.notify.showNotify() 才能使用这个消息通知组件，用起来不爽





2、该组件最好挂全局上，使用直接 this.$notify  更好



3、演示案例一开始用的是pi-list 布局，后面改了button那样的演示模式，感觉可能交互和显示会更友好些



4、没有提供事件回调的方法 