1、vue中怎么防止事件冒泡？有什么应用场景？

通过添加事件修饰符.stop阻止单击事件的向上传播。

事件冒泡：防止父元素具有与子元素相同的事件（父子元素都具有点击事件/父子元素都具有失去焦点事件等...）的时候，子元素触发会冒泡到父元素身上，影响预期想要输出的内容。

例子：一个页面中本来是点击父盒子才会显示或隐藏某些内容，但是子盒子又有其他相同的事件，这时点击子盒子的具有相同事件的标签也会触发父盒子的事件，达不到预期的效果。这时候就可以给子盒子具有该事件的标签加上.stop阻止冒泡行为，保证只有在点击父盒子的时候才会触发该事件。（还要有再具体一点的业务场景？）



2、v-if 和 v-show 的区别？ 又要显示该元素，保留原有位置，还有什么方法？

v-if 和 v-show 都会导致重绘和重排。 然后v-if是通过添加或删除DOM元素实现对元素的显示与隐藏，而v-show则是通过给元素添加css元素display设置为none或者block来实现对元素的显示或隐藏。所以在需要频繁的切换元素的显示与隐藏的话使用v-show可以减少频繁的操作DOM，影响性能；如果是某个元素在渲染后就一直存在或隐藏的话使用v-if。 使用CSS的 visibility: visible 属性可以实现对元素的显示，但是该属性是占有原来的位置的。



3、计算属性和方法的 区别？

计算属性的话是会进行缓存的，就是基于该计算属性的相关依赖即data() 函数中的数据不变的话缓存时会一直存在的，每次用的时候直接就是在缓存中取；而方法是不存在缓存的，每次调用的时候都会重新调用该方法，如果是数据多的话对性能会造成影响。所以在template中显示数据的话一般都是用计算属性，用方法的话会效率低，而且会影响页面响应，造成性能消耗。



4、讲一下vue的响应式原理。具体是如何实现的？举个例子（什么场景下怎么实现的响应式）说明下。  |   还得引导下才知道要往哪个方向去回答问题

vue具有MVVM框架思想，即当视图层页面发生变化的时候，会实时更新模型层中的数据；当模型层中的数据发生变化时，视图页面中显示的内容也会跟着变化。视图层相当于vue中的template中的内容，模型层相当于data()函数中的数据，而视图模型相当于控制器。当页面上出现事件改变的时候，会在vue的内部相当于new了一个Vue()对象，创建了一个vue实例，vm层监听到该变化会通知到methods中对应的方法，通过this.xxx会改变模型层中的数据，这样就实现了视图层变化更新模型层数据的过程；而当模型层中的data数据发生变化的时候，vm层监听到该变化会通知视图层中通过双花括号使用到的该数据发生更新，这样就实现了模型层变化双向绑定视图层中的内容。

深入响应式原理：利用了Object.defineProperty()中的getter和setter方法，和设计模式中的观察者模式。





![](images/WEBRESOURCEb72da86afaeb9060a52bf4a19b0df8dc截图.png)

object.defineProperty()  定义新属性 / 修改原有属性：



语法： Object.defineProperty(obj, prop, descriptor) 

obj：要定义属性的对象  

prop: 要定义或修改的属性的名称  

descriptor：要定义或修改的属性描述符，参数如下：

(1、value: 设置属性的值


(2、writable: 值是否可以重写。true | false


(3、enumerable: 目标属性是否可以被枚举。true | false


(4、configurable: 目标属性是否可以被删除或是否可以再次修改特性 true | false



set/get：属性中的setter和getter函数

当使用了getter或setter方法，不允许使用writable和value这两个属性

```javascript
// 一、
var obj = {
    name: 'zhang'
}
Object.defineProperty(obj, "name", {
  enumerable: false,
  configurable: false,
  writable: false,
  value: "wang",
});

// 二、
var obj = {};
var initValue = 'hello';
Object.defineProperty(obj,"newKey",{
    get:function (){
        //当获取值的时候触发的函数
        return initValue;    
    },
    set:function (value){
        //当设置值的时候触发的函数,设置的新值通过参数value拿到
        initValue = value;
    }
});
//获取值
console.log( obj.newKey );  //hello

//设置值
obj.newKey = 'change value'; // 相当于执行了setter方法

console.log( obj.newKey ); //change value
```



响应式场景：

1）、view变model变：比如一个input输入框，当你输入内容时，v-model中的:value会动态绑定你输入的值，然后vm层通过输入框的input事件监听输入内容的改变，然后通知到data选项中的数据发生相应的改变，实现一个页面到数据的绑定。

2）、model变view变：在data中预先声明好了一个对象numObj的a: 1,b: 2,c: 3三个属性，然后从接口返回来了a,b,c,d有四个，最后的d是没有被加到响应式系统的。除了this.xxx可以获取到该属性，还可以通过  Vue.set(object, propertyName, value)  方法或者this.$set 方法添加新属性或改变已有属性，Vue.set(this.numObj, 'd', 4)，将其加入响应式系统中。



如何追踪变化？

【      当把一个普通的 JavaScript 对象传入 Vue 实例作为 data 选项，Vue 将遍历此对象所有的 property，并使用 Object.defineProperty 把这些 property 全部转为 getter/setter。这些 getter/setter 对用户来说不可见，但是在内部它们让 Vue 能够追踪依赖，在 property 被访问和修改时通知变更。每个组件实例都对应一个 watcher 实例，它会在组件渲染的过程中把“接触”过的数据 property 记录为依赖。之后当依赖项的 setter 触发时，会通知 watcher，从而使它关联的组件重新渲染。    】



5、vue中删除数组的方法。

pop()：不带参数，删除数组最后一个，返回被删除的元素，一次只能删一个

shift()：不带参数，删除数组第一个，返回被删除的元素，一次只能删一个

spilce(whichOne, num, replaceVal)：删除数组，有3个参数，第一个是要删除的位置（从第几个开始），第二个是删除的个数，第三个是要替换或插入的元素（当第二个是0的时候）。返回被删除元素的数组，会影响原数组。



```javascript
// 插入
arr.splice(2, 0 , 'A', 'B')   // 在第二个元素后面插入元素A和B
```



```javascript
// 替换
arr.splice(2, 3, 'E', 'F', 'G')  // 把第二个元素后面的3个元素都删除，然后再插入E,F,G 
                                 // 替换第二个元素后面的3个为E, F, G 
```

6、说一下vue的生命周期，然后具体每一个生命周期创建都干了啥？

vue的生命周期表现为vue实现从开始创建到挂载，更新，销毁的一系列过程。其中会基尼这么几个生命周期。一开始是beforeCreate实例创建，data和methods和页面DOM还没初始化，一般这里不做操作。接着是created，实例化创建后该生命周期钩子函数立即被调用，data和methods此时可以使用，但是页面还没渲染出来；然后就是beforeMount，是在挂载之前被调用，此时页面看不到真实数据。接着就是mounted，数据已经真实的渲染到页面上了，el被新创建的vm.$el所替换，并挂载到实例上去之后调用该钩子（template挂载到DOM之后回调）；然后就是beforeUpdate，数据更新之前调用，页面上还是旧的数据。接着就是updated，页面上的数据已经替换成最新的（当界面发生刷新的时候回调）；然后就是beforeDestory在实例销毁之前调用，这里vue实例还是存在的，可以在这清除定时器等回调函数的触发。接着就是destoryed，实例销毁之后会调用。

可以按照官网自己画图过一遍流程图，然后自己写DEMO验证这些知识点，体验一下什么时候出来哪一个生命周期。多对应用场景做下扩充。知其然，也要知其所以然，才会用。百变不离其中。



