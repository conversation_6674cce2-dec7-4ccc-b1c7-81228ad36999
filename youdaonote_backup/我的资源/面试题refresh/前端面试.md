

1. 使用vue实现弹窗组件，说说你的详细实现思路

技术要点：

1. 遮罩层实现，屏蔽用户手势及背景滚动

1. fixed布局以及层级方面知识点考察

1. 弹窗水平/垂直方向居中的实现

1. slot方面考察（title slot, body slot, footer slot）

1. 实现细腻度（点击遮罩层可关闭，H5中可以append to body中避免浏览器兼容性影响）

1. 解释下1px、1rem、1em、1vh代表的含义分别是什么？

1px

像素（Pixel），相对于显示器屏幕分辨率而言的，即一个像素点。

1rem

相对于根元素HTML，即同根元素font-size大小。

1em

相对于父元素

1vh

相对于视口/视窗，即可视区域

1. 请使用css实现以下效果

         



![](images/WEBRESOURCE69f333ef95b5e121de657e53dd594881截图.png)

        

技术要点：

1. 伪元素的考察

1. css transform 的考察

1. 加分：考察其是否能用一个伪元素去实现该效果

1. 题目：合计考生总分数（重复分数以最后一次为准）

* 请以你觉得最优雅的方式去实现



```javascript
const users = [
    {name: 'zhangsan', score: 99},
    {name: 'lisi', score: 78},
    {name: 'zhangsan', score: 93},
    {name: 'wangwu', score: 72},
    {name: 'wangwu', score: 88}
]

```

自己实现的方法（花了小两个钟......）

![](images/WEBRESOURCE451b6599906b49353f60050d7552c81b截图.png)





1. vue中data与computed的区别，说说你的理解

不需要改动的属性可以放计算属性中（也可以放data中，使用Object.freeze冻结）

计算属性会进行缓存，当其依赖的data没有变化的时候，直接输出结果，不需要经过计算

1.  你是如何在工作中积累知识的？有哪些方法？