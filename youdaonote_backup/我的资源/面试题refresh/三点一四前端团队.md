

1. 团队结构


| 主管<br> | 张振飞<br> | <br> | <br> |
| - | - | - | - |
| 高级工程师<br> | 刘伟涛<br> | 付文龙<br> | <br> |
| 中级工程师<br> | 郑一帆<br> | 张创祺<br> | 林柏全<br> |
| 初级工程师<br> | 庄宏斌<br> | 刘永红<br> | 周贤攀<br> |





1. 岗位职责


### 初级前端


1.  了解项目背景，参与项目/产品开发，按照要求完成功能


1.  熟读开发规范以及工作手册，养成良好的编码习惯


1.  每天下班前更新项目进度，并主动上报开发过程中遇到的问题点


1.  熟练使用ui框架开发页面，在使用的过程中反馈问题


1.  提交代码前进行codereview，确保无明显问题


1.  前端基础知识持续学习，每月进行知识总结，输出一份语雀文档



### 中级前端


1.  负责项目初始化，需求的梳理，并初始化项目


1.  对设计图进行公用组件的抽取和实现，定义组件的属性（输入输出）标准化


1.  审核code review，产出评审报告


1.  参与前端基建，负责技术难点的攻克


1.  每个月至少出一个PiUI官方模板



### 高级前端


1.  担任项目负责人角色，管理团队&项目，对开发任务进行分解


1.  负责前端基建的项目规划以及版本迭代


1.  负责项目技术评审


1.  负责团队的沟通协作


1.  每次版本迭代结束后，应该组织复盘总结会，总结成功经验，吸取失败教训，有助于提升团队能力



### 前端主管


1.  负责团队成员的能力成长，能力成长规划


1.  负责前端团队的能力建设，文化建设，技术建设 


1.  团队成员的职级评定（能力评定基准标准）以及晋升考核


1.  负责前端技术路线的规划，研究，培训以及落地计划


1.  负责前端技术专栏的定期培训


1.  负责前端相关规范的制定 


1.  对员工转正进行考核（制定考核标准）



1. 能力提升


张振飞：郑一帆，张创祺


付文龙：林柏全，刘永红，庄宏斌


刘伟涛：周贤攀





1. 有规划，有要求，能落地的方案制定


1. 平时更多的沟通交流，良好的互动


1. 每个季度的访谈





2022 年目标


刘永红：初 -> 中


周贤攀： 初 -> 中


林柏全： 中 -> 高


郑一帆： 中 -> 高