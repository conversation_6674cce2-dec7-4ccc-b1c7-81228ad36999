# 前言



不知不觉一个月已经过去了，回想这一个月的实习工作情况，有成长，也有做的不好的地方。



以下从主要工作内容、技术的学习过程、知识的积累、对项目的整体认知、未来的工作学习计划等几个方面进行一个梳理。



- 主要工作内容

在工作上主要完成了熟悉语雀文档的代码规范，智能引导管理后台bug的修复和中心新闻页面的新增，

PIUI组件文档和案例的编写，基层公共服务支撑平台首页的编写。具体的工作内容可分为以下：



1. 第一周

① 熟悉智能引导终端系统预约取号相关页面的组件代码；



② 熟悉智能终端刷取身份证和手动输入号码组件代码，增加选择号码组件页面；



③ 修改选择号码组件页面可滚动样式，了解代码推送规范，根据PIUI组件代码编写倒计



 时组件的.md实例；



④ 根据PIUI中动画相关SCSS代码编写piui-awesome中pages中的mask组件相关的.vue



页面的动画实例和piui-docs中Mak组件的.md实例；



⑤ 继续完善实例并提交，修复若依管理系统微信预约管理记录页面选择预约日期搜索无



 效，选择预约办理时段和选择事项分类搜索无效的小bug；





2. 第二周

① 设备管理页面加了个解绑的功能，其他页面一些字段属性的修改和添加。列表加搜索条件（listType）；



② 完善解绑功能，其他页面的bug修复（一些字段属性的修改和添加）；



③ 叫号管理的叫号监控和叫号记录中bug的更改；新闻公告组件的编写；



④ 对新增的新闻公告页面进行完善和后台接口的调用渲染页面 ；



⑤ 新增的新闻公告页面功能开发基本完成,包括搜索重置编辑的bug的调整；



⑥ 完善新闻公告页面置顶和取消置顶功能；





3. 第三周



①  新闻公告页面bug修改-解决置顶和取消置顶的来回切换；



②  PIUI文档学习 新闻公告组件完成bug修复；



③  智能引导后台bug修复，主要是预约管理和叫号管理，部门管理；



④  PIUI文档组件完成计划编写相关组件的方法缺少的字段的完善；



⑤  PIUI 组件  文档/案例 编写  包括rate、stepper、radio、switch、codeInput；





4. 第四周



①  waterfall组件案例/文档编写；



②  select组件，skeleton组件，numberKeyboard组件编写；



③ 对已经编写完成的组件文档/案例进行进一步的完善；



④ 解决stepper组件源码小数位相加减精度溢出问题；



⑤ 基层公共服务支撑平台首页组件的编写；



- 技术的学习过程

通过这段时间的学习实践与导师的悉心指导，让我对项目的整体结构有了一定的认识，知道了如何通过目录文件去定位寻找目标组件位置，



了解了组件的使用方法及注意场合，学习了代码推送流程，JS常用的数组和字符串方法在项目中的使用，掌握了项目中几个vue常用API的使用，知道了怎么排查错误信息，



导师通过抛出问题的方式一步一步的引导我去思考，去定位到bug出现的代码位置，去寻找问题出现的原因，去思考，然后得出解决方法。不会的地方也会细心的讲解，直到问题能够被解决。



然而，在工作过程中也存在一些不足。



(1) 比如在完成智能引导后台管理时由于对自身知识体系的不够完善，和对业务的不熟悉和前后端工作的负责内容不够了解，以及没有及时的进行沟通，



导致在完成像解绑和置顶功能时本来只需要通过调用后台接口方法正确传参将其渲染在前端页面即可，而花了很多时间去



研究其方法实现的具体逻辑代码，做了无效的产出；



(2) 比如在对piui实例编写时由于对PIUI基础样式库的不熟悉，本来一两个类名能解决的样式问题却花了很长时间去手动调节，反而却是得不偿失；



(3) 比如在对element-ui框架的使用没有真正了解，一开始写页面样式时并没有用框架提供的组件进行快速开发，



而是花了很多时间自己手写样式，最后了解之后才又全部改了回来；



(4) 比如对组件的一些属性和方法的使用不熟悉而导致在改bug时无法准确定位而花了很多时间对比组件间代码差异，而没有第一时间去官方文档了解其对应的用法和使用实例；



(5) 比如在写一个功能时遇到了问题尝试解决过程中没有注意控制时间而导致过了很久都没有解决而花费了无效的时间；



(6) 比如写页面的时候没有组件化的开发思想而写了很多无效的代码,没有真正的利用到vue提供的各种api和语法提高开发效率。像这些问题在以后的工作过程中都得格外的注意，尽量提高效率。



- 知识的积累

      

① 全局组件和局部组件在使用时的相关注意事项；

② 了解vuex store仓库的几种属性及用法；



③ 项目代码提交规范和文件夹统一管理；



④ 了解点击穿透相关知识；







① vue中params和data属性对于post和get的对返回的请对参数格式格式的问题；



② 正确定义参数并正确传值；



③ vue-router路由控制；



④ 接口方式（put、get、post、delete）、v-model双向绑定；



⑤ 按钮绑定on-change事件响应v-bind的绑定值；



⑥ .map自定义数组封装后端接口数据；







①  Number(true) // 1；                 Number(false) // 0；



②  js中数据类型为假的6种情况：  null / undefined / false / 0 / 空字符串” ” / NaN；



③  slice splice方法使用； 修改仅查看权限 :disabled来做；



④  vuese gen 自动生成 markdown 文档；







① 组件化的开发思想；



② JS小数相加减的精度计算问题；



③ 常见数组/字符串方法的使用；



④  flex布局换行导致的间隙问题；





- 对项目的整体认知

由于自己之前没有真正意义上的去对做过的项目进行理解和思考，不能很好地将学习过的零散的知识进行结合起来，以至于第一次面对一个全新的真实项目，不知道要从哪里入手，组件对应的页面的定位，



对应的bug要如何进行定位修复，参数如何进行传递等都是一头雾水的状态。经过一段时间的接触，对于一



个项目，要先充分了解其技术栈分别有哪些，以及自己对于这些相关技术栈的熟悉程度以及是否有熟练掌握的技术，明确定位好自己在项目中的位置。知道项目的入口，路由如何配置，页面如何跳转传参及获取参数，





是否涉及权限的控制，常量/变量配置和引用，API的请求编写/请求拦截，项目的打包调试等都



是要先过一遍，心里有个底，才能更好的进行下一步的开发。



- 未来的学习工作计划



1. 多对每日/每周/每月工作进行复盘，总结不足的地方进行改进；

1. 每日坚持花一个小时左右时间学习补充新的知识，保证自己的技术和能力不落后；

1. 收获技术上和经验上的提升，自己的视野和各项综合能力也能得到进一步发展。