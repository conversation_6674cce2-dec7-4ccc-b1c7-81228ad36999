把赞美体现在代码上


工程师是喜欢被别人夸代码的一群人，无论他的技术多厉害都不会拒绝这样的赞美，除了口头的美誉还有很多可以尝试的方式，比如到团队的代码仓库里，多花时间把不同同学的代码都看看，

一个是可以帮助自己更了解这个团队的代码风格和质量，更重要的是这是一个极佳的了解别人技术深度的机会，在翻阅别人的代码的时候，看到不懂的地方可以做个笔记，凑合适的机会可以当面请教下，

还有一个更有意思的动作就是对写的让自己眼前一亮的代码，进行在线评论，赤果果的表达自己的崇拜之情，比如 “这个去重函数还可以这样封装啊，真是开眼界了！” 等等这样的崇拜脸，有了这样的尝试，

可以让所有技术好的同学对你产生好感，进而更愿意接受你的求助。同样，当自己在项目中参考别人的代码，也可以把引用信息备注到注释里，标明是受到了谁的启发诸如此类，如果再 open 一些，

可以把这段参考别人实现的地方截图发技术大群，向别人炫耀自己遇到了一个很好的代码实现，用这种方式来为原作者博得更多的成就感。往往打动别人才是迈出的第一步，而打扰别人往往成为你走出去的最后一步，两者的区别大家可以仔细品味下。


### 将代码仓库视为藏宝地


每一个团队的每一个仓库，背后都有它神秘的故事，所有的故事细节都埋藏在 commit 记录里，如果想让自己快速融入整个团队的技术栈里，必要的代码 review 是必须走的一步，这一步可以用寻宝的心态走。既然是寻宝，就一定是先粗筛选找出最有价值最有技术含量的仓库和组件，甚至是框架或者通用方案，定位这个仓库后，让自己一边看源码咀嚼里面的设计思想，一边用文字记录这个过程，也就是为这个仓库编写一篇技术实现分析的文章，通过这种方式，不仅让自己对原来团队的宝贝仓库代码更有理解，更重要的是，这个文章的团队内部发表，可以快速的激起大家的兴趣，任何一个点都可以拿来跟仓库的维护者进行一些形而上的讨论，从整个过程里可以进一步获得到他们原本深思熟虑的精髓思想，而这些思想，如果你没有经过自己的整理和理解，完全靠他们主动灌输也是很难消化的，但经历了这样的整理过程，你不仅会收获别人更多的输入更容易理解原理，也会获得更高的人气，更多的仓库的作者都希望被你 Review，这种事情当你做过后，你会发现自己可能比团队更了解团队，至于技术的提升，更不是问题了。


### 适当的制造一些惊喜


惊喜总是能让人眼前一亮，耳目一新，而适当的制造一些惊喜，对于融入团队有很大的帮助。比如你接手一个项目，又快又好的完成了项目并且还跟进了项目的后期效果，最终让合作方对你乃至整个前端团队都建立了新的好感和认知，那么这就是对于团队的惊喜，再往小一点，从家乡带一些特产来跟大家伙分享，也算是一种惊喜。


所以惊喜不一定拘泥于工作，也不拘泥形式，只要团队因为你的到来，而大幅度的或者小幅度的发生一些更好的变化。无论是整体研发能力还是气氛的营造都变得更好，那么你的到来注定是被所有人拥抱欢迎的，融入过程中的障碍也就更小了。





## 技术骨干身上的几个特征


前面我们对技术骨干的存在合理性建立了一个认识，我们接下来看看在他/她身上会存在的几个明显特征：


### 技术底子扎实


> 万丈高楼平地起，靠的就是深厚的地基，团队楠哥语录。


技术底子是工程师能力的核心基础，技术栈语言栈的广度深度，工程框架设计、原理的理解和运用的程度，这些方面不够扎实基本上与技术骨干无缘了。


至于说广度要多广，深度要多深反而没有一些清晰的指标。现在前端技术栈本身就是上下越来越厚，左右越来越宽，在 PC Web 的 Javascript 的单一技术栈上，如果积淀够深也足以支撑一个骨干的长成，同样在 ReactNative/Node 方面都如此，并不是前端的主要技术栈每一样都逐一掌握的足够好才能成为技术骨干，反倒是只要满足一专多长，这一专成立甚至是多个擅长项成立，那么就具备成为技术骨干的实力硬件基础了，接下来就要看软件实力了。


> 小案例：团队有个小伙伴 A，喜欢思考，关于 React 全家桶的知识储备非常扎实，从框架设计到内部运行原理以及同类型数据流方案的优缺点，所有人与他交谈都能有所收获，甚至醍醐灌顶。


### 善于独立解决难题


无论是在业务的技术架构中，还是纯技术性的攻坚中，我们知道工程实现会遇到五花八门各种各样的问题和难点，这些问题的解决和难点的突破，有许许多多可以尝试的办法，其中一个快捷键就是去请教团队里更资深的人，也就是场外求助迅速解决，但一个技术骨干在这方面往往能独立性的快速推进。


这里强调独立性并不是说他也不去咨询团队内外的人，而是说他不依赖团队内外的人，通过咨询是给他提供更多分析问题的视角，最终的解决依然是靠他个人。


问题解决的过程也因人而异，有的会快速的 Github/StackOverflow/Google 甚至百度/搜狗微信参考和 review 各种开源实现，有人会直接进入框架代码的阅读和逐行 Debug，有人会拿一支笔在画板上反复勾勒推理，无论哪一种，最终都是靠个人的能力解决问题，而每次解决问题后，能看到他征服困难后的成就感，用各种表情写到了脸上，身边的人也会受他感染共享征服感，大家可以设想下如果身边好多个技术骨干，每天都连续上演征服成功的案例，这对于团队士气提升是非常有益的。


> 小案例：团队有个小伙伴 B，喜欢独辟蹊径，无论多难受的问题到了他这里，总能独立以异于常人的方式把问题解决掉，这种问题解决的越来越多，功力也日渐增长，每次解决完都要在团队里炫耀吹嘘一下自己多牛逼，所有人都跟着开心片刻，这种氛围最终会影响到身边人。


### 不畏惧陌生领域的挑战


在一家增长型的公司，除了能感受到组织架构和业务上每个季度的变化外，还有一块技术人最看重的东西，那就是业务进程内外潜在的巨大技术机会与挑战，所谓技术成长空间。


这种机会通常是伴随业务的快速发展和大胆试错而来的，它有可能是业务驱动、运营驱动或者产品驱动，也有可能是技术驱动，无论哪种都可能会在原有的团队技术栈里面炸开一个口子，这个口子可能就是所有团队的工程师都不熟悉的领域，大家都不熟悉怎么办，除了快速招人外，就必须有技术骨干顶上去硬啃，至少能支撑项目的 1.0 粗糙版跑出来。


那么这时候技术骨干身上的不畏惧等于什么？怎么才叫不畏惧？一句 “都闪开，老子来” 口号算不算，我觉得只能算一半，前一半是刚开始时的勇气，另外一半是持续去挑战所带来的征服欲，征服欲越强或者兴趣越浓，越有驱动力去想法设法钻研，征服欲越弱，眼前的问题就会变成枯燥的任务，就算解决了，带来的征服快感也随之变弱。


> 小案例：团队有个小伙伴 C，遇到的一个技术领域上黑盒，在我们团队决定花精力去钻研个初步方案后，小伙伴自费搞了必要的设备，甚至整个过年期间都在强攻技术难点，终于春节后，带着可行性的方案来公司，为业务带来了极大的想象空间，驱动他的不仅仅是任务，更是征服欲所带来的满足感。


### 极少让别人失望


这个更多是在说结果，这个别人可能是合作方，可能是你的主管。为什么把这个单独拎出来，是因为不是所有技术好的同学都具备这样一个使命必达的执行力，只要允诺下来的事情，无论多难无论成败，都能拿出一定的成果来让等待的一方有所收获，这是一个技术好的同学走向技术骨干最重要的一个特征之一。


技术骨干的同学技术一定好，但技术好的同学未必是技术骨干，这一点往往被忽视，也是童鞋们在工作中最容易想当然的一件事情，如果你让别人失望的次数到了一定数量，那么距离技术骨干也还有一段距离了，因为骨干脱离了公司脱离了团队就会失去实际意义。而一个团队一定有它特定的定位和目标，目标的达成是衡量这个团队战斗力非常核心的标杆，也是主管脑海中的骨干的画像特征，定义骨干与非骨干的分水岭就在于此了。


## 如何快速成长为技术骨干


上面我们聊了技术骨干存在于我们大脑中的投影，也知道了他身上具备的显著特征，那怎么成为技术骨干呢？可以从下面几种路径入手：


### 1. 问清楚任务的 what 和 why


任何一个任务都有特定的背景和目的，比如老板让你去预研下 Electron 开发客户端软件的可行性，这是一定要问清楚这个可行性的软件客户端开发方案是为了承载什么场景的需求？为什么要用客户端而不是网页的方式来实现？


这就是任务的 what 和 why，需要你跟老板明确对焦，有可能他需要的仅仅是一个可以收发消息的聊天功能实现方案，这时候一个 socket 的聊天室网页版可能就能满足需求，应该是去调研 socket 更有价值而非是 Electron。一旦真的是去调研了，即便调研过程很漂亮，但对于最终问题的解决不是最优解，损失的不仅仅是老板对你的信任，更是失去了一次独立最优解拿下问题的机会。


有了这样的一个任务的对焦过程，我们会更了解到自己做这件事情的价值，对于结果也会更有期待，原始的驱动力天然就存在了。


### 2. 从过程中而不是从结果中学习


在微信群和社区经常看到提问的同学，非常焦急的等待一个问题的答案，或者是自己独立解决问题的过程中各种快捷方式求结果，拿到结果或答案后便迅速用到项目中，之后便丢到脑后，这是非常不可取的学习方式，每一次丢弃都丧失了一次成长的机会，要知道结果的价值是相对于业务和项目而言，而过程的价值才是相对于自己而言。


每一次拿到结果后，可以写一篇博客记录，也可以记个笔记，也可以弄张纸 review 一下，也可以讲给别人听，本质上是让自己重新播放刚才解决问题的过程，从中观察是什么样有意无意的动作和思考方式，启发了自己最终找到关键线索和路径，这样的一个思考过程反复锤炼会形成一个解决问题的套路库，比如什么问题直接 Google 就可以，什么问题要深入到代码中去深究，什么症状大概率是人为使用错误而非程序设计 Bug，从外向内再从内向外，让自己不仅仅对于技术框架或方案的细节更了解，也对于它们宏观上的特征更了解，最终让自己的问题解决能力越来越高效。


### 3. 以开放的视角看待一切技术存在


如今互联网所有上层的繁荣集市都是建立在各色各样的技术底层之上，无论是从 ASP 到 Go 这样的语言层面，还是 jQuery 到 React 这样的框架层面，从硬件到软件的方方面面杂糅在一个无限复杂的网络中执行着自己 0 和 1 的信号逻辑，任何一只能抓老鼠的猫都是一只有用的猫，技术同样如此，合理存在必然有特定场景下的价值，我们可以打开胸怀去观察甚至去接纳。


但开放到什么程度呢，是无限开放么？答案肯定不是。我们凡胎肉身不可能把目前极度细分的技术领域都摸过来，只能在特定的工程背景下做必要的心态开放，在未见到一个技术的真正价值之前不轻易否定它，在未评估好在自己项目中落地可能性之前不轻易使用它，这是两个层面的接收和拒绝，前者是价值与合理性的接收，后者是可行性落地与成本评估的拒绝。


聊这么多，跟技术骨干有什么关系呢？是因为技术骨干永远不知道自己接下来会面临一个来自什么领域的挑战，而保持视界的开阔和心态开放会给自己注入足够多的信息线索，有选择性的尝鲜，保持试错的好奇心，总是尝试去琢磨一个技术方案的核心价值点和设计策略，这样即便面临陌生领域的挑战，也可以用各种参照比对的方法为自己快速构建一个解决问题的路径坐标系，在这个坐标系里面，上下左右延展总会碰到之前大脑中索引的一些信息线索，从而触发一些灵感的产生，这些灵感的产生可能就是问题得到有效解决的关键。


### 4. 坚持高强度的学习和持续性的总结


当我们可以正确的认知一个任务的特征，也能有一个开放的心态和开阔的视野观察问题，也能从问题解决过程中回收套路进行索引的时候，我们距离一个技术骨干就差一个习惯了，这个习惯就是高强度的学习和持续性总结的习惯，为什么学习要高强度，而总结要持续性呢？


学习是为了输入，知识体系变得有力量一定需要足够的输入，而输入从哪里来，连续做两年 React 框架内的业务代码可以带来沉淀么？其实也未必，如果常年做业务但没有深入框架内部学习，也没有对框架之内的设计（如数据、状态、交互、异步、更新等等实现原理）更没有对框架之外的意义（组件、API、工具链、维护与封装等成本与效率）有足够的认识，那么所谓的内修基本功是站不住脚的。


至于说高强度，是因为低烈度的输入会伴随着遗忘，更会导致整个学习周期过长，更容易看不到质变而感觉枯燥无味甚至弃坑而去，这尤其在新人身上容易发生。如果让我建议一个学习的周期，我觉得 1~2 个月的高强度学习，分成 2 ~ 4 个小阶段是可取的，如果 2 个月没有明显进阶，那么需要推倒重来从 0 开始，而不是续命。


伴随着学习的一定是总结，所有的美食入口到胃，长长的肠道蠕动很久后，营养成分才能被机体充分吸收，最终再合成为新的动力之源要么燃烧要么存储备用，这时候的摄入才转成自己身体的一部分。无论是项目开发还是单纯的学习，都要给自己建立一套 review 机制，通过 review 把自己摄入的零碎的知识点进行重组串联，反反复复的理解消化，并重新输入一套新的知识蓝图把它刻到自己的记忆硬盘中，通过这样的持续性的总结归纳，自己的记忆硬盘会的不断的升级调整，最终对于所有知识的理解会越来越立体。