


### 初级前端


1.  了解项目背景，参与项目/产品开发，按照要求完成功能


1.  熟读开发规范以及工作手册，养成良好的编码习惯


1.  每天下班前更新项目进度，并主动上报开发过程中遇到的问题点

1. 


1.  熟练使用ui框架开发页面，在使用的过程中反馈问题


1.  

1. 提交代码前进行codereview，确保无明显问题

1. 


1.  每周开展项目评审会议，对已完成功能进行讲解

1. 


1.  前端基础知识持续学习，每月进行知识总结，输出一份语雀文档

1. 


 





### 高级前端


1.  负责项目初始化，需求的梳理，并初始化项目


1.  对设计图进行公用组件的抽取和实现，定义组件的属性（输入输出）标准化


1.  

1. 审核code review，产出评审报告

1. 


1.  参与前端基建，负责技术难点的攻克

1. 


1.  

1. 每个月至少出一个PiUI官方模板

1. 


1.  每个季度发布一篇深度研究的技术文章

1. 


 





### 资深前端


1.  

1. 担任项目负责人角色，管理团队&项目，对开发任务进行分解

1. 


1.  负责前端基建的项目规划以及版本迭代

1. 


1.  负责项目技术评审


1.  负责团队的沟通协作

1. 


1.  每次版本迭代结束后，应该组织复盘总结会，总结成功经验，吸取失败教训，有助于提升团队能力

1. 


 





### 前端技术经理（前端专家）


1.  负责前端技术路线的规划，研究，培训以及落地计划

1. 


1.  负责前端技术专栏的定期培训

1. 


1.  负责前端相关规范的制定 


1.  组织前端会议（2周一次）


1.  负责面试相关工作


1.  

1. 对员工转正进行考核（制定考核标准）

1. 


 





### 


### 前端主管（高级前端专家）


1.  负责团队成员的能力成长，能力成长规划

1. 


1.  负责前端团队的能力建设，文化建设，技术建设 

1. 


1.  每月对团队成员进行一次访谈，并记录

1. 


1.  组织管理层会议（2周一次）

1. 


1.  团队成员的职级评定（能力评定基准标准）以及晋升考核

1. 


1.  负责团队业界影响力的提升

1. 





### 如何做？


1.  以上标红的目前团队缺少的能力，需要按照实际情况分阶段重点去加强落实


1.  如何实施落地？每一项开展都需要有资料的产出（飞书记录，规划，持续性迭代更新，阶段性总结），向上汇报，向下管理 


1.  第一步怎么做？管理的目的 → 团队的能力提升，个人的成长


1.  先将团队成员对号入座（定职级） 


1.  努力大胆的发散思维，将对应岗位职责工作做好（有思考，做规划，写文档） 


1.  重点培养新人（柏全，贤攀，宏斌，永红），进行转正考核评定（评定标准是什么？需要制定）


1.  事项很多，会花费大量时间，影响到项目的开发？


1.  转变思维，做技术的对于难点的攻克总是自信满满的，如果我们拿每一项管理的工作当做项目来做呢，任务分解，任务规划，周期迭代，优化升级，再难的事情也会做的很好，事在人为，努力的去推动它就有收获


1.  自己做 → 教会别人做


 