

| 分类 | 能力要求【每项 0 ~ 5 】分 | 初级前端 | 中级前端 | 高级前端 | 主管 |
| - | - | - | - | - | - |
| 能力要求 | 0：不具备<br>1：仅仅了解<br>2：能做到<br>3：能做到，表现良好<br>4：能做到，并且表现优异<br>5：做的很好，可以作为同事学习的榜样 | 46 | 73 | 108 | 128 |
| 业务能力  | 1.  能对项目需求和业务背景有全面的理解    | 2 | 2 | 3 | 3 |
|  | 2.  能对设计图中的业务流程有明确的理解 | 3 | 4 | 4 | 5 |
|  | 3.  具备准确的业务进行构建数据模型的能力 | 2 | 3 | 4 | 5 |
|  | 4.  对业务边界以及数据边界有敏锐的判断能力  | 2 | 3 | 4 | 5 |
| 学习能力  | 1.  有明确的职业规划和学习规划   | 2 | 2 | 3 | 3 |
|  | 2. 能周期性制定明确的目标，持续提升 | 2 | 2 | 3 | 4 |
|  | 3. 学习过程中，能有效的进行总结，有文档输出 | 3 | 3 | 3 | 4 |
| 协作能力  | 1. 能对需求疑问点能合理的和客户沟通达成最佳解决方案 | 2 | 3 | 4 | 5 |
|  | 2. 能对接口不满足地方能够和后端同事沟通给出建议达成一致 | 2 | 3 | 4 | 4 |
|  | 3. 能和设计部门沟通，从用户体验考虑，调整UI，明确交互方式 | 3 | 3 | 4 | 4 |
|  | 4. 在编码实现方面，能够对项目有全局思维，和同事协同处理好公用模块的实现方式，沟通要点和关键点 | 2 | 3 | 4 | 5 |
|  | 5. 面对客户沟通表现礼貌，专业，能及时对客户进行反馈，对客户有阶段性汇报 | 1 | 2 | 4 | 5 |
|  |  |  |  |  |  |
|  |  |  |  |  |  |
| 技能水平  | 1. html，js，css基础掌握能力       | 2 | 3 | 4 | 5 |
|  | 2. vue语法掌握能力 | 2 | 3 | 4 | 5 |
|  | 3. 数据算法，模型的转换能力 | 2 | 3 | 4 | 4 |
|  | 4. typescript&amp;es6语法掌握能力 | 2 | 2 | 4 | 5 |
|  | 5. piui使用熟练度 | 2 | 3 | 4 | 5 |
|  | 6. 进行技术难题攻坚、技术方案比对 | 0 | 2 | 4 | 5 |
| 项目管理  | 1. 积极努力推动项目开展 | 2 | 2 | 4 | 4 |
|  | 2. 每天反馈更新项目开发进度 | 2 | 3 | 3 | 3 |
|  | 3. 周期性更新项目进度，把控项目风险 | 0 | 2 | 4 | 4 |
|  | 4. 能够有效的把控项目的需求边界 | 0 | 2 | 4 | 4 |
|  | 5. 积极的进行团队的项目沟通 | 2 | 3 | 4 | 4 |
|  | 6. 能管理项目资源，调节自我情绪、会沟通、有感染力 | 1 | 2 | 4 | 5 |
| 影响力  | 1. 独立思考，能优化工作流程提出建议 | 1 | 2 | 4 | 5 |
|  | 2. 自我驱动力强，主动建设效能提升工具 | 0 | 2 | 3 | 5 |
|  | 3. 有知识总结能力，不定期进行知识分享 | 1 | 2 | 3 | 4 |
|  | 4. 能主动指导同事，分享自己的知识和经验 | 0 | 2 | 3 | 4 |
|  | 5. 积极参与基础建设工作 | 1 | 2 | 4 | 5 |


