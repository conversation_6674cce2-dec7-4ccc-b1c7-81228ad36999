301、文字对齐（中日韩文）：  最后一列保持一样的样式

text-align: justify;

302、字体图标加粗 ： **-webkit-text-stroke  **

![](images/WEBRESOURCEab1cdd61160fafe51c75e101f4987a03截图.png)

-webkit-text-stroke: 2px white;

1px  粗一大半了。

303、vue 的 @click.stop.prevent 修饰符 连用 

304、浏览器的默认字体大小：

![](images/WEBRESOURCE5da2d6d6074614d9a0f353f8a61aa32f截图.png)

![](images/WEBRESOURCE409d3ce7a8c48e4b98ace43e96184a4e截图.png)

305、两个watch方法里，同时生效一个方法，只会执行一次？

![](images/WEBRESOURCE0da5bf8ffe1b6b25167ca8b82cf550af截图.png)

306、打开一个页面的三种方法：

（1）、 window.open() 方法

（2）、location.href 属性

（3）、createElement('a') 创建一个a标签 append到body，跳完再removeElement

307、前端构建工具的基础：  JS模块化

308、跨端**小程序** 编译时/运行时； 

309、Airbnb JavaScript 编码规范指南(ES6)

(1)、变量不要链式赋值，会创建隐藏的全局变量，污染全局命名空间。

![](images/WEBRESOURCE6c48e489c60a48bbba1f5360b3bba51e截图.png)

会把链式赋值后面的值当全局变量使。

(2)、匿名函数/具名函数 变量提升-提升变量名，不是赋值

![](images/WEBRESOURCE1ac4f41093715e9d6161a2361c8c3902截图.png)

![](images/WEBRESOURCE105c5112aea3390e4ad49fb87f096fd5截图.png)

![](images/WEBRESOURCE066c7022e7b8d62a643d382a8a09b76a截图.png)

（3）、对于布尔值使用简写，但对于字符串和数字要显式比较.  （对于后两者来说 就是**更严谨点**？相当于 === 不用 ==的意思？）

![](images/WEBRESOURCEafd6f57dd8510d2c7473f8b8d92a88d1截图.png)

（4）、避免不必要三元

![](images/WEBRESOURCE93c5394c205a61d627b5c26eef6e4d63截图.png)

（5）、

310、JavaScirpt 中的** ****falsy 值**：  有且只有 8 个。

0(+0) 、 -0、 null、 undefined、 false、** ''( `` / "" )**、 **NaN**、**0n**( 读作 零n (字母n) ，就是定义一个 BigInt 的数  )   ---  与 0 是宽松相等,不严格相等 

![](images/WEBRESOURCE97736592ee00181c10b05eb1bfce065c截图.png)

**BigInt** 

**BigInt** 可以表示任意大的整数.

可以用  （1）在一个整数字面量后面加 n   /   （2）调用函数 BigInt()    的方式定义一个 BigInt 。

![](images/WEBRESOURCE9ccbf34ad698bfbbb6fbe84d752389b5截图.png)

![](images/WEBRESOURCEa12bb15efaf1e2222a9113182d1a4d9d截图.png)

![](images/WEBRESOURCE7c6eb52daee867e97fa3aef5b6230326截图.png)

311、JSON对象 转 树结构

写！！

Map 解千愁啊 ...  万事皆可Map !!!

**递归是最消耗性能的**

![](images/WEBRESOURCE364ca49abce859f41d8cf62ecdf60e34截图.png)

![](images/WEBRESOURCEdb45b0cf34487ac65d32e2a5b435e48e截图.png)

312、 网站灰白

html.gray-mode {

 ** filter: grayscale(1);**

**  -webkit-filter: grayscale(1);**

}

移动端/app 设置了 fixed 等属性 会失效。

![](images/WEBRESOURCE2ea711e9777f0e4fb3cdcbce3842ca0a截图.png)

![](images/WEBRESOURCE1ee92edaff8da2f9ba34efab7cdbfa71截图.png)

全局黑白后还要局部彩色？

![](images/WEBRESOURCE30e95f4dd403302bdb72d474e8e7c1b9截图.png)

看起来是局部元素  filter: none !important;**（**当前元素**）**

如果是局部实现，可以设置一个类名：

**.gray_mode** {

  filter: grayscale(100%);

  -webkit-filter: grayscale(100%);

  -moz-filter: grayscale(100%);

  -o-filter: grayscale(100%);

}

**局部加上这个类名就可以了**

实现类似移动端 / h5 只有遮挡首屏的视口那么大的黑白，往下滑动就是正常色彩了。

：  一个 100vh，100vw 的 div，覆盖在最上面，然后设置属性** backdrop-filter****（**元素**背后**的所有元素**）****: grayscale(1); **

再设置下 pointer-events 允许点击穿透过去， 就可以啦。

![](images/WEBRESOURCE4db2ca25d8b0e12dfde3021e6cec8e17截图.png)

![](images/WEBRESOURCEf8f9dd143a7d10f99d42c8f540f4c046截图.png)

![](images/WEBRESOURCE4bbeb7fedebc49672c9b1254201a0aec截图.png)

313、**column-count**

描述元素的列数

![](images/WEBRESOURCE4769a31353ef00e6bbf1570a9aba2607截图.png)

![](images/WEBRESOURCE4b20bc5020fe0af4b954a047a8e5c386截图.png)

![](images/WEBRESOURCE4ea6cf751cf8aa20fa694231b5b46612截图.png)

314、前端的 Hack ：

** ****针对不同浏览器编写css前缀代码是 css hack  （写一些不同浏览器的兼容代码）**

![](images/WEBRESOURCE433f6ec34fabaf2b912ff8ff71665712截图.png)

![](images/WEBRESOURCEd335d70d9576b14f10445817b548b6e7截图.png)

![](images/WEBRESOURCE0d7f1660edbdfdbf6116ab3bab645626截图.png)

315、自定义 CSS 属性 --* :

![](images/WEBRESOURCE36774c3384129a2b25a5259f843e13d6截图.png)

![](images/WEBRESOURCEcf1d5bb3391c5cdbed8b30502f719b74截图.png)

316、clip-path 这个 CSS 属性：

画不规则多边形（polygon），可以画画，画任何头像，你想要的。

mix-blend-mode (混合模式) / filter / backdrop-filter / background-blend-mode （背景混合模式） / mask-image / mask-clip / cross-fade() 图像函数（设置透明度）

原图：

![](images/WEBRESOURCE731378acefc13a3488be715d997da088截图.png)

增加了 background-blend-mode 的 div

![](images/WEBRESOURCE3e8ca6b2bc90059f7da5ad52c39d0830截图.png)

317、常见的编程范型有：**函数式编程、指令式編程、过程式编程、面向对象编程**等等。 编程范型提供并决定了程序员对程序执行的看法。

在面向对象编程中，程序员认为程序是**一系列相互作用的对象**，由于方法论的不同，面向对象编程又分为基于类编程和基于原型编程，

而在函数式编程中一个程序会被看作是**一个无状态的函数计算的序列**。

- 拒绝副作用，拥抱纯函数（相同输入输出）

- 函数是“一等公民”     First-Class Function   头等函数 。   JS 函数的本质，就是**可执行的对象**。

        1. 函数是否可作为参数（形参实参）

        2. 函数是否可作为返回值

        3. 函数是否可赋值给变量

      满足3个（一等值）一等公民

- 避免对状态的改变（不可变值）    数据不可变

函数副作用？ 纯函数 （没有副作用，相同输入输出）

**side-effect ?  ** 除对输入产生预期的输出外，还对 **执行上下文 **/ **执行宿主 **等外部因素产生影响的。 

（除了单纯计算外 没有做其他的任何事情）

“快照”： **快照  本质是对文件索引的记录 （保存/更新的是索引，而不是文件本身）----  **持久化数据结构的思想   --- 做 **数据共享**

字典树 （trie） / 数字树 （digital tree） / 前缀树 （prefix tree）

JS元编程：　**二次修改编程语言**（对编程语言进行再定义） Proxy 、Reflect、Object.defineProperty、eval、JSON.stringify / parse 第二个参数（为函数时可改变源数据）？

普通编程：只能用该编程语言本身定义好的一系列方法（API）去使用该接口   只读

元编程： 支持自定义方法（API）的功能/用法，可以改变程序本身的一个能力 可写

eval 函数 ？  把字符串转成可以执行的代码。（在函数内部 间接调用eval 当全局变量使用） 很耗计算，依赖JS解释器，不好。 很容易受攻击

返回值：   返回最后一个表达式的值 。  如果 定义的字符串内容是 函数的话， eval前后 需要用   **"("**   和   **")"**   的前缀和后缀。

```
var fctStr1 = 'function a() {}'
var fctStr2 = '(function a() {})'
var fct1 = eval(fctStr1)  // 返回 undefined
var fct2 = eval(fctStr2)  // 返回一个函数
```

![](images/WEBRESOURCE44dff235db3c3da1c526d9bbcb2dff80截图.png)

替代方案：  **Function 构造函数**

![](images/WEBRESOURCE814778c68ca09a3815c83ec8695843dc截图.png)

![](images/WEBRESOURCE36b46e9e577ccd962be4b356d415d64b截图.png)

** **

318、关于 promise 的使用 ： 

永远记住这三件事：

1. return 另一个 promise

1. return 一个同步的值 (或者 undefined)

1. throw 一个同步异常

别忘了加 .catch 捕获异常

![](images/WEBRESOURCE4ed6268e23e0734358d6237dfd7c1ffc截图.png)

发生这个的原因是如果你像 

then() 传递的并非是一个函数（比如 promise），它实际上会将其解释为 

then(null)，这就会导致前一个 promise 的结果会穿透下面。

![](images/WEBRESOURCEac03f5b307cb687c9b01eba94e9b3d04截图.png)

**永远都是往 .****then()**** 中传递函数！**

参考 ： [http://fex.baidu.com/blog/2015/07/we-have-a-problem-with-promises/](http://fex.baidu.com/blog/2015/07/we-have-a-problem-with-promises/)  promise 深度好文

319、 p 标签 嵌套 p 标签， 会变成两个平级的 p 标签 。 （eslint 规范可能都过不去）

![](images/WEBRESOURCEf9c785caddcc4653e47e858b1f2405c9截图.png)

最终显示：

![](images/WEBRESOURCE8e2ca4e5959db25c8c09498886d15389截图.png)

320、前端（向服务器）发送网络请求的方式：

（1）、ajax （1、const xhr = new XMLHttpRequest() 然后 2、xhr.onreadystatechange = function() {} 然后 3、xhr.open(method, url, true) 然后 4、xhr.send() ）

（2）、fetch API      （   fetch(url, options)    ）

（3）、axios

（4）、form 表单 的 action

（5）、script 标签的 src （类似 CDN 那种  /   JSONP）

（6）、 img 标签的 src （get 请求）

（7）、 iframe 标签的 src

（8）、navigator.sendBeacon(url, data)    （异步 post 请求   不能携带参数）

（9）、 css请求外部资源 （link 标签的 href）

（10）、 webSocket、 消息主动推送 .....

（11）、web worker 发辅线程，然后再合并到主线程（利用CPU，资源不浪费，不会阻塞主线程）

321、内网穿透？ 使用第三方工具进行配置，将你的本地ip（127.0.0.1）映射到三方平台提供的网址中，然后其他电脑就可以通过这个三方平台暴露的地址直接访问你的本地服务了。

![](images/WEBRESOURCEdb299f4c0bcb8b60c3e863f74bb3e7f8截图.png)

322、以后再遇到 给一列横向排列的盒子增加hover效果（默认第一个盒子要有hover样式），就别用 js 实现了。纯 css 能实现啊。

（1）、用定位，将提前写好的跟hover一模一样的样式的div盒子定位到第一个盒子上面，然后hover样式该怎么写怎么写。

             但是要多一个变量（默认true）记录当前是否hover这个动作，在mouseenter的时候设置false标记这个div隐藏掉，使用hover的样式；

然后在mouseleave的时候设置该变量为true标记要使用这个div的样式当做第一个盒子的默认样式（hover时候的样式）。

然后就可以在这个div身上加个动态 style 判断了， :style="{ display: 变量true : 'block' ? 'none' }"。

![](images/WEBRESOURCEdb01d30aa196480efa901766851486a2截图.png)

（2）、纯 CSS 实现：  给第一个盒子和hover一起设置样式，**然后在 li 的父盒子 ul 中 再设置 hover 对 第一个 li 的 hover 做样式控制** （取消第一个盒子的默认样式）

     （li hover 时不能控制第一个 hover 是否显示， 但是 ul 可以啊 ，主要就是利用父盒子可以控制子盒子的 hover 这一点。）

     然后鼠标在移动到第一个盒子的时候，这个时候再单独设置 hover 样式。这样子就完美解决了。。。。。！！！~

![](images/WEBRESOURCE15cc5e322494b0deddd28f828e581784截图.png)

323、 display: flex;设置水平垂直居中 + 页面滚动到该模块一半时 + 鼠标 hover 该模块   ===》   出现切换的时候页面疯狂抖动的 bug ？ 用 margin 手动计算px 代替居中解决。。。 

**多行文本的时候就不能用 height + line-height  做 垂直居中了 ！！！！！**

   

324、**1970-01-01**    这个时间是计算机的起始时间    纪念 **unix操作系统** 的诞生。（北京东八区，时间早8个钟，所以是08:00:00）  标准时间是 GMT 格林威治时间 (Greenwich Mean Time，简称G.M.T.)

![](images/WEBRESOURCE75fb27da337a5cc92668213db607b5a3截图.png)

new Date() 的值是取的 **运行环境** 的时间（浏览器的话就是本地电脑/手机的时间，这个是可以用户手动改变的，不准确，不安全。 nodejs的话是服务器的时间）

![](images/WEBRESOURCE0e731dc6d62a0dbfd6046cc61f6e1f20截图.png)

![](images/WEBRESOURCE7dc7f32676cc7a5284bcb72010a604dc截图.png)

325、cookie

js-cookie 插件  （js-md5）

1）、 js 设置 cookie （只设置该值的字段，不影响已有的字段，不会覆盖！）  document.cookie   设置/修改/删除（设置expires="new Date(0).toUTCString()"或者设置maxage="-1" 过期时长，刷新后会删除）

2）、默认是浏览器关闭的时候cookie会消除（被清空），除非手动设置 expires/maxage 后，关闭浏览器不会消失，只有在到期时间时才会被清除

3）、一般情况是服务器设置的（Set-Cookie）字段，然后给浏览器，请求时每次都带上cookie字段在request header上，辨识是同一身份（用户）进行的请求（相当于token放cookie上了）。服务器设置

  httpOnly的话，浏览器的 document.cookie 是获取不到 cookie 的，只有服务器才能取到。

4）、常见的字段有 **name****、****value****、****domain****、****path****、****expires/Max-Age****、****size**、samesite、httpOnly、等

5）、存储大小限制只有4kb，会跟随每个请求发送到服务器。算http协议一部分   （原生没有直接获取和设置指定字段cookie值的api，用正则自己封装下getCookie/setCookie函数，或者使用第三方插件 js-cookie 等......）

//清除所有cookie函数

```
function clearAllCookie() {
  var keys = document.cookie.match(/[^ =;]+(?==)/g);
  if(keys) {
    for(var i = keys.length; i--;)
      document.cookie = keys[i] + '=0;expires=' + new Date(0).toUTCString()
   }
}
```

Storage 对象（包括 **会话存储** **sessionStorage** 和 **本地存储** **localStorage**）

1）、共同API ： getItem / setItem / removeItem / clear / length

2）、**sessionStorage** 当前标签页tab关闭就消失（当前页刷新不消失），**localStorage **不手动清除就不会消失

3）、**sessionStorage **生命周期只在当前会话（标签页）生效，不同tab相同域名不会同步相同的key； **localStorage **同源窗口会共享数据

4）、相同页面不同tab的第一个tab中 setItem('test', 1)，相同页面不同tab的另一个tab中 getItem('test') 的结果是 null

5）、两个的 存储大小限制都为 5m+ （不同浏览器略有不同）

indexedDB: 浏览器的键值对数据库，可存储更大更复杂的数据

326、基本类型和引用（复杂）类型：

简单：string、number、boolean、undefined、null、bigInt、symbol

引用：Object

(标准)内置对象: Array、Date、Function、RegExp、Math、等  （包含包装对象）

（[https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects)）

包装对象（包装类）：String、Number、Boolean

327、var 声明 和 不用 var 声明直接赋值 区别是 会不会变量提升

328、js 改变**数组元素**本身的方法：  shift 、 unshift、 pop、 push、 reverse、 sort、splice、 fill、 copywithIn（不会改变length）、 map（基本数据类型时不会改变，引用类型好像也不会啊。。。 ）就是不会改！

改变**字符串**本身的方法  ： replace（正则方法）

329、html标签 转 字符串  **浏览器解析**  用  '\' 转义符

![](images/WEBRESOURCE9586b6b3565a7ebe9295877d6c0fbfa9截图.png)

330、form-data    基于**post**方法    请求内容格式为**Content-Type: multipart/form-data**

**        **生成一个boundary字符串来分割请求头与请求体

类似表单提交的 一种 数据结构 

331、chatGPT： **Generative Pre-trained Transformer**  **生成式预训练变形器**     增大模型参数量  预训练模型   （**发指令  ----->   响应指令**）

                                                                                                                             专业词汇                  精准响应

 技术调研 （确定技术方案）  +  正则表达式  +  代码优化  +   函数功能测试  + 现成 demo + 报错调试  +  源码解读

332、dom元素可以直接使用id名称来获取元素，会挂载到 window的 原型上。

![](images/WEBRESOURCE1ff04e1fb06b375378890cfcb69d567a截图.png)

333、console.**dir**     输出函数  ------>   按文件目录结构 输出

![](images/WEBRESOURCE070e34615dee614f033df590d2880bf5截图.png)

334、 **web component  三要素**

** **

创建封装功能的定制元素，无框架限制，极致的复用

（1）custom element  （**自定义元素**）

（2）shadow dom    （**影子 DOM**）

（3）html template （**HTML 模板**）

335、window.**requestAnimationFrame**(callback) 实现动画效果 （**在浏览器下一次重绘时/下一帧 执行一次**）   （CSS动画优先，JS实现动画避免使用定时器，用 requestAnimationFrame）

（1）、setTimeout / setInterval 定时器 受**机器硬件**设备性能影响，**抖动/卡顿** ；  **屏幕分辨率 / 尺寸** 影响  延迟时间跟设备刷新时间不同步，会出现**随机丢帧**的现象。

（2）、 与定时器相比较，**优势**是**将 回调函数的执行时间 交由 系统 来决定**。（屏幕刷新率是60Hz,其回调函数大约16.7ms执行一次 1000 **/ **60； 屏幕刷新率是75Hz,其回调函数大约13.3ms执行一次 1000 **/ **75）

（3）、保证回调函数在屏幕的每次刷新间隔中只被执行一次，从而避免因随机丢帧而导致的卡顿现象 	

![](images/WEBRESOURCEf3fad4584fa5caadd40818e6a686eae6截图.png)

![](images/WEBRESOURCE5ea4f9569fc3ab99a4001b6ee12e816d截图.png)

336、**wheel 事件**  和  **scroll 事件**

（1）、 鼠标 **滚轮滚动 **事件     和      **页面滚动条滚动  **事件

（2）、wheel事件的事件对象中包含deltaX、deltaY、deltaZ属性，表示滚动的距离和方向

scroll事件的事件对象中包含 scrollTop 和 scrollLeft 属性，表示滚动条的位置

337、** http2**

http1.0       每次请求都要重新建立连接。增加延时

http1.1	  **队头阻塞**（Head-of-Line Blocking）一系列包有一个阻塞了，导致后面的请求都无法进行；

     **连接无法复用**（**keep-alive **复用一部分连接）

     **协议开销大**（Header 请求头携带内容大，每次不怎么变化）

     **安全因素 **（所有传输的内容都是明文，一定程度无法保证数据安全性）

http2.0

     1. 二进制传输

     2. 多路复用

     3. Header 传输

     4. Server Push

server push （服务器主动向客户端推送） 与  websoket 的区别 ： 

推送资源 （html/ css/ js ...）				数据交互、客户端可以控制（相互通讯）

338、webpack 配置： 

-  路径解析优化

-  include /  exclude

- resolve  -> alias / extentions / modules

- uglify  ->  terser

- cssloader  ->  mintsize  -> cssnano

- tree-shaking

- happypack  -> thread-loader  多线程管理

- 配置 sourcemap： 基础配置的组合    **cheap 			**只映射到行数，不精确到列，提升 sourcemap 生成速度

**         eval		      **浏览器 devtool 支持通过 **sourceURL** 将eval函数的内容单独出来，支持eval函数内容的可调试

**         module		      **sourcemap 生成时会关联每一步 loader 生成的 sourcemap，可以映射回最初的源码

**         nosources	      **不生成 sourceContent 内容，减小 sourcemap 文件的大小

**         source-map	       **生成 sourcemap 文件，可以配置 inline，会以 dataURL 的方式内联，可以配置 hidden，只生成 sourcemap，不和生成的文件关联

 

框架源码调试：  **一行代码开启 sourceMap     **在代码的最后加这一行     （普通js代码直接加 **sourceURL**）

![](images/WEBRESOURCE966a23c27e1ac42fb81ab65d43fb5185截图.png)

**//# sourceMappingURL=xxx (自定义的名字)**

339、H5 白屏优化指标：

**性能指标：**

FP：       First Paint                                首次绘制                      页面第一次绘制像素的时间

FCP：     First Contentful Paint		  首次内容绘制	       页面首次绘制文本、图片、非空白Canvas 或 SVG 的时间

LCP：     Largest Contentful Paint         最大内容绘制	       记录视窗内最大元素的绘制时间（最大元素在页面渲染过程可能会发生改变） 页面的**速度**指标

FID：      First Input Delay			  首次输入延迟	       记录第一次与页面交互到浏览器真正能够处理响应 该交互的时间（浏览器主线程无法及时响应用户）页面的**交互体验**指标

CLS：     Cumulative Layout Shift          累积布局偏移	       记录页面上的非预期位移波动 （滑动过程突然动态插入一个DOM。植入广告）推荐值小于 0.1  页面的**稳定**指标

TTI：      Time To Interactive		  可交互时间                   

TBT：	  Total Blocking Time		          总阻塞时长

**优化手段：**

340、 web worker   和     service worker

**运行在后台的JavaScript，它是独立于浏览器的        **单独的线程 无法直接访问主线程的DOM或者全局变量  （如果需要在 Worker 中使用这些资源，可以通过 postMessage() 方法将它们传递给 Worker）

web worker的主要作用就是使用JavaScript创建workers线程，浏览器的主线程就会把复杂的逻辑，

内耗较大的任务交给workers线程去做，主线程继续运行，同时workers线程也在后台运行，这样它们互不干扰，

直到workers线程运行完成，把结果返回给主线程，主线程再继续执行相关的任务内容。

workers线程一旦创建成功了，就会一直运行，不会被主线程打断，这样就能随时响应主线程的通信。所以用完要及时关闭workers线程。

![](images/WEBRESOURCEdc0651c7e1c6e266b62f8433c8530dcf截图.png)

possageMessage() 发送消息

onmessage 监听 接收消息

![](images/WEBRESOURCEf8e17b8f705e2662b7a873e5fcd9a5b8截图.png)

![](images/WEBRESOURCEd5c470aaba42f165c370668eeaf36685截图.png)

341、RESTful API      支持以下的请求方式    （结合 axios）

**GET:     **获取资源（列表）      /api/xxx  （{ url: '/api/xxx', method: 'get' }）                     /api/xxx?id=xxx  （**{ **url: '/api/xxx', method: 'get', params: { id: xxx } ** }**）

**POST:  **创建新的资源	       /api/xxx   （**{ **url: '/api/xxx', method: 'post', data: {}  （request body）  ** }**）

**PUT:     **更新现有的资源        /api/xxx  （**{ **url: '/api/xxx', method: 'get', data: {} ** }**）      /api/xxx/subxx  （**{ **url: '/api/xxx/subxx', method: 'get' **}**）

**DELETE:  **删除资源	      /api/xxx  （{ url: '/api/xxx', method: 'delete' }）

**PATCH:  **更新资源的部分属性

**HEAD:  **获取资源的头部信息

**OPTIONS:  **获取资源支持的请求方式和头部信息

342、new URL( url [, base] )

```
// 默认是 "/"
let m = 'https://developer.mozilla.org'
let a = new URL("/", m)    ===>    等同于   let a = new URL(m)
// href => 'https://developer.mozilla.org**/**'

// url 为相对路径, 会 base 到 m 后面
let b = new URL('en-US/docs', m);
// href => 'https://developer.mozilla.org/en-US/docs'
```

![](images/WEBRESOURCEe7b8b45fd9bd1b98d87453953bb88400截图.png)

343、Nuxt.js  优化 百度搜索引擎算法   SEO   

（1）、static 文件夹下面新建立 **robots.txt** 文件 ，编写符合 robot 协议 的 内容， 百度会根据该文件的配置信息进行对应内容的抓取

（2）、装包 下载 @nuxtjs/sitemap 依赖库， nuxt.config.js 文件配置生成 地点地图  该插件会读取你的网站路由信息 npm run generrate 会自动生成 sitemap.xml 文件 

344、three.js 基于 webgl（js实现的库 2d + 3d），cesium（3d可视化地球引擎）基于 webgl， webgl 基于opengl es 2.0

345、 

**      进程： **操作系统分配资源的基本单位，每个进程都有 独立的 运行环境 和 内存空间，可以并发执行 多进程

**      线程： **进程子任务，CPU调度基本单位，每个进程可以有多个线程，线程见共享进程的组员， 可以并发执行 多线程 

346、 Promise.all  要成功/失败都返回   用 Promise.allSettled()

两个参数，第一个promises数组，第二个value/reason 对应成功的值 / 失败的原因

- status：表示对应的 Promise 对象的状态，可能的值有 "fulfilled"、"rejected" 和 "pending"。

- value 或 reason：如果对应的 Promise 对象的状态是 "fulfilled"，则该属性表示对应的 Promise 对象的返回值；

      如果状态是 "rejected"，则该属性表示对应的 Promise 对象的拒绝原因；如果状态是 "pending"，则该属性值为 undefined。

![](images/WEBRESOURCE81ca90ece056b575790b330be8a3c0c5截图.png)

使用Promise.all 进行 polyfill

Promise.all(promises.map((promise) => {

  return  promise.then(value => ({ status: 'fulfilled', value }))

     .catch(reason => ({ status: 'rejected', reason }))

}))

![](images/WEBRESOURCEa6a664ee5d7f47df4653b1fea263c9f8截图.png)

347、 vite 开发环境 用的 esbuild （底层 go  快）  node （单线程）慢

    生产环境 用的 rollup 

348、 git 默认走 ssh，是不走本地代理的。要改成 http 的， 才会走本地代理的！！！

git remote set-url origin [https://github.com/Hbin-Zhuang/custom-quill-editor.git](https://github.com/Hbin-Zhuang/custom-quill-editor.git)

主要原因就是设置 http.proxy 和 https.proxy  这两个代理

![](images/WEBRESOURCE6d718371b8265937c6e4b278ffc7a83d截图.png)

Windows 操作系统：在“控制面板”中打开“Internet 选项”，然后单击“连接”选项卡。在“局域网设置”中查找代理服务器地址和端口。

![](images/WEBRESOURCEa3271ae37fd6ae61b56e12b1087f1e1b截图.png)

[**git 设置和取消代理**](https://gist.github.com/laispace/666dd7b27e9116faece6#file-git)

|   | git config --global https.proxy  | 
| -- | -- |
|   |   | 
|   | git config --global https.proxy  | 
|   |   | 
|   | git config --global --unset http.proxy | 
|   |   | 
|   | git config --global --unset https.proxy | 
|   |   | 
|   |   | 
|   | npm config delete proxy | 


349、**vite 是不支持 require 语法的 （也就是在 .vue 文件里写 require 会报错的 ！！！）**

  vue2 是 webpack 打包， 默认就支持 require 语法的。。  

vite 中 还可以 使用 process.env.NODE_ENV 变量 （在终端能看到 console.log 的打印，在 node 环境下的） 一般用的都是 import.meta.env 变量  

在 vite.config.ts 文件的话 就是 defineConfig() 传函数 结构出 （{ command, mode, ssrBuild }） 这几个内置变量

350、 nginx 代理 部署项目 如果是部署到根目录下的话，就不用改项目的路径

   如果是部署到子路径下的话，就要改路径了

vite:

![](images/WEBRESOURCEe47059fac3804b4c160a303e4d079248截图.png)

webpack: 

![](images/WEBRESOURCEcb2bf52e5961f4d62e78b64a06e74ea2截图.png)

这样才能避免**访问**该**路径**的时候**项目白屏**！！！！！！！！（而且还是在资源加载正常，控制台没报错的情况下的！）

还有就是nginx也要代理该路径下的静态资源文件，确保线上的路径是正常的！！！

![](images/WEBRESOURCE3b6ab0862d7d5b822c65c1580fff7ff8截图.png)

![](images/WEBRESOURCE8b1a83a770ac4b10ca51eb844d141828截图.png)

这样下来就能正常访问线上项目了！！！！！！！！！

351、我发的第一个 npm 包 （package.json 要 配置 main 入口字段 打包后的路径  dist/umd/xxx.js）

![](images/WEBRESOURCE38a2c650ae39e58dffb929fe476e0001截图.png)

![](images/WEBRESOURCE4a24969634ea2ff1eef807119728d98b截图.png)

![](images/WEBRESOURCEdd62b4cce5eae4441fc17ffb8ed1f7e2截图.png)

352、加强下基础哈~~~：

![](images/WEBRESOURCE0e9ca838edc13f460235b16c34eb8866截图.png)

![](images/WEBRESOURCEbaba626425ecfb57cc26e24c3acd37ca截图.png)

353、浏览器通过JS获取当前设备的IP地址 （每次设备连接互联网时，计算机会分配一个唯一的IP地址）

![](images/WEBRESOURCE0be4c9ddf9abb2ac4825ac668eb48f77截图.png)

**IP地址API服务  **[https://api.ipify.org?format=json](https://api.ipify.org?format=json)** **通过读取发送过去的请求中的请求头信息，可以获取该设备的公网IP地址并返回给客户端设备。

354、文章浏览量的统计 -  IP去重 和 Cookie 去重

（1）、IP的话，同个IP视为同个用户，访问量只加1，但可能出现多个用户使用同一IP的情况

（2）、Cookie的话，是用户登录后，携带请求头发送给服务端做校验，相同Cookie的视为同一用户。（或者不登录，前端写入对应的cookie标识），但是用户清除浏览器Cookie就不能识别了。

355、iife 解决闭包可能导致的内存泄漏， 引用完成，会自动销毁 iife里面的变量。

**function func() {
  var arr = new Array(1000000).fill(1);
  return (function() {
    console.log(arr.length);
    // do something with arr
  })();
}
func(); // 执行完毕后，arr变量被销毁，内存得到释放**

闭包导致内存泄漏，函数最终执行完成后手动释放对自由变量的引用，也不一定能完全保证避免内存泄漏。因为JS引擎的垃圾回收机制是异步的，变量可能被持续引用一段时间，直到垃圾回收器释放它们的内存。iife就不存在这种问题，用完会直接回收掉的。

356、 Docker 讲解 ：   镜像（image） 容器（container      隔离   -   设置        ） 仓库（registry）

namespace：（命名空间   pid 进程id      net 网络    ipc 共享进程通信      mnt 挂载      uts unix分时系统 ） 

cgroups：（control groups  控制分组   做资源分配）

![](images/WEBRESOURCE4f5d1528a563f7a43108c88f9e1d4644截图.png)

union file system （联合文件系统）

container format （容器格式） 上面一系列的组合

Docker 打镜像：	镜像是 静态的、只读的、不可修改的快照        要改的话就要重新打镜像了。

1.  当前目录下需要有 Dockerfile 文件, 以打前端项目为例，要有 Dockerfile  dist  nginx.conf.template  这几个文件

![](images/WEBRESOURCE2c0ec79681e82195843321494561a085截图.png)

1. 然后就可以跑构建镜像的命令了 (docker build -t 镜像名:镜像标签 构建上下文路径, 也就是在哪里找Dockerfile文件,  . 表示当前目录下)

![](images/WEBRESOURCE400c98398ce92045d26b2375816ed133截图.png)

1. 起一个容器跑镜像  (在 docker 中 运行容器)     docker run -d -p 8080:8080 aup-front:v1.0.0

 -d: 守护进程模式, 容器后台运行, 不会阻塞当前终端	

-p: 端口映射, 将主机端口和容器端口做映射

aup-front:v1.0.0： 告诉 Docker 要基于 aup-front 镜像的 v1.0.0 标签创建一个新容器

![](images/WEBRESOURCE3b59fbc85eb712b6c7ac9bb13d581103截图.png)

生成一个 	docker ID 

1. 根据 docker ID 查看容器信息（inspect），管理容器（start / stop），进入容器（exec）

![](images/WEBRESOURCE8976119014589709a7a13c1600844e5e截图.png)

进入容器：  一个容器只能有一个镜像。	容器是镜像的运行时实例

![](images/WEBRESOURCE8cee7eaefbb1ed4b3979ef26d6156537截图.png)

  镜像： 预制的软件包、

  容器： 软件包的运行实例（环境无关）		文件系统、库、应用程序 等

1. 容器编排   docker-compose		docker-swarm			kubernetes (k8s)

1. 大

1. 大大声

357、优化接口响应

![](images/WEBRESOURCEf985a8aef97e551dd9055548463eb5c3截图.png)

接口响应回来的 content download 下载内容时间长 （可能跟前端js代码-主线程阻塞、后端返回数据格式  等 有关）

发现是接口字段的问题（之前是 []   改之后是 null）

![](images/WEBRESOURCE1cf45d1bd285c444f7f1a0f63251e20a截图.png)

 优化后的时间快了非常多

![](images/WEBRESOURCE9cf8ce28ceecd4ed9ded347e4f351f10截图.png)

358、ENOENT  表示 error no entry  表示 “文件或目录不存在“

359、 Array = [] 和 Array.length = 0 

1)、只影响 被赋值[ ]  的数组

![](images/WEBRESOURCE71d7de101a6f4b32a5d7ba14936b7109截图.png)

2)、原数组和引用到的数组都会被影响

![](images/WEBRESOURCEbc7dad2e3d7fdd3f3f58c14dbadc924f截图.png)

360、  input 标签 不能直接加 ::before  和  ::after  ，    会不生效。。   除非 外面套多一层  <label> 标签。

361、Windows删node_modules的方式 (多2个字符)

rm -r -fo node_modules    (吐了  这命令有问题！！！！ 删除的是同级目录下的其他文件 )   也可以用 rimraf ( npm 包 ) 

Mac:  rm -rf node_modules

Win: rd /s /q node_modules	

1. rd 是 remove directory 的缩写，也就是删除目录的意思。

2. /s 参数表示删除指定目录及其中的所有文件及子目录。执行后不会提示“是否确定”的确认信息。

3./q 参数在删除目录前关闭确认询问。（/q 的全称是 quiet，意为安静模式）

362、  浏览器手动进入调试模式。 就是比如你点击按钮后就会进入 debugger ; 

setTimeout(() => {debugger;},2000)

然后在两秒内移上去

363、  AI 相关的单词：

llm (LLM) :  大语言模型  large language model

364、 克隆项目时，有时候项目很大的时候， 可以使用带参数 **--depth 1 **   指定 只下载 最近一次提交的代码，忽略掉早期的历史记录。可以使下载不成功的时候能减少下载量，提高成功率

使用 --depth 1 选项，Git 会只克隆最近的一次commit，忽略掉早期的历史记录。这样可以显著减少克隆仓库所需要的下载量，使得克隆过程更加迅速。这在对历史版本不感兴趣，只需要最新版本的源代码的时候非常有用。

365、yarn 有 yarn --offline 离线包 版本 ，  在没有网络环境/其他的比如行内环境的时候 可以直接使用离线包进行下载。  具体使用如下：

1. “离线镜像”存储目录	

```
yarn config set yarn-offline-mirror ./yarn-offline
```

会将 yarn-offline 文件夹生成到 win 的 我的文档中的用户目录下，  mac 的话就是 /User/zhuanghongbin/yarn-offline， 离线下载的时候就去这里面拿的对应的 gz 包

1. .yarnrc 离线文件夹   yarn config set yarn-offline-mirror-pruning true

移动配置文件到项目路径,以便离线镜像仅用于此项目

cp ~/.yarnrc ./

1.  生成离线文件

rm -rf node_modules/ yarn.lock

yarn install

1. 离线安装，关掉网络，删除node_modules执行下面命令：

yarn --offline

![](images/WEBRESOURCE54a091ad02e851dccc79610c096ad844image.png)

就是说 拿到 一整个 yarn-mirror 文件夹后，要设置下 .yarnrc 的 yarn-offline-mirror 然后 yarn --offline 才会走当前的 离线包 安装。

在这之前先 **yarn cache clean**** 清一下 本地缓存 。**

![](images/WEBRESOURCE4f5714578db1730d7740b238e1a37d3cimage.png)

上面的第 4 点 的 yarn.lock 文件 不能删除的 ！！！！！！

### **其实，  node_modules 包  压缩  然后再打开  跑项目的话 应该是会有点问题的 有些包 会有问题。导致项目报错  跑不起来的。**

所以，还是会用的 yarn offline 的方式 进行的。。。。。。。。。。。。

366、nginx 代理：  有无后缀的区别。   这里就跟本地开发 配置的 vue.config.js 中的

![](images/WEBRESOURCE4c4fde7f3b5391780464b3b313796818image.png)

![](images/WEBRESOURCE2364b57354a93bed2306207b3fa9c941image.png)

有个问题，稍微整理下 nginx 的与** / **相关的几个代理 ：

location

proxy_pass

alias

root

顺带说一句，这种配置是一个容器一个nginx的配置，就是子应用走本地路径，不用单独开server代理一个端口的，那种本质上子应用还是走的http网络请求的。

![](images/WEBRESOURCE6bf16bc26959e96160c124c8b0d8583eimage.png)

![](images/WEBRESOURCE4c20b0293188bd995a48837d2497b119image.png)

另外一种配置一个容器的，是不本质上还是走的2个网络请求的子主应用。

367、         **路由 **

动态路由本质上的话是根据你组件的 name 去查对应的组件的。所以你这名字要写对！！！！ 不然就报错了/。。。

![](images/WEBRESOURCEece6e782ce5dfd128f73ec71f9df51a4image.png)

![](images/WEBRESOURCE04e021660333149e33f28a7c83816669image.png)

认识几个路由参数：

1. router.matcher

**Vue Router 的 **router.matcher** 是 Vue Router 的一个内部属性，主要用来存储和管理路由匹配规则。**

![](images/WEBRESOURCE1296ff627d8a7122f090750bdf3fb26aimage.png)

1. router.options.routes

![](images/WEBRESOURCE1132d3099492a731e0ed7c7829fa0c8fimage.png)

你可以使用这个 router.options.routes 来查看或修改你的路由配置。但是请注意，直接修改它并不会改变已经创建的 Vue Router 实例的行为。如果你希望动态修改路由，你应该使用 

router.addRoutes 或其他 Vue Router 提供的方法。

1. router.addRoutes

router.addRoutes** 方法用于动态的添加更多的路由到已有的路由器实例中。**

![](images/WEBRESOURCEde125e2e3c3a56c4fe48df6b009aaa19image.png)

368、 直接 package.json 里面写死某个依赖的版本(定义 resolutions 字段)，这样就不会因为该依赖的最新版本与本地node版本不兼容（本地node版本过低）而导致依赖安装不成功的报错。

![](images/WEBRESOURCE1caeb41c3bfbd43156fa3efbc0ecccbcimage.png)

369、 淘宝镜像证书过期了，现在用这个：    [https://registry.npmmirror.com](https://registry.npmmirror.com)        

370、   

要是再有这个报错

![](images/WEBRESOURCE224c4c23c9e3b0e28eab59e44388489cimage.png)

对应用到这个组件的子应用那里，

加这个文件：

![](images/WEBRESOURCEdd39c62bd330e87d01ff82f9a0ddd04dimage.png)

然后加多这个插件：  是应该是这几个。。     主要就一个插件嘛：  @vue/cli-plugin-babel

![](images/WEBRESOURCEe310e08f26dd78e10b06b14dddc84460image.png)

报错 core-js 相关的错的话，就是这个@vue/cli-plugin-babel插件关联的，强制执行下升级core-js版本，

```bash
yarn add --dev core-js@3
```

就可以了。

![](images/WEBRESOURCE376740d876e822e67a00e767d36a5514image.png)

就上面这些。     !!!!!!⚠️注意啊啊啊啊：   这里的 babel.config.js 文件 是在 根目录下的，不是在 src 下的啊。不然是报错 解析不到 babel 文件的啊！！！！！！

371、 微前端 常用 解决方案：

0、 iframe 

1、 single - spa

2、阿里 qiankun

3、腾讯 无界

4、京东 micro-app

5、 module-federation 模块联邦

372、 数组修改自己本身的优化：

![](images/WEBRESOURCEf10523a142a6a2985b60667043a4e174image.png)

373、 访问服务器上的项目： 

![](images/WEBRESOURCE667c6531da003316e03642093d4ba541image.png)

要是访问不了，那就是服务器需要设置防火墙，允许访问 8080 端口的。

   sudo firewall-cmd --zone=public --add-port=8080/tcp --permanent

   sudo firewall-cmd --reload

贴上面这2条命令上去 就可以了。

![](images/WEBRESOURCE170bfe3aee6e239338b5629478d0042bimage.png)

374、 v-bind 跟 单独定义的属性， 单独定义的属性 优先级更高。 不关定义的先后顺序。

![](images/WEBRESOURCEd183dfc2c41cb17e68a4bfa365e8c9a0image.png)

这里就是 propsData 有 type，

![](images/WEBRESOURCE29a0983e060e6f69bb5b21533339a9c1image.png)

但是优先级还是 单独的 type 更高。不会走 propsData 的 type

375、  PC 端（还有移动端，好像是app 里自定义好协议然后 url 输入就能打开了？）网页浏览器打开本地应用（客户端）：网上看了4天。

一般的软件都会默认给你在注册表中插入对应的 URL Scheme ，所以直接搜他的键就能在浏览器打开的。比如咱的 vscode，在安装的时候就会顺带在注册表中注入对应的 URL Scheme 信息：

比如在windows上的注册表中能查到：

![](images/WEBRESOURCEb220db9213f5e2c8d4fd38aca26b3646image.png)

比如在macos的 Info.plist 配置文件中能查到：

![](images/WEBRESOURCE450c296d8e7b1235359089b452037643image.png)

但是呢，不是所有的应用都会有的，所以对于没有的应用，就得自己手动进行配置了，比如在：

**windows: **     

 在注册表中配置        具体就是 新建一个 txt文档：

![](images/WEBRESOURCE7dbfd3003c495c0eddb919d231eaaa06image.png)

这里的路径如果没写入的话，可能**需要用双斜杆 // 而不是单斜杆 / ，  如果有双引号""  则需要使用转义符 反斜杆 \ 进行转义**。

下面的地址用这个：

**@="\"C:\\Users\\<USER>\\Downloads\\AgreeStudio-win-20240907-1438\\AgreeStudio.exe\" "\%1\""**

这样就能正确写入 了。

![](images/WEBRESOURCE2c4e745ea45a872b9db14865f7fc9c2bimage.png)

![](images/WEBRESOURCE08239619ae5390c4bbe508789bf82ffcimage.png)

然后修改后缀名为 reg ：

![](images/WEBRESOURCE0c96d0657efc6b02d856a83e75aead42image.png)

然后双击运行

![](images/WEBRESOURCEb0423080caf5a5cc78308b1cff9dfb34image.png)

![](images/WEBRESOURCE54e090f50332aff0a062c4c866fc6ea3image.png)

![](images/WEBRESOURCEe6cf55cd9bcf8ec69026e6b1544212beimage.png)

然后打开注册表就能搜到了对应的URL Scheme定义信息。

![](images/WEBRESOURCE18d93c3b7ee372cfe935164ddf818741image.png)

然后浏览器就能打开了：

![](images/WEBRESOURCE396e481fc9f1413d49ada329038c47b3image.png)

在macos:        ** URL Scheme** 方式实现。修改  ** ****Info.plist	**文件：主要是要加这个 **CFBundleURLSchemes **

![](images/WEBRESOURCE1fa27da43d7055db3d5a66868cb4df89image.png)

下载的本地app需要签名应用，不然就算改了 Info.plist（ 应用的配置文件，显示版本信息，提供应用在运行期的一些配置。操作系统通过这个文件判定依赖关系和其他属性 ） 文件也不行的。

通过这样配置（这段加在 CFBundleShortVersionString 和 CFBundleVersion 之间 就可以了。）

![](images/WEBRESOURCE6628180a5ad920205a2df67a230bff9bimage.png)

然后浏览器输入配置的这个 URL Scheme 就可以了：  ** abx:// ** 或者  **abx://open**  或者** ****ab:// ** 或者 **ab://open**

![](images/WEBRESOURCEcccd698eed2c3ea36731627e7ad46fc8image.png)

但是呢？ 前端想要实现点击按钮就能在网页中弹窗打开对应的本地应用，这个怎么搞？？有没得搞头？ （实现就是简单的通过 window.location.href = '**vscode://open**' 这样，主要问题还是这个 url 要怎么获取？？？）

通过 node搭建一个本地服务 或者 通过 Electron 可以访问本地应用 来实现呢。  Qt 也可以实现 。  c# 程序也能修改。		**最简单的就是安装项目的时候应用本身提供支持**

还有就是如果是本地没有安装对应的软件的话，如何进行拦截 然后提示 跳转去下载对应的应用 进行安装？？？ （setTimeout定时器？）

然后还有在 唤起的情况下 ，  怎么去给应用进行参数 传递呢 ？ 

这些都是需要 后端配合 进行实现的 。 

376、IntersectionObserver  和 MutationOberver 

378、  M1 nvm 安装 node 低版本 （14 及以下）报错问题：

低版本的 Node 并不是基于 arm64 架构的（基于x86），所以不适配 M1 芯片。

解决： 2 条命令搞定。        

arch -x86_64 zsh

nvm install v14 （默认安装最新版本 14.21.3）

![](images/WEBRESOURCE70d8de5c275baaa60daeb037ea24f9bdimage.png)

![](images/WEBRESOURCEe6ce7ee25b99847321109345521315beimage.png)

379、字间距 letter-spacing 

没设置前：

正常情况下的字体间距：

![](images/WEBRESOURCE68bf742822d62c0851e548fbd29cd05eimage.png)

设置了字间距：

![](images/WEBRESOURCE35f0884707e9b302f4d03d76fc855288image.png)

这样就更明显了：

![](images/WEBRESOURCEddf7b3d0ac3eb0bedc9bc71fcdafab9eimage.png)

380、 JavaScript 针对 **生僻字** 的处理：js默认采用utf-16 编码

codeAt(index)：返回指定索引的字符

- 特点 ：

	- 只能返回单个字符，无法处理复杂的 Unicode 字符（如表情符号）。

	- 不涉及编码值，仅返回字符本身。

charCodeAt(index)：返回指定索引位置字符的 UTF-16 编码值。

- 特点 ：

	- 只能处理单个 UTF-16 代码单元。

	- 对于非 BMP（基本多语言平面）之外的字符（如表情符号），只能返回代理对的第一个部分。

**codePointAt**(index)：返回指定索引位置字符的完整 Unicode 编码值（包括非 BMP 字符）。

- 特点 ：

	- 能够正确处理非 BMP 字符（如表情符号）。

	- 返回的是完整的 Unicode 码点值，而不仅仅是 UTF-16 代码单元。

381、**Vue.directive** 自定义指令： 直接操作 DOM 元素

- 关注点分离: 指令的主要目的是封装 DOM 操作逻辑，将其与组件逻辑分离。

- 可复用性: 定义好的指令可以在项目中的任何组件模板里方便地复用。

- 底层访问: 提供了直接访问和操作 DOM 元素的能力。

方法：  bind  inserted  update  componentUpdated  unbind 

1. bind(el, binding, vnode): 只调用一次，在指令**第一次绑定到元素时**调用。

1. inserted(el, binding, vnode): 在被绑定元素**插入父节点时**调用 (仅保证父节点存在，但不一定已被插入文档中)。

1. update(el, binding, vnode, oldVnode): 所在组件的 VNode **更新时**调用，**但是可能发生在其子 VNode 更新之前**。指令的值可能发生了改变，也可能没有。

1. componentUpdated(el, binding, vnode, oldVnode): 所在组件的 VNode **及其子 VNode 全部更新后**调用。

1. unbind(el, binding, vnode): 只调用一次，在指令**与元素解绑时**调用。

![](images/WEBRESOURCE3829a41b02a41e9201bbd7521f3311d3image.png)

![](images/WEBRESOURCE367c912656d21b1305280850147cd1a5image.png)

382、vite-plugin-federation        Vite 模块联邦

- 去中心化

- 共享模块和代码

- 无需重复构建打包

383、 使用 git-filter-repo 修改仓库提交信息。（改提交人）  ----  前提自己仓库/有权限操作

 1. pip install git-filter-repo

2. pip install pygit2

3.

 git filter-repo --commit-callback '

if commit.author_email == b"<EMAIL>":

commit.author_name = b"Hbin-Zhuang"

commit.author_email = b"<EMAIL>"

if commit.committer_email == b"<EMAIL>":

commit.committer_name = b"Hbin-Zhuang"

commit.committer_email = b"<EMAIL>"

' --force

4. git push -u github main --force

![](images/WEBRESOURCEf42037f08f50e1fc0fd65106d1fdbdb1image.png)

![](images/WEBRESOURCE04ac208f3f881192b5525f70dba395a7image.png)

**但是原来账号创建的分支还有打得tang标签等都会给删了，还是谨慎玩的好**

384、astro 使用有个问题，就是默认使用的是ssr（client:load 指令），服务端渲染默认返回的图片logoSrc 是空串 " "，浏览器接收的时候自动补全了默认值。

   客户端进行 hydration  **水合 ** 的时候， Vue 更新了 logoSrc 的值，但是  DOM 的src已经被浏览器 “固化” 了。

解决就是强制在客户端重新设置 src 属性。设置 img的src，而不是设置 logoSrc

![](images/WEBRESOURCE38bacd56a01efbd54f6219bcdd8cdaddimage.png)

385、

386、