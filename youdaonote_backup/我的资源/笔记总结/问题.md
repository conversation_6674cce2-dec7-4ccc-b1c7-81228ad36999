1、验证码登录注册是同个东西，没有分开？

是的。就是同个东西，是在后端做判断的，如果账号存在的话就直接正常显示，如果是新账号就是没有企业信息，就还是要引导创建企业  

           =>   密码登录的跟验证码登录时同个接口，验证码注册的一个接口

2、



登录注册问题有点多：

1、已有账号，验证码登录：第一次获取验证码失败后，再次点击获取验证码就会一直提示 输入的验证码不正确（要刷新才行）

2、已有账号，验证码登录：输入123456或没有点获取验证码  

提示账号已存在

3、验证码登录

1）如果是已存在账号，输验证码 直接跳首页（有企业信息的）

2）如果是新账号，验证码登录直接进入创建企业流程



正则表达式之间不能有空格，不然会有问题







BUG:

1、每次退出登录后再登录进去是默认重定向到推出前最后一次停留的位置，如果是创建企业页面的话那么不管是不是新账号，再次登录都会跳转到这个页面

// 不在登录页面,并且没有用户信息有企业项目信息,重定向到首页

if (!isEmpty(userInfo) && !isEmpty(enterpriseInfo) && !checkPath) {

          next({ path: '/home' })

          NProgress.done()

        }

2、已有账号在地址栏（window.location）中输入/createenterprise会进入创建企业页面

3、进入访客管理路由，每次刷新页面后默认该路由状态未被激活，只有点击切换其他tabs路由时才会激活该路由

4、验证码登录输入手机号后退出再登录，应该要有个记住手机号的功能，方便快速登录 （autocomplete未生效？）



利用cookie记住密码，



```javascript
   <el-form
              ref="form"
              :model="form"
              label-width="80px"
              label-position="top"
              @submit.native.prevent
            >
              <el-form-item label="用户名/账号">
                <div class="userError">
                  <el-input
                    size="mini"
                    v-model.trim="form.userName"
                    clearable
                  ></el-input>
                </div>
              </el-form-item>
              <el-form-item label="密码">
                <div class="pwdError">
                  <el-input
                    size="mini"
                    v-model.trim="form.loginPwd"
                    clearable
                    show-password
                  ></el-input>
                </div>
              </el-form-item>
              <el-checkbox label="记住账号" v-model="isRemember"></el-checkbox>
              <el-button native-type="submit" size="mini" @click="loginPage"
                >登录</el-button
              >
 </el-form>
```



```javascript
js部分
export default {
  name: "login",
  data() {
    return {
      form: {
        userName: '',
        loginPwd: '',
      },
      isRemember: false,
    };
  },
  mounted() {
    // 第1步，在页面加载的时候，首先去查看一下cookie中有没有用户名和密码可以用
    this.getCookie();
  },
  methods: {
    /*  第3步，当用户执行登录操作的时候，先看看用户名密码对不对
              若不对，就提示登录错误
              若对，就再看一下用户有没有勾选记住密码
                    若没勾选，就及时清空cookie，回到最初始状态
                    若勾选了，就把用户名和密码存到cookie中并设置7天有效期，以供使用
                      （当然也有可能是更新之前的cookie时间）
    */
    async loginPage() {
      // 发请求看看用户输入的用户名和密码是否正确
      const res = await this.$api.loginByUserName(this.form)
      if(res.isSuccess == false){
        this.$message.error("登录错误")
      }
      else{
        const self = this;
        // 第4步，若复选框被勾选了，就调用设置cookie方法，把当前的用户名和密码和过期时间存到cookie中
        if (self.isRemember === true) {
          // 传入账号名，密码，和保存天数（过期时间）3个参数
          //  1/24/60 测试可用一分钟测试，这样看着会比较明显
          self.setCookie(this.form.userName, this.form.loginPwd, 1/24/60);
          // self.setCookie(this.form.userName, this.form.loginPwd, 7); // 这样就是7天过期时间
        } 
        // 若没被勾选就及时清空Cookie，因为这个cookie有可能是上一次的未过期的cookie，所以要及时清除掉
        else {
          self.clearCookie();
        }
        // 当然，无论用户是否勾选了cookie，路由该跳转还是要跳转的
        this.$router.push({
          name: "project",
        });
      }
    },
    // 设置cookie
    setCookie(username, password, exdays) {
      var exdate = new Date(); // 获取当前登录的时间
      exdate.setTime(exdate.getTime() + 24 * 60 * 60 * 1000 * exdays); // 将当前登录的时间加上七天，就是cookie过期的时间，也就是保存的天数
      // 字符串拼接cookie,因为cookie存储的形式是name=value的形式
      window.document.cookie = "userName" + "=" + username + ";path=/;expires=" + exdate.toGMTString();
      window.document.cookie = "userPwd" + "=" + password + ";path=/;expires=" + exdate.toGMTString();
      window.document.cookie = "isRemember" + "=" + this.isRemember + ";path=/;expires=" + exdate.toGMTString();
    },
    // 第2步，若cookie中有用户名和密码的话，就通过两次切割取出来存到form表单中以供使用，若是没有就没有
    getCookie: function () {
      if (document.cookie.length > 0) {
        var arr = document.cookie.split("; "); //因为是数组所以要切割。打印看一下就知道了
        // console.log(arr,"切割");
        for (var i = 0; i < arr.length; i++) {
          var arr2 = arr[i].split("="); // 再次切割
          // console.log(arr2,"切割2");
          // // 判断查找相对应的值
          if (arr2[0] === "userName") {
            this.form.userName = arr2[1]; // 转存一份保存用户名和密码
          } else if (arr2[0] === "userPwd") {
            this.form.loginPwd = arr2[1];//可解密
          } else if (arr2[0] === "isRemember") {
            this.isRemember = Boolean(arr2[1]);
          }
        }
      }
    },
    // 清除cookie
    clearCookie: fu![image](/img/bVcOHhz)
      this.setCookie("", "", -1); // 清空并设置天数为负1天
    },
  },
};
```



```javascript
其实也很简单，就是设置一个过期时间，也就是cookie的失效的日期，当然中间需要有一些格式的处理，数据的加工。
补充，cookie是存在浏览器中，浏览器安装在电脑中，比如安装在C盘，所以cookie是存在C盘中的某个文件夹下，那个文件夹不仅有cookie，还有localStorage和sessionStorage和别的
```



5、我的审核确认进场离场时间显示

6、修改密码模块没有做

7、错误码判断哪种情况修改后台返回的错误信息

8、预约/进场/离场时间不能小于当前时间（时间选择器要做限制），后台也得做判断

9、创建企业成功后再返回去又是创建企业那里      （路由导航守卫控制不了的可以在createenterprise页面进行设置，就在createenterprise这一个页面并且有企业信息的路由是这一个就跳转到首页）





10、企业部门默认不可删除，判断管理员/超级管理员有权限删除，本地存储中的ROLES判断

![](images/WEBRESOURCE314d7e080ffc9819cbc56bd5ca38d5db截图.png)



11、首页待办事项的接口如果是新建企业接口返回的data就是 null ，这个时候要判断不为 null，不然会报错

12、Header的管理员头像显示，当前管理员信息在当前企业员工中查找不到，无法获取其头像信息，只能是获取对应的企业头像icon或者背景图pic（企业信息logo）

13、图片地址给相对路径不能显示，让后端返回绝对路径

14、进入到新建企业的页面，在地址栏输入/login可以跳转到登录页

15、用户协议/隐私协议 PDF形式   首页帮助中心  PDF形式

16、登陆二维码还有一种情况没考虑，就是得是二维码刷新出来的情况下才会轮询，不然的话就是二维码刷新失败的话要显示刷新失败的页面，然后点击重新刷新（跟失效的情况是一样的），然后注册二维码还没搞上去，找时间优化下，抽成组件处理，减少大量冗余代码。

还有整个二维码登陆注册的逻辑要整理一下，写成文字，化成图，贴代码理解。

17、旧数据无法显示，不是代码问题，需要清数据。



18、全局装pdf无效

19、组件内路由无效



20、存cookie上的密码要加密



21、简单的问题不要把逻辑复杂化

22、预约详情审批流只要一级审批通过就是直接通过，不用走二级审批，这样不对，代码有问题



移动端：：：



1、访客为到达状态后上自己账号查不到信息，无法离场

2、二级审批完成没有显示已通过文案，对应图标也没有变色

3、一级审核拒绝后，二级审核还是显示待审核状态

![](images/WEBRESOURCEad3c924de4a9ce8fb48c8b64e3ad8187截图.png)

4、访客预约过不能再预约，后台没判断

5、访客预约详情visitor-detail中通过props传拒绝原因head.param.reason到审批流程子组件中，子组件能接收到数据，刷新页面后就不见了？

6、子组件中可以直接调接口，打印信息嘛？为什么我不行？是不是哪里有问题？







7、审批流通过拒绝按钮有问题 ---    管理后台

![](images/WEBRESOURCE4aaac047f058de6db07379b8d09d2364截图.png)







样式相关：

1、pi-ui常用的css样式有些不熟悉的，要去看源码有没有这些属性吗？  比如说圆角border-radius，自定义定位的top/right/left/bottom，display: inline-block/none，background，自定义样式内容等

//  github上大部分有，看piui源码的common.scss文件













把所有的审核情况全部列出来        



1、 当前审核状态为拒绝时，后面的审核状态置灰

2、最后一级审核同意/拒绝后，前面层级的审核状态要保留

3、备注信息显示：（要求：只有在拒绝的时候下显示备注信息）

（1）同意拒绝有备注信息       /         同意有， 拒绝没有： 不是第一级审批拒绝的话所有层级的备注信息均会显示

（2）同意没有，拒绝有  ：正常显示









颜色状态   4种  待审核、拒绝、通过





写一个新数组，用计算属性，用map表示，处理这些判断条件，页面不做判断，直接显示数据













如果是待审核，找到当前待审核的index，上面正常显示，下面圆点置灰，状态不显示

如果是全部通过，正常流程走

如果是拒绝，找到当前拒绝的index，上面正常显示，下面圆点置灰，状态不显示









待审核



拒绝



通过













2、三级审核：

（1）

 一级审核通过显示正常（有备注信息），

 二级审核通过显示正常（有备注信息），   ----------点击确认同意/拒绝按钮会循环显示两次预约信息卡片内容 （刷新后正常显示）

 三级审核通过显示错误（有备注信息）：第三级的审核状态显示在第一级，原先第一第二级的审核状态被覆盖     ----------点击确认同意/拒绝按钮会循环显示两次预约信息卡片内容 （刷新后正常显示）

 三级审核拒绝显示错误（有备注信息）：所有备注信息均显示；只有第一级审核显示拒绝，其他剩余层级审核状态均不显示



（2）

 一级审核通过显示正常（有备注信息），

 二级审核拒绝显示错误（有备注信息）：第一、第二级审批均显示拒绝，所有备注信息均显示

 三级审核拒绝显示错误                 显示待审核



（3）

 一级审核拒绝显示错误（有备注信息）：当前层级信息显示正确，要求：剩下层级不显示

 二级审核拒绝显示错误                 显示待审核

 三级审核拒绝显示错误 





二级审核：

（1）

 一级审核通过显示正常（有备注信息），

 二级审核通过显示正常（有备注信息），



（2）

 一级审核通过显示正常（有备注信息），

 二级审核拒绝显示错误（有备注信息）：第一、第二级审批均显示拒绝，所有备注信息均显示





（3）

 一级审核通过显示正常（有备注信息），

 二级审核拒绝显示错误（有备注信息）：第一、第二级审批均显示拒绝，所有备注信息均显示

































    问一下补贴相关问题





小问题：

1、企业信息logo头像显示问题，调大小，去el-dialog下边框

2、创建预约头像组件el-upload显示问题

3、登录注册可回车登录

4、el-tag设置 effect= "plain" 不生效，是不是element版本问题

5、我的预约页面审核/到达状态列固定宽度，拉长时间列

6、企业员工编辑用户头像要可编辑，

7、角色管理遍历获取不到roleExplain角色描述





代码优化：

1、二维码登录注册：怎么设置定时器在规定时间内（5分钟）只点击一次有效，多次点击无效（防抖？）











修改详情页显示传date menuid 查 数据

当前学生选过的要记录选过的信息

当前时间之前的禁用显示

修改订单进入订餐页调接口



订餐详情进入：支付有问题（拿不到vuex中的payParams参数）



上传图片组件换一下

订餐详情页不显示合计底部状态栏

支付时按钮防抖

绑定家长老师那里的学校选择器要改一下popup-select

选餐不按顺序来 应该是选完所有餐次才跳弹窗，而不是在选完最后一餐就跳



点提交的时候 setUni 到 localStorage，

下次再回来判断（参考visitorForm）





点击 push  在 push 之前做判断（去重）：

1、如果没有，就直接push进去

2、如果有，先找到对应的日期和餐次做判断，如果已经存在，则替换原来的下标

3、最后就是对应的每一天的每一餐只有一次

4、本周可订餐需要 可修改（可点击）

5、首页刷新的时候绑定账号按钮不显示







1、订餐详情不同用户的订餐显示问题



![](images/WEBRESOURCE93ede299b62d76cece70bcc27c368599截图.png)



![](images/WEBRESOURCE771b20a0fc129a1adcde999c78d5689e截图.png)





合计问题   

 



![](images/WEBRESOURCEe5ca3c92bf70180ff88f2dedce33af39截图.png)





  pi-fw-600  在真机上只有数字生效？  font-weight: bold; 才可以 ？啥情况？ 





member-item使用slot插槽进行编写修改订餐

需要解决 date 报错  和  find 报错问题





1、在子账号的页面点击每个账号里的订餐的时候判断有没有上传图片（pic是否为空），没有的话就先跳转上传图片那里

2、绑定成员后进入下一个页面把当前页清空再跳到上传人脸页(relaunchTo)，这样在人脸页返回上一页就不会到绑定成员页（提示已绑定的信息）  账号信息进入人脸页也是同样操作

3、订餐详情用当前账号的id 去查 数据