#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单图片路径替换工具
作者: Claude 4.0 sonnet
功能: 将Markdown中的网络图片链接替换为本地images路径
"""

import re
import os

def replace_image_paths(input_file, output_file):
    """替换图片路径"""
    try:
        # 读取文件
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"原始文件大小: {len(content)} 字符")
        
        # 统计原始图片数量
        original_pattern = r'!\[([^\]]*)\]\((https://note\.youdao\.com/yws/res/\d+/(WEBRESOURCE[a-f0-9]+))\)'
        original_matches = re.findall(original_pattern, content)
        print(f"找到 {len(original_matches)} 张网络图片")
        
        # 替换图片链接
        def replace_func(match):
            alt_text = match.group(1)
            full_url = match.group(2)
            resource_id = match.group(3)
            
            # 新的本地路径
            local_path = f"images/{resource_id}.png"
            
            # 如果alt_text为空，使用默认描述
            if not alt_text or alt_text == "图片":
                alt_text = "图片"
            
            return f"![{alt_text}]({local_path})"
        
        # 执行替换
        new_content = re.sub(original_pattern, replace_func, content)
        
        # 统计替换后的图片数量
        local_pattern = r'!\[([^\]]*)\]\(images/WEBRESOURCE[a-f0-9]+\.png\)'
        local_matches = re.findall(local_pattern, new_content)
        print(f"替换为 {len(local_matches)} 张本地图片")
        
        # 写入新文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ 替换完成: {output_file}")
        print(f"新文件大小: {len(new_content)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 替换失败: {e}")
        return False

def main():
    """主函数"""
    input_file = "youdaonote/我的资源/笔记总结/JavaScript知识点大杂烩_简单提取版.md"
    output_file = "youdaonote/我的资源/笔记总结/JavaScript知识点大杂烩_本地图片版.md"
    
    print("🖼️ 开始替换图片路径...")
    
    if replace_image_paths(input_file, output_file):
        print("\n🎉 图片路径替换成功！")
        print(f"📁 输出文件: {output_file}")
        print("\n📋 使用说明:")
        print("1. 确保images文件夹与Markdown文件在同一目录")
        print("2. 图片文件名格式: WEBRESOURCE*.png")
        print("3. 使用支持相对路径的Markdown预览器查看")
    else:
        print("❌ 图片路径替换失败！")

if __name__ == "__main__":
    main()
