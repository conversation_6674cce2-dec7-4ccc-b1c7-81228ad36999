#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大型.note文件拆分工具
作者: Claude 4.0 sonnet
功能: 将大型有道云笔记文件拆分为多个小文件，然后转换为Markdown
"""

import html
import logging
import os
import re
import xml.etree.ElementTree as ET
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class LargeNoteSplitter:
    """大型笔记文件拆分器"""
    
    def __init__(self, max_elements_per_file=50):
        self.max_elements_per_file = max_elements_per_file
        self.namespace = {'note': 'http://note.youdao.com'}
    
    def parse_note_file(self, file_path):
        """解析.note文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析XML
            root = ET.fromstring(content)
            return root
        except Exception as e:
            logging.error(f"解析文件失败: {e}")
            return None
    
    def extract_text_from_element(self, element):
        """从元素中提取文本内容"""
        text_elem = element.find('.//text')
        if text_elem is not None and text_elem.text:
            return html.unescape(text_elem.text)
        return ""
    
    def get_heading_level(self, element):
        """获取标题级别"""
        level_attr = element.get('level')
        if level_attr:
            return int(level_attr)
        return 1
    
    def process_image(self, element):
        """处理图片元素"""
        source_elem = element.find('.//source')
        if source_elem is not None and source_elem.text:
            image_url = source_elem.text
            return f"![图片]({image_url})\n\n"
        return ""
    
    def element_to_markdown(self, element):
        """将单个元素转换为Markdown"""
        if element.tag == 'heading':
            text = self.extract_text_from_element(element)
            if text.strip():
                level = self.get_heading_level(element)
                return f"{'#' * level} {text.strip()}\n\n"
            else:
                return "\n"
        
        elif element.tag == 'para':
            text = self.extract_text_from_element(element)
            if text.strip():
                return f"{text.strip()}\n\n"
            else:
                return "\n"
        
        elif element.tag == 'image':
            return self.process_image(element)
        
        return ""
    
    def create_markdown_header(self, part_num, total_parts, original_filename):
        """创建Markdown文件头部"""
        # 从文件名中提取时间信息
        time_match = re.search(r'\((\d{4}-\d{2}-\d{2})\s+(\d{2})(\d{2})\)', original_filename)
        if time_match:
            date_str = time_match.group(1)
            hour_str = time_match.group(2)
            minute_str = time_match.group(3)
            full_time_str = f"{date_str} {hour_str}:{minute_str}:00"
        else:
            full_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        header = f"""<!--
📝 笔记信息
创建时间: {full_time_str}
修改时间: {full_time_str}
原始文件: {original_filename}
数据来源: 有道云笔记导出
文件分片: 第{part_num}部分，共{total_parts}部分
-->

# 知识点大杂烩 - 第{part_num}部分

"""
        return header
    
    def split_and_convert(self, input_path):
        """拆分并转换文件"""
        if not os.path.exists(input_path):
            logging.error(f"输入文件不存在: {input_path}")
            return False
        
        # 解析文件
        root = self.parse_note_file(input_path)
        if root is None:
            return False
        
        # 查找body元素
        body = root.find('.//body')
        if body is None:
            logging.error("未找到body元素")
            return False
        
        # 获取所有元素
        elements = list(body)
        total_elements = len(elements)
        
        if total_elements == 0:
            logging.error("文件中没有内容元素")
            return False
        
        # 计算需要拆分的文件数量
        total_parts = (total_elements + self.max_elements_per_file - 1) // self.max_elements_per_file
        
        logging.info(f"文件包含 {total_elements} 个元素，将拆分为 {total_parts} 个文件")
        
        # 获取原始文件信息
        original_filename = os.path.basename(input_path)
        base_dir = os.path.dirname(input_path)
        base_name = os.path.splitext(original_filename)[0]
        
        # 拆分文件
        for part_num in range(1, total_parts + 1):
            start_idx = (part_num - 1) * self.max_elements_per_file
            end_idx = min(start_idx + self.max_elements_per_file, total_elements)
            
            # 创建Markdown内容
            markdown_lines = []
            
            # 添加头部
            header = self.create_markdown_header(part_num, total_parts, original_filename)
            markdown_lines.append(header)
            
            # 处理元素
            for i in range(start_idx, end_idx):
                element = elements[i]
                markdown_content = self.element_to_markdown(element)
                if markdown_content:
                    markdown_lines.append(markdown_content)
            
            # 生成输出文件名
            output_filename = f"{base_name}_第{part_num}部分.md"
            output_path = os.path.join(base_dir, output_filename)
            
            # 写入文件
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(''.join(markdown_lines))
                
                logging.info(f"✅ 创建文件: {output_filename} (元素 {start_idx+1}-{end_idx})")
                
            except Exception as e:
                logging.error(f"❌ 写入文件失败 {output_filename}: {e}")
                return False
        
        logging.info(f"🎉 拆分完成！共创建 {total_parts} 个Markdown文件")
        return True

def main():
    """主函数"""
    splitter = LargeNoteSplitter(max_elements_per_file=30)  # 每个文件最多30个元素
    
    # 处理指定文件
    input_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).note"
    
    if splitter.split_and_convert(input_file):
        print("✅ 文件拆分和转换完成！")
    else:
        print("❌ 文件拆分和转换失败！")

if __name__ == "__main__":
    main()
