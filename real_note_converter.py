#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的有道云笔记转Markdown工具
作者: Claude 4.0 sonnet
功能: 真正解析XML内容并转换为可读的Markdown格式
"""

import html
import logging
import os
import re
import xml.etree.ElementTree as ET
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class RealNoteConverter:
    """真正的有道云笔记转换器"""
    
    def __init__(self):
        self.namespace = {'note': 'http://note.youdao.com'}
    
    def parse_note_file(self, file_path):
        """解析.note文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析XML
            root = ET.fromstring(content)
            return root
        except Exception as e:
            logging.error(f"解析文件失败: {e}")
            return None
    
    def extract_text_from_element(self, element):
        """从元素中提取纯文本内容"""
        text_elem = element.find('.//text')
        if text_elem is not None and text_elem.text:
            # 解码HTML实体
            text = html.unescape(text_elem.text)
            # 清理多余的空白字符
            text = re.sub(r'\s+', ' ', text).strip()
            return text
        return ""
    
    def get_heading_level(self, element):
        """获取标题级别"""
        level_attr = element.get('level')
        if level_attr:
            return min(int(level_attr), 6)  # Markdown最多支持6级标题
        return 1
    
    def process_image(self, element):
        """处理图片元素"""
        source_elem = element.find('.//source')
        if source_elem is not None and source_elem.text:
            image_url = source_elem.text
            # 提取图片文件名作为alt文本
            alt_text = os.path.basename(image_url)
            return f"![{alt_text}]({image_url})\n\n"
        return ""
    
    def process_styles(self, element):
        """处理样式信息，转换为Markdown格式"""
        text = self.extract_text_from_element(element)
        if not text:
            return ""
        
        # 检查是否有样式信息
        inline_styles = element.find('.//inline-styles')
        if inline_styles is None:
            return text
        
        # 处理粗体
        bold_elements = inline_styles.findall('.//bold')
        if bold_elements:
            for bold in bold_elements:
                from_pos = int(bold.find('from').text) if bold.find('from') is not None else 0
                to_pos = int(bold.find('to').text) if bold.find('to') is not None else len(text)
                if bold.find('value') is not None and bold.find('value').text == 'true':
                    # 简单处理：如果整个文本都是粗体，就加粗
                    if from_pos == 0 and to_pos >= len(text) - 5:
                        text = f"**{text}**"
        
        # 处理颜色标记（转换为代码块或强调）
        color_elements = inline_styles.findall('.//color')
        back_color_elements = inline_styles.findall('.//back-color')
        
        # 如果有特殊颜色或背景色，可能是代码或重要内容
        if color_elements or back_color_elements:
            # 检查是否像代码
            if any(keyword in text.lower() for keyword in ['function', 'var', 'let', 'const', '()', '{', '}', ';']):
                text = f"`{text}`"
            elif len(text) < 50 and any(char in text for char in ['=', '>', '<', '!', '&']):
                text = f"`{text}`"
        
        return text
    
    def convert_to_markdown(self, root):
        """将XML转换为真正的Markdown"""
        markdown_lines = []
        
        # 查找body元素
        body = root.find('.//body')
        if body is None:
            logging.error("未找到body元素")
            return ""
        
        for element in body:
            if element.tag == 'heading':
                # 处理标题
                text = self.process_styles(element)
                if text.strip():
                    level = self.get_heading_level(element)
                    # 清理标题文本
                    clean_text = re.sub(r'[*`]', '', text).strip()
                    if clean_text:
                        markdown_lines.append(f"{'#' * level} {clean_text}\n\n")
                else:
                    # 空标题，添加分隔线
                    markdown_lines.append("---\n\n")
            
            elif element.tag == 'para':
                # 处理段落
                text = self.process_styles(element)
                if text.strip():
                    # 检查是否是代码块
                    if self.is_code_block(text):
                        markdown_lines.append(f"```\n{text}\n```\n\n")
                    else:
                        markdown_lines.append(f"{text}\n\n")
                else:
                    # 空段落，添加空行
                    markdown_lines.append("\n")
            
            elif element.tag == 'image':
                # 处理图片
                image_md = self.process_image(element)
                if image_md:
                    markdown_lines.append(image_md)
        
        return ''.join(markdown_lines)
    
    def is_code_block(self, text):
        """判断文本是否应该作为代码块"""
        code_indicators = [
            'function', 'var ', 'let ', 'const ', 'return',
            'if (', 'for (', 'while (', 'switch (',
            '=>', '===', '!==', '&&', '||',
            'console.log', 'document.', 'window.',
            '{', '}', ';', '//', '/*'
        ]
        
        # 如果包含多个代码特征，认为是代码块
        code_count = sum(1 for indicator in code_indicators if indicator in text)
        return code_count >= 2 or (len(text) > 50 and code_count >= 1)
    
    def add_timestamp_header(self, markdown_content, original_filename):
        """添加时间戳头部"""
        # 从文件名中提取时间信息
        time_match = re.search(r'\((\d{4}-\d{2}-\d{2})\s+(\d{2})(\d{2})\)', original_filename)
        if time_match:
            date_str = time_match.group(1)
            hour_str = time_match.group(2)
            minute_str = time_match.group(3)
            full_time_str = f"{date_str} {hour_str}:{minute_str}:00"
        else:
            full_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        header = f"""<!--
📝 笔记信息
创建时间: {full_time_str}
修改时间: {full_time_str}
原始文件: {original_filename}
数据来源: 有道云笔记导出
-->

# 知识点大杂烩

> 这是从有道云笔记导出的JavaScript知识点整理

"""
        return header + markdown_content
    
    def convert_file(self, input_path, output_path=None):
        """转换单个文件"""
        if not os.path.exists(input_path):
            logging.error(f"输入文件不存在: {input_path}")
            return False
        
        # 解析文件
        root = self.parse_note_file(input_path)
        if root is None:
            return False
        
        # 转换为Markdown
        markdown_content = self.convert_to_markdown(root)
        
        # 添加时间戳头部
        original_filename = os.path.basename(input_path)
        markdown_content = self.add_timestamp_header(markdown_content, original_filename)
        
        # 确定输出路径
        if output_path is None:
            base_name = os.path.splitext(input_path)[0]
            output_path = f"{base_name}_真正转换.md"
        
        # 写入Markdown文件
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            logging.info(f"转换成功: {input_path} -> {output_path}")
            return True
            
        except Exception as e:
            logging.error(f"写入文件失败: {e}")
            return False

def main():
    """主函数"""
    converter = RealNoteConverter()
    
    # 转换拆分后的文件
    input_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1)_第1部分.note"
    output_file = "youdaonote/我的资源/笔记总结/知识点大杂烩_真正转换.md"
    
    if converter.convert_file(input_file, output_file):
        print(f"✅ 真正的转换成功: {output_file}")
        
        # 显示文件大小
        if os.path.exists(output_file):
            size = os.path.getsize(output_file)
            print(f"📊 文件大小: {size} 字节")
            
            # 显示前几行内容
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()[:20]
                print("\n📄 文件预览（前20行）:")
                print("".join(lines))
    else:
        print("❌ 转换失败！")

if __name__ == "__main__":
    main()
