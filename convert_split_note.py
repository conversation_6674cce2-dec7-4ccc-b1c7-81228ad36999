#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
转换拆分后的.note文件
作者: Claude 4.0 sonnet
功能: 使用项目自带的转换功能转换拆分后的.note文件
"""

import os
import sys
import logging

# 添加项目路径到sys.path
sys.path.append('.')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def convert_note_file(note_file_path):
    """转换单个.note文件"""
    try:
        # 导入转换模块
        from core.covert import YoudaoNoteConvert
        
        # 创建转换器实例
        converter = YoudaoNoteConvert()
        
        # 转换文件
        md_content = converter.covert_xml_to_markdown(note_file_path)
        
        # 生成输出文件名
        md_file_path = note_file_path.replace('.note', '.md')
        
        # 写入Markdown文件
        with open(md_file_path, 'w', encoding='utf-8') as f:
            f.write(md_content)
        
        logging.info(f"✅ 转换成功: {note_file_path} -> {md_file_path}")
        return True
        
    except Exception as e:
        logging.error(f"❌ 转换失败 {note_file_path}: {e}")
        return False

def main():
    """主函数"""
    # 要转换的文件
    note_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1)_第1部分.note"
    
    if not os.path.exists(note_file):
        print(f"❌ 文件不存在: {note_file}")
        return
    
    print(f"开始转换文件: {note_file}")
    
    if convert_note_file(note_file):
        print("✅ 转换完成！")
        
        # 显示转换后的文件
        md_file = note_file.replace('.note', '.md')
        if os.path.exists(md_file):
            print(f"📄 生成的Markdown文件: {md_file}")
            
            # 显示文件大小
            size = os.path.getsize(md_file)
            print(f"📊 文件大小: {size} 字节")
    else:
        print("❌ 转换失败！")

if __name__ == "__main__":
    main()
