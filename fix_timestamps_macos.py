#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
有道云笔记时间戳修复工具 - macOS专用版本
作者: Claude 4.0 sonnet
功能: 通过有道云笔记API获取原始时间戳，并修复本地文件的时间戳（包括创建时间）
"""

import json
import logging
import os
import platform
import shutil
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path

import requests

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('timestamp_fix_macos.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class TimestampFixerMacOS:
    """macOS专用时间戳修复工具"""
    
    def __init__(self, cookies_path="cookies.json", youdaonote_dir="youdaonote"):
        self.cookies_path = cookies_path
        self.youdaonote_dir = youdaonote_dir
        self.session = requests.session()
        self.cstk = None
        self.backup_dir = f"{youdaonote_dir}_backup_macos_{int(time.time())}"
        
        # 设置请求头
        self.session.headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36",
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        }
        
        # API URLs
        self.ROOT_ID_URL = "https://note.youdao.com/yws/api/personal/file?method=getByPath&keyfrom=web&cstk={cstk}"
        self.DIR_MES_URL = "https://note.youdao.com/yws/api/personal/file/{dir_id}?all=true&f=true&len=1000&sort=1&isReverse=false&method=listPageByParentId&keyfrom=web&cstk={cstk}"
    
    def create_backup(self):
        """创建备份"""
        if not os.path.exists(self.youdaonote_dir):
            logging.error(f"源目录 {self.youdaonote_dir} 不存在")
            return False
            
        logging.info(f"正在创建备份到 {self.backup_dir}...")
        try:
            shutil.copytree(self.youdaonote_dir, self.backup_dir)
            logging.info(f"备份创建成功: {self.backup_dir}")
            return True
        except Exception as e:
            logging.error(f"创建备份失败: {e}")
            return False
    
    def login_by_cookies(self):
        """通过cookies登录"""
        try:
            with open(self.cookies_path, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            cookies = {}
            for cookie in cookies_data['cookies']:
                cookies[cookie[0]] = cookie[1]
                if cookie[0] == 'YNOTE_CSTK':
                    self.cstk = cookie[1]
            
            self.session.cookies.update(cookies)
            
            if not self.cstk:
                raise Exception("未找到YNOTE_CSTK")
                
            logging.info("登录成功")
            return True
            
        except Exception as e:
            logging.error(f"登录失败: {e}")
            return False
    
    def get_root_dir_info(self):
        """获取根目录信息"""
        data = {"path": "/", "entire": "true", "purge": "false", "cstk": self.cstk}
        response = self.session.post(self.ROOT_ID_URL.format(cstk=self.cstk), data=data)
        return response.json()
    
    def get_dir_info_by_id(self, dir_id):
        """根据目录ID获取目录信息"""
        url = self.DIR_MES_URL.format(dir_id=dir_id, cstk=self.cstk)
        response = self.session.get(url)
        return response.json()
    
    def collect_file_timestamps(self, dir_id="", path_prefix=""):
        """递归收集所有文件的时间戳信息"""
        timestamps = {}
        
        try:
            if not dir_id:
                # 获取根目录ID
                root_info = self.get_root_dir_info()
                dir_id = root_info['fileEntry']['id']
            
            dir_info = self.get_dir_info_by_id(dir_id)
            
            if 'entries' not in dir_info:
                return timestamps
                
            for entry in dir_info['entries']:
                file_entry = entry['fileEntry']
                name = file_entry['name']
                file_id = file_entry['id']
                is_dir = file_entry.get('dir', False)
                
                current_path = os.path.join(path_prefix, name) if path_prefix else name
                
                if is_dir:
                    # 递归处理子目录
                    sub_timestamps = self.collect_file_timestamps(file_id, current_path)
                    timestamps.update(sub_timestamps)
                else:
                    # 处理文件
                    modify_time_raw = file_entry.get('modifyTimeForSort', 0)
                    create_time_raw = file_entry.get('createTimeForSort', 0)
                    
                    # 时间戳转换：如果是毫秒级别（大于10位数），则除以1000
                    modify_time = modify_time_raw / 1000 if modify_time_raw > 9999999999 else modify_time_raw
                    create_time = create_time_raw / 1000 if create_time_raw > 9999999999 else create_time_raw
                    
                    # 处理文件名，移除非法字符
                    safe_name = self.optimize_file_name(name)
                    
                    # 如果是.note文件，转换为.md格式
                    if safe_name.endswith('.note'):
                        safe_name = safe_name[:-5] + '.md'
                    elif not safe_name.endswith(('.md', '.txt', '.doc', '.docx')):
                        safe_name += '.md'
                    
                    local_path = os.path.join(current_path, safe_name) if current_path else safe_name
                    
                    timestamps[local_path] = {
                        'modify_time': modify_time,
                        'create_time': create_time,
                        'original_name': name
                    }
                    
        except Exception as e:
            logging.error(f"收集时间戳失败 (dir_id: {dir_id}): {e}")
            
        return timestamps
    
    def optimize_file_name(self, name):
        """优化文件名，移除非法字符"""
        import re
        # 移除非法字符
        name = re.sub(r'[\\/:\*\?"<>\|]', '', name)
        return name
    
    def find_matching_file(self, api_path):
        """查找与API路径匹配的本地文件"""
        # 提取文件名
        filename = os.path.basename(api_path)
        
        # 转换文件名格式
        if filename.endswith('.note'):
            target_filename = filename[:-5] + '.md'
        else:
            target_filename = filename
        
        # 在youdaonote目录中递归查找
        for root, dirs, files in os.walk(self.youdaonote_dir):
            for file in files:
                if file == target_filename:
                    return os.path.join(root, file)
        
        return None
    
    def set_file_timestamps_macos(self, file_path, create_time, modify_time):
        """在macOS上设置文件的创建时间和修改时间"""
        try:
            # 1. 使用touch命令设置修改时间
            modify_time_str = datetime.fromtimestamp(modify_time).strftime('%Y%m%d%H%M.%S')
            subprocess.run(['touch', '-t', modify_time_str, file_path], check=True)
            
            # 2. 使用SetFile命令设置创建时间（需要安装Xcode Command Line Tools）
            create_time_str = datetime.fromtimestamp(create_time).strftime('%m/%d/%Y %H:%M:%S')
            try:
                subprocess.run(['SetFile', '-d', create_time_str, file_path], check=True)
                logging.debug(f"使用SetFile设置创建时间成功: {file_path}")
            except (subprocess.CalledProcessError, FileNotFoundError):
                # 如果SetFile不可用，尝试使用Python的os.utime
                logging.debug(f"SetFile不可用，使用os.utime: {file_path}")
                os.utime(file_path, (create_time, modify_time))
            
            return True
            
        except Exception as e:
            logging.error(f"设置时间戳失败 {file_path}: {e}")
            return False
    
    def apply_timestamps(self, timestamps):
        """应用时间戳到本地文件"""
        success_count = 0
        fail_count = 0
        
        for relative_path, time_info in timestamps.items():
            local_file_path = os.path.join(self.youdaonote_dir, relative_path)
            
            if not os.path.exists(local_file_path):
                # 尝试查找匹配的文件
                actual_file = self.find_matching_file(relative_path)
                if actual_file:
                    local_file_path = actual_file
                    logging.info(f"找到匹配文件: {relative_path} -> {actual_file}")
                else:
                    logging.warning(f"文件不存在，跳过: {local_file_path}")
                    continue
            
            try:
                modify_time = time_info['modify_time']
                create_time = time_info['create_time']
                
                # 使用macOS专用方法设置时间戳
                if self.set_file_timestamps_macos(local_file_path, create_time, modify_time):
                    logging.info(f"✅ 修复时间戳: {local_file_path}")
                    logging.info(f"   创建时间: {datetime.fromtimestamp(create_time)}")
                    logging.info(f"   修改时间: {datetime.fromtimestamp(modify_time)}")
                    success_count += 1
                else:
                    fail_count += 1
                
            except Exception as e:
                logging.error(f"❌ 修复失败: {local_file_path} - {e}")
                fail_count += 1
        
        return success_count, fail_count
    
    def run(self):
        """运行修复程序"""
        logging.info("=== 有道云笔记时间戳修复工具 (macOS专用版) ===")
        
        # 检查系统
        if platform.system() != "Darwin":
            logging.error("此工具仅适用于macOS系统")
            return False
        
        # 1. 创建备份
        if not self.create_backup():
            return False
        
        # 2. 登录
        if not self.login_by_cookies():
            return False
        
        # 3. 收集时间戳信息
        logging.info("正在收集有道云笔记的时间戳信息...")
        timestamps = self.collect_file_timestamps()
        
        if not timestamps:
            logging.error("未收集到任何时间戳信息")
            return False
        
        logging.info(f"收集到 {len(timestamps)} 个文件的时间戳信息")
        
        # 4. 应用时间戳
        logging.info("开始修复本地文件时间戳...")
        success_count, fail_count = self.apply_timestamps(timestamps)
        
        # 5. 总结
        logging.info("=== 修复完成 ===")
        logging.info(f"成功修复: {success_count} 个文件")
        logging.info(f"修复失败: {fail_count} 个文件")
        logging.info(f"备份位置: {self.backup_dir}")
        
        return True

def main():
    """主函数"""
    fixer = TimestampFixerMacOS()
    
    try:
        success = fixer.run()
        if success:
            logging.info("时间戳修复完成！")
        else:
            logging.error("时间戳修复失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        logging.info("用户中断操作")
        sys.exit(1)
    except Exception as e:
        logging.error(f"程序异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
