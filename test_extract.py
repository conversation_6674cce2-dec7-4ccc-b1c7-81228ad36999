#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文本提取
"""

import html
import re
import os

def test_extract():
    """测试提取功能"""
    try:
        input_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).note"
        
        print(f"开始测试文件: {input_file}")
        
        if not os.path.exists(input_file):
            print(f"文件不存在: {input_file}")
            return
        
        # 读取文件
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"文件大小: {len(content)} 字符")
        
        # 提取文本内容
        text_pattern = r'<text>([^<]*)</text>'
        matches = re.findall(text_pattern, content)
        
        print(f"找到 {len(matches)} 个文本段落")
        
        # 显示前10个文本段落
        for i, match in enumerate(matches[:10]):
            if match.strip():
                decoded_text = html.unescape(match)
                cleaned_text = re.sub(r'\s+', ' ', decoded_text).strip()
                print(f"{i+1}. {cleaned_text[:100]}...")
        
        # 提取图片
        image_pattern = r'<source>([^<]+)</source>'
        image_matches = re.findall(image_pattern, content)
        
        print(f"找到 {len(image_matches)} 个图片链接")
        
        # 创建简单的Markdown
        markdown_lines = []
        markdown_lines.append("# JavaScript 知识点大杂烩\n\n")
        
        for i, match in enumerate(matches):
            if match.strip():
                decoded_text = html.unescape(match)
                cleaned_text = re.sub(r'\s+', ' ', decoded_text).strip()
                if cleaned_text:
                    markdown_lines.append(f"{cleaned_text}\n\n")
        
        # 写入文件
        output_file = "youdaonote/我的资源/笔记总结/测试提取结果.md"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(''.join(markdown_lines))
        
        print(f"✅ 测试成功，输出文件: {output_file}")
        
        # 显示文件大小
        size = os.path.getsize(output_file)
        print(f"输出文件大小: {size} 字节")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_extract()
