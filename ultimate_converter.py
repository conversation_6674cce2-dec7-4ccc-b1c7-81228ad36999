#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极有道云笔记转Markdown工具
作者: Claude 4.0 sonnet
功能: 真正解析单行XML并转换为完美的Markdown格式
"""

import html
import logging
import os
import re
import xml.etree.ElementTree as ET
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class UltimateConverter:
    """终极有道云笔记转换器"""

    def __init__(self):
        self.namespace = {'note': 'http://note.youdao.com'}

    def clean_xml_content(self, content):
        """清理XML内容"""
        # 先备份原始内容
        original_content = content

        try:
            # 1. 处理HTML实体
            content = content.replace('&amp;', '&')  # 先还原
            content = content.replace('&', '&amp;')  # 重新转义
            content = content.replace('&amp;amp;', '&amp;')  # 避免重复转义
            content = content.replace('&amp;lt;', '&lt;')
            content = content.replace('&amp;gt;', '&gt;')

            # 2. 移除有问题的属性
            content = re.sub(r'\s+list-id="[^"]*"', '', content)
            content = re.sub(r'\s+id="null"', '', content)

            # 3. 修复可能的XML格式问题
            content = re.sub(r'<([^/>]+)>\s*</\1>', r'<\1/>', content)  # 空标签简化

            return content
        except Exception as e:
            logging.warning(f"XML清理失败，使用原始内容: {e}")
            return original_content

    def parse_note_file(self, file_path):
        """解析.note文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            # 清理XML内容
            content = self.clean_xml_content(content)

            # 解析XML
            root = ET.fromstring(content)
            return root
        except Exception as e:
            logging.error(f"XML解析失败: {e}")
            # 尝试使用BeautifulSoup作为备选方案
            try:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(content, 'xml')
                # 转换为ElementTree格式
                import xml.etree.ElementTree as ET
                root = ET.fromstring(str(soup))
                return root
            except ImportError:
                logging.error("BeautifulSoup未安装，无法使用备选解析方案")
            except Exception as e2:
                logging.error(f"备选解析方案也失败: {e2}")

            return None

    def extract_text_from_element(self, element):
        """从元素中提取纯文本内容"""
        text_elem = element.find('text')
        if text_elem is not None and text_elem.text:
            # 解码HTML实体
            text = html.unescape(text_elem.text)
            # 清理多余的空白字符
            text = re.sub(r'\s+', ' ', text).strip()
            return text
        return ""

    def get_heading_level(self, element):
        """获取标题级别"""
        level_attr = element.get('level')
        if level_attr:
            try:
                level = int(level_attr)
                return min(level, 6)  # Markdown最多支持6级标题
            except:
                return 2
        return 2  # 默认二级标题

    def process_image(self, element):
        """处理图片元素"""
        source_elem = element.find('source')
        if source_elem is not None and source_elem.text:
            image_url = source_elem.text
            # 提取图片描述
            alt_text = "图片"
            if "WEBRESOURCE" in image_url:
                alt_text = "有道云图片"
            return f"![{alt_text}]({image_url})\n\n"
        return ""

    def analyze_text_style(self, element):
        """分析文本样式"""
        text = self.extract_text_from_element(element)
        if not text:
            return ""

        # 检查是否有样式信息
        inline_styles = element.find('inline-styles')
        if inline_styles is None:
            return text

        # 分析样式
        is_bold = False
        is_code = False
        is_important = False

        # 检查粗体
        bold_elements = inline_styles.findall('.//bold')
        for bold in bold_elements:
            value_elem = bold.find('value')
            if value_elem is not None and value_elem.text == 'true':
                is_bold = True
                break

        # 检查颜色和背景色（可能表示代码或重要内容）
        color_elements = inline_styles.findall('.//color')
        back_color_elements = inline_styles.findall('.//back-color')

        # 判断是否是代码
        code_indicators = [
            'function', 'var ', 'let ', 'const ', 'return',
            'console.log', 'document.', 'window.',
            '=>', '===', '!==', '&&', '||',
            'Array.', 'Object.', '.push(', '.map(',
            'addEventListener', 'querySelector',
            '{', '}', '()', ';', '//'
        ]

        code_count = sum(1 for indicator in code_indicators if indicator in text)

        # 如果包含多个代码特征，认为是代码
        if code_count >= 2 or (code_count >= 1 and len(text) < 100 and any(char in text for char in ['{', '}', '()', ';'])):
            is_code = True

        # 如果有特殊颜色标记，可能是重要内容
        if (color_elements or back_color_elements) and not is_code:
            is_important = True

        # 应用样式
        if is_code:
            if '\n' in text or len(text) > 100:
                return f"```javascript\n{text}\n```"
            else:
                return f"`{text}`"
        elif is_bold and len(text) < 200:
            return f"**{text}**"
        elif is_important and len(text) < 100:
            return f"**{text}**"
        else:
            return text

    def convert_to_markdown(self, root):
        """将XML转换为真正的Markdown"""
        markdown_lines = []

        # 查找body元素
        body = root.find('body')
        if body is None:
            logging.error("未找到body元素")
            return ""

        element_count = 0
        current_section = ""

        for element in body:
            element_count += 1

            if element.tag == 'heading':
                # 处理标题
                text = self.analyze_text_style(element)
                if text.strip():
                    level = self.get_heading_level(element)
                    # 清理标题文本
                    clean_text = re.sub(r'[*`]', '', text).strip()
                    if clean_text:
                        markdown_lines.append(f"{'#' * level} {clean_text}\n\n")
                        current_section = clean_text
                else:
                    # 空标题，添加分隔线
                    markdown_lines.append("---\n\n")

            elif element.tag == 'para':
                # 处理段落
                text = self.analyze_text_style(element)
                if text.strip():
                    # 检查是否是列表项
                    if re.match(r'^\d+[、）)]', text) or re.match(r'^[（(]\d+[）)]', text):
                        # 数字列表
                        clean_text = re.sub(r'^[（(]?\d+[、）)]\s*', '', text)
                        markdown_lines.append(f"1. {clean_text}\n\n")
                    elif text.startswith(('一、', '二、', '三、', '四、', '五、', '六、', '七、', '八、', '九、', '十、')):
                        # 中文数字标题
                        markdown_lines.append(f"## {text}\n\n")
                    elif text.startswith(('1、', '2、', '3、', '4、', '5、', '6、', '7、', '8、', '9、')):
                        # 数字小标题
                        clean_text = re.sub(r'^\d+、\s*', '', text)
                        markdown_lines.append(f"### {clean_text}\n\n")
                    else:
                        markdown_lines.append(f"{text}\n\n")
                else:
                    # 空段落，添加空行
                    markdown_lines.append("\n")

            elif element.tag == 'image':
                # 处理图片
                image_md = self.process_image(element)
                if image_md:
                    markdown_lines.append(image_md)

        logging.info(f"处理了 {element_count} 个元素")
        return ''.join(markdown_lines)

    def add_header(self, markdown_content, original_filename):
        """添加文档头部"""
        # 从文件名中提取时间信息
        time_match = re.search(r'\((\d{4}-\d{2}-\d{2})\s+(\d{2})(\d{2})\)', original_filename)
        if time_match:
            date_str = time_match.group(1)
            hour_str = time_match.group(2)
            minute_str = time_match.group(3)
            full_time_str = f"{date_str} {hour_str}:{minute_str}:00"
        else:
            full_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        header = f"""# JavaScript 知识点大杂烩

> 📅 创建时间: {full_time_str}
> 📝 数据来源: 有道云笔记导出
> 🎯 内容: JavaScript 前端开发技巧和知识点整理

---

"""
        return header + markdown_content

    def convert_file(self, input_path, output_path=None):
        """转换单个文件"""
        if not os.path.exists(input_path):
            logging.error(f"输入文件不存在: {input_path}")
            return False

        # 解析文件
        logging.info(f"开始解析文件: {input_path}")
        root = self.parse_note_file(input_path)
        if root is None:
            return False

        # 转换为Markdown
        logging.info("开始转换为Markdown...")
        markdown_content = self.convert_to_markdown(root)

        if not markdown_content.strip():
            logging.error("转换后的内容为空")
            return False

        # 添加文档头部
        original_filename = os.path.basename(input_path)
        markdown_content = self.add_header(markdown_content, original_filename)

        # 确定输出路径
        if output_path is None:
            base_name = os.path.splitext(input_path)[0]
            output_path = f"{base_name}_终极版.md"

        # 写入Markdown文件
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            logging.info(f"转换成功: {input_path} -> {output_path}")
            return True

        except Exception as e:
            logging.error(f"写入文件失败: {e}")
            return False

def main():
    """主函数"""
    converter = UltimateConverter()

    # 直接转换原始大文件
    input_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).note"
    output_file = "youdaonote/我的资源/笔记总结/JavaScript知识点大杂烩_终极版.md"

    print(f"🚀 开始终极转换: {input_file}")

    if converter.convert_file(input_file, output_file):
        print(f"✅ 终极转换成功: {output_file}")

        # 显示文件信息
        if os.path.exists(output_file):
            size = os.path.getsize(output_file)
            print(f"📊 转换后文件大小: {size} 字节")

            # 统计行数和内容
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"📄 总行数: {len(lines)} 行")

                # 统计内容类型
                heading_count = len([line for line in lines if line.startswith('#')])
                code_block_count = lines.count('```\n')
                image_count = len([line for line in lines if line.startswith('![')])

                print(f"📋 内容统计:")
                print(f"   - 标题数量: {heading_count}")
                print(f"   - 代码块数量: {code_block_count}")
                print(f"   - 图片数量: {image_count}")

                # 显示前50行内容
                print(f"\n📄 文件预览（前50行）:")
                print("".join(lines[:50]))

                if len(lines) > 50:
                    print(f"\n... 还有 {len(lines) - 50} 行内容")
    else:
        print("❌ 转换失败！")

if __name__ == "__main__":
    main()
