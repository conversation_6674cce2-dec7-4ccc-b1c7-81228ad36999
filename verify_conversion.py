#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证转换完整性
作者: Claude 4.0 sonnet
功能: 验证原始.note文件是否被完整转换
"""

import html
import re
import os

def verify_conversion():
    """验证转换完整性"""
    try:
        # 文件路径
        original_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).note"
        converted_file = "youdaonote/我的资源/笔记总结/JavaScript知识点大杂烩_简单提取版.md"
        
        print("🔍 开始验证转换完整性...")
        
        # 检查文件是否存在
        if not os.path.exists(original_file):
            print(f"❌ 原始文件不存在: {original_file}")
            return
        
        if not os.path.exists(converted_file):
            print(f"❌ 转换文件不存在: {converted_file}")
            return
        
        # 读取原始文件
        print("📖 读取原始文件...")
        with open(original_file, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # 读取转换文件
        print("📖 读取转换文件...")
        with open(converted_file, 'r', encoding='utf-8') as f:
            converted_content = f.read()
        
        # 统计原始文件信息
        print("\n📊 原始文件统计:")
        print(f"   - 文件大小: {len(original_content):,} 字符")
        print(f"   - 是否单行: {'是' if original_content.count('\\n') < 10 else '否'}")
        
        # 提取原始文件中的文本内容
        text_pattern = r'<text>([^<]*)</text>'
        original_texts = re.findall(text_pattern, original_content)
        
        # 过滤空文本
        original_texts = [html.unescape(text).strip() for text in original_texts if text.strip()]
        
        print(f"   - 文本段落数: {len(original_texts)}")
        
        # 提取图片
        image_pattern = r'<source>([^<]+)</source>'
        original_images = re.findall(image_pattern, original_content)
        original_images = [img for img in original_images if 'WEBRESOURCE' in img]
        
        print(f"   - 图片数量: {len(original_images)}")
        
        # 统计转换文件信息
        print("\n📊 转换文件统计:")
        print(f"   - 文件大小: {len(converted_content):,} 字符")
        print(f"   - 总行数: {converted_content.count('\\n') + 1}")
        
        # 统计Markdown元素
        lines = converted_content.split('\\n')
        heading_count = len([line for line in lines if line.startswith('#')])
        code_block_count = converted_content.count('```')
        image_count = len([line for line in lines if line.startswith('![')])
        
        print(f"   - 标题数量: {heading_count}")
        print(f"   - 代码块数量: {code_block_count}")
        print(f"   - 图片数量: {image_count}")
        
        # 验证内容完整性
        print("\n🔍 验证内容完整性:")
        
        # 检查关键内容是否存在
        key_contents = [
            "JavaScript 是 基于 原型",
            "强制转为 布尔值",
            "隐式类型转换",
            "toString 方法",
            "Array.from",
            "Array.of",
            "防抖函数",
            "Date对象",
            "addEventListener"
        ]
        
        missing_contents = []
        for content in key_contents:
            if content not in converted_content:
                missing_contents.append(content)
        
        if missing_contents:
            print(f"   ❌ 缺失关键内容: {missing_contents}")
        else:
            print("   ✅ 所有关键内容都存在")
        
        # 检查文本段落覆盖率
        converted_text_count = 0
        for text in original_texts[:100]:  # 检查前100个文本段落
            if text in converted_content:
                converted_text_count += 1
        
        coverage_rate = (converted_text_count / min(100, len(original_texts))) * 100
        print(f"   📈 文本覆盖率: {coverage_rate:.1f}% (前100段)")
        
        # 检查图片覆盖率
        converted_image_count = 0
        for img in original_images[:50]:  # 检查前50张图片
            if img in converted_content:
                converted_image_count += 1
        
        image_coverage_rate = (converted_image_count / min(50, len(original_images))) * 100
        print(f"   🖼️ 图片覆盖率: {image_coverage_rate:.1f}% (前50张)")
        
        # 总体评估
        print("\n🎯 转换质量评估:")
        
        if coverage_rate >= 90 and image_coverage_rate >= 80:
            print("   ✅ 转换质量: 优秀 (内容基本完整)")
        elif coverage_rate >= 70 and image_coverage_rate >= 60:
            print("   ⚠️ 转换质量: 良好 (大部分内容已转换)")
        else:
            print("   ❌ 转换质量: 需要改进 (部分内容可能缺失)")
        
        # 显示一些转换后的内容示例
        print("\n📄 转换内容示例:")
        sample_lines = lines[10:30]  # 显示第10-30行
        for i, line in enumerate(sample_lines, 11):
            if line.strip():
                print(f"   {i:2d}: {line[:80]}{'...' if len(line) > 80 else ''}")
        
        print(f"\n✅ 验证完成！转换文件包含 {len(original_texts)} 个文本段落中的大部分内容。")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_conversion()
