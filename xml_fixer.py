#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XML修复工具
作者: Claude 4.0 sonnet
功能: 修复有道云笔记XML文件中缺失的list-id属性
"""

import os
import re
import shutil

def fix_xml_structure(input_file, output_file):
    """修复XML结构中缺失的list-id属性"""
    try:
        # 读取原始文件
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 读取文件: {input_file}")
        print(f"📊 原始文件大小: {len(content)} 字符")
        
        # 查找所有缺少list-id的list-item元素
        pattern = r'<list-item level="(\d+)"(?!\s+list-id)'
        matches = re.findall(pattern, content)
        print(f"🔍 找到 {len(matches)} 个缺少list-id的list-item元素")
        
        if len(matches) == 0:
            print("✅ 文件结构正常，无需修复")
            return True
        
        # 从head部分找到现有的list-id
        head_pattern = r'<list id="([^"]+)" type="ordered"/>'
        head_match = re.search(head_pattern, content)
        
        if head_match:
            existing_list_id = head_match.group(1)
            print(f"📋 找到现有的list-id: {existing_list_id}")
        else:
            # 如果没有找到，创建一个新的list-id
            existing_list_id = "rhfO-1668067544614"
            print(f"📋 使用默认list-id: {existing_list_id}")
            
            # 在head部分添加list定义
            head_insert_pattern = r'(<head>.*?)(</head>)'
            head_replacement = rf'\1<list id="{existing_list_id}" type="ordered"/>\2'
            content = re.sub(head_insert_pattern, head_replacement, content, flags=re.DOTALL)
        
        # 修复所有缺少list-id的list-item元素
        def replace_func(match):
            level = match.group(1)
            return f'<list-item level="{level}" list-id="{existing_list_id}"'
        
        new_content = re.sub(pattern, replace_func, content)
        
        # 统计修复数量
        fixed_count = len(re.findall(r'<list-item level="\d+" list-id="[^"]+"', new_content))
        print(f"🔧 修复完成，共修复 {len(matches)} 个list-item元素")
        print(f"✅ 验证：现在有 {fixed_count} 个正确的list-item元素")
        
        # 写入修复后的文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"💾 修复后的文件已保存: {output_file}")
        print(f"📊 新文件大小: {len(new_content)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def convert_fixed_note_to_markdown(note_file):
    """使用有道云笔记工具转换修复后的文件"""
    try:
        # 导入有道云笔记转换模块
        import sys
        sys.path.append('.')
        from core.covert import YoudaoNoteConvert
        
        print(f"🔄 开始转换修复后的文件...")
        
        # 转换为Markdown
        YoudaoNoteConvert.covert_xml_to_markdown(note_file)
        
        # 检查是否生成了对应的.md文件
        md_file = note_file.replace('.note', '.md')
        if os.path.exists(md_file):
            print(f"✅ 转换成功: {md_file}")
            
            # 显示文件信息
            size = os.path.getsize(md_file)
            print(f"📊 Markdown文件大小: {size} 字节")
            
            # 读取并显示前几行
            with open(md_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"📋 文件预览 (前10行):")
            for i, line in enumerate(lines[:10]):
                print(f"  {i+1:2d}. {line.rstrip()}")
            
            return True
        else:
            print(f"❌ 转换失败，未生成Markdown文件")
            return False
            
    except Exception as e:
        print(f"❌ 转换过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    input_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).note"
    fixed_file = "youdaonote/我的资源/笔记总结/知识点大杂烩_修复版.note"
    
    print("🔧 开始XML结构修复...")
    
    if not os.path.exists(input_file):
        print(f"❌ 输入文件不存在: {input_file}")
        return
    
    # 步骤1: 修复XML结构
    if fix_xml_structure(input_file, fixed_file):
        print("\n✅ XML结构修复成功!")
        
        # 步骤2: 转换为Markdown
        print("\n" + "="*50)
        if convert_fixed_note_to_markdown(fixed_file):
            print("\n🎉 完整转换流程成功!")
            
            # 清理临时文件
            try:
                os.remove(fixed_file)
                print(f"🗑️ 已清理临时文件: {fixed_file}")
            except:
                pass
        else:
            print("\n❌ Markdown转换失败!")
    else:
        print("❌ XML结构修复失败!")

if __name__ == "__main__":
    main()
