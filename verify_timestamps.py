#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间戳验证工具
验证文件时间戳的来源和准确性
"""

import json
import os
import time
from datetime import datetime

import requests

class TimestampVerifier:
    """时间戳验证工具"""
    
    def __init__(self, cookies_path="cookies.json"):
        self.cookies_path = cookies_path
        self.session = requests.session()
        self.cstk = None
        
        # 设置请求头
        self.session.headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36",
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        }
        
        # API URLs
        self.ROOT_ID_URL = "https://note.youdao.com/yws/api/personal/file?method=getByPath&keyfrom=web&cstk={cstk}"
        self.DIR_MES_URL = "https://note.youdao.com/yws/api/personal/file/{dir_id}?all=true&f=true&len=1000&sort=1&isReverse=false&method=listPageByParentId&keyfrom=web&cstk={cstk}"
    
    def login_by_cookies(self):
        """通过cookies登录"""
        try:
            with open(self.cookies_path, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            cookies = {}
            for cookie in cookies_data['cookies']:
                cookies[cookie[0]] = cookie[1]
                if cookie[0] == 'YNOTE_CSTK':
                    self.cstk = cookie[1]
            
            self.session.cookies.update(cookies)
            return True
            
        except Exception as e:
            print(f"登录失败: {e}")
            return False
    
    def get_file_timestamps_from_api(self, filename):
        """从API获取指定文件的时间戳"""
        try:
            # 获取根目录信息
            data = {"path": "/", "entire": "true", "purge": "false", "cstk": self.cstk}
            response = self.session.post(self.ROOT_ID_URL.format(cstk=self.cstk), data=data)
            root_info = response.json()
            root_id = root_info['fileEntry']['id']
            
            # 递归搜索文件
            return self._search_file_in_dir(root_id, filename)
            
        except Exception as e:
            print(f"API查询失败: {e}")
            return None
    
    def _search_file_in_dir(self, dir_id, target_filename, path_prefix=""):
        """在目录中递归搜索文件"""
        try:
            url = self.DIR_MES_URL.format(dir_id=dir_id, cstk=self.cstk)
            response = self.session.get(url)
            dir_info = response.json()
            
            if 'entries' not in dir_info:
                return None
                
            for entry in dir_info['entries']:
                file_entry = entry['fileEntry']
                name = file_entry['name']
                file_id = file_entry['id']
                is_dir = file_entry.get('dir', False)
                
                if is_dir:
                    # 递归搜索子目录
                    result = self._search_file_in_dir(file_id, target_filename, 
                                                    os.path.join(path_prefix, name) if path_prefix else name)
                    if result:
                        return result
                else:
                    # 检查文件名匹配
                    if name == target_filename or name == target_filename.replace('.md', '.note'):
                        modify_time_raw = file_entry.get('modifyTimeForSort', 0)
                        create_time_raw = file_entry.get('createTimeForSort', 0)
                        
                        # 时间戳转换
                        modify_time = modify_time_raw / 1000 if modify_time_raw > 9999999999 else modify_time_raw
                        create_time = create_time_raw / 1000 if create_time_raw > 9999999999 else create_time_raw
                        
                        return {
                            'api_name': name,
                            'api_path': os.path.join(path_prefix, name) if path_prefix else name,
                            'create_time_raw': create_time_raw,
                            'modify_time_raw': modify_time_raw,
                            'create_time': create_time,
                            'modify_time': modify_time,
                            'create_time_str': datetime.fromtimestamp(create_time).strftime('%Y-%m-%d %H:%M:%S'),
                            'modify_time_str': datetime.fromtimestamp(modify_time).strftime('%Y-%m-%d %H:%M:%S')
                        }
            
            return None
            
        except Exception as e:
            print(f"搜索目录失败: {e}")
            return None
    
    def get_file_timestamps_from_filesystem(self, filepath):
        """从文件系统获取时间戳"""
        try:
            stat_info = os.stat(filepath)
            return {
                'modify_time': stat_info.st_mtime,
                'create_time': stat_info.st_birthtime,  # macOS特有
                'access_time': stat_info.st_atime,
                'change_time': stat_info.st_ctime,
                'modify_time_str': datetime.fromtimestamp(stat_info.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                'create_time_str': datetime.fromtimestamp(stat_info.st_birthtime).strftime('%Y-%m-%d %H:%M:%S'),
                'access_time_str': datetime.fromtimestamp(stat_info.st_atime).strftime('%Y-%m-%d %H:%M:%S'),
                'change_time_str': datetime.fromtimestamp(stat_info.st_ctime).strftime('%Y-%m-%d %H:%M:%S')
            }
        except Exception as e:
            print(f"读取文件系统时间戳失败: {e}")
            return None
    
    def verify_file(self, filepath):
        """验证单个文件的时间戳"""
        filename = os.path.basename(filepath)
        
        print(f"\n{'='*60}")
        print(f"验证文件: {filepath}")
        print(f"{'='*60}")
        
        # 1. 从API获取原始时间戳
        print("\n📡 从有道云笔记API获取原始时间戳...")
        api_data = self.get_file_timestamps_from_api(filename)
        
        if api_data:
            print(f"✅ API查询成功:")
            print(f"   原始文件名: {api_data['api_name']}")
            print(f"   API路径: {api_data['api_path']}")
            print(f"   原始创建时间戳: {api_data['create_time_raw']} (毫秒)")
            print(f"   原始修改时间戳: {api_data['modify_time_raw']} (毫秒)")
            print(f"   转换后创建时间: {api_data['create_time_str']}")
            print(f"   转换后修改时间: {api_data['modify_time_str']}")
        else:
            print("❌ API查询失败")
            return False
        
        # 2. 从文件系统获取当前时间戳
        print(f"\n💾 从文件系统获取当前时间戳...")
        fs_data = self.get_file_timestamps_from_filesystem(filepath)
        
        if fs_data:
            print(f"✅ 文件系统查询成功:")
            print(f"   当前创建时间: {fs_data['create_time_str']}")
            print(f"   当前修改时间: {fs_data['modify_time_str']}")
            print(f"   当前访问时间: {fs_data['access_time_str']}")
            print(f"   当前变更时间: {fs_data['change_time_str']}")
        else:
            print("❌ 文件系统查询失败")
            return False
        
        # 3. 对比验证
        print(f"\n🔍 时间戳对比验证...")
        
        # 允许1秒的误差（由于时间戳精度问题）
        create_diff = abs(api_data['create_time'] - fs_data['create_time'])
        modify_diff = abs(api_data['modify_time'] - fs_data['modify_time'])
        
        print(f"   创建时间差异: {create_diff:.2f} 秒")
        print(f"   修改时间差异: {modify_diff:.2f} 秒")
        
        if create_diff <= 1 and modify_diff <= 1:
            print("✅ 验证通过: 文件时间戳与API数据一致!")
            print("📋 时间戳来源确认:")
            print("   - 创建时间: 来自有道云笔记API的 createTimeForSort 字段")
            print("   - 修改时间: 来自有道云笔记API的 modifyTimeForSort 字段")
            print("   - 存储位置: macOS文件系统的 Birth Time 和 Modification Time")
            return True
        else:
            print("❌ 验证失败: 时间戳不一致")
            return False
    
    def run_verification(self, files_to_verify):
        """运行验证"""
        print("🔍 时间戳来源验证工具")
        print("=" * 60)
        
        if not self.login_by_cookies():
            return False
        
        success_count = 0
        total_count = len(files_to_verify)
        
        for filepath in files_to_verify:
            if self.verify_file(filepath):
                success_count += 1
        
        print(f"\n{'='*60}")
        print(f"验证完成: {success_count}/{total_count} 个文件验证通过")
        print(f"{'='*60}")
        
        return success_count == total_count

def main():
    """主函数"""
    verifier = TimestampVerifier()
    
    # 要验证的文件列表
    files_to_verify = [
        "youdaonote/Vue.js 解读.md",
        "youdaonote/JS代码整洁.md",
        "youdaonote/JS 高級.md"
    ]
    
    verifier.run_verification(files_to_verify)

if __name__ == "__main__":
    main()
