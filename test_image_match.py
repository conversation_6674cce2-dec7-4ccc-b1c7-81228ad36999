#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图片匹配
"""

import os
import re

def test_image_match():
    """测试图片匹配"""
    # 测试URL
    test_url = "https://note.youdao.com/yws/res/1949/WEBRESOURCEb7b529a394d8b1ab707b791f718f7349"
    
    # 提取资源ID
    pattern = r'https://note\.youdao\.com/yws/res/\d+/WEBRESOURCE([a-f0-9]+)'
    match = re.search(pattern, test_url)
    if match:
        resource_id = match.group(1)
        print(f"提取的资源ID: {resource_id}")
    else:
        print("未能提取资源ID")
        return
    
    # 检查本地图片文件
    images_dir = "youdaonote/我的资源/笔记总结/images"
    if not os.path.exists(images_dir):
        print(f"图片目录不存在: {images_dir}")
        return
    
    # 查找匹配的文件
    matching_files = []
    for filename in os.listdir(images_dir):
        if resource_id in filename:
            matching_files.append(filename)
    
    print(f"找到匹配的文件: {matching_files}")
    
    # 显示前10个图片文件
    print("\n前10个图片文件:")
    count = 0
    for filename in os.listdir(images_dir):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
            print(f"  {count+1}. {filename}")
            count += 1
            if count >= 10:
                break

if __name__ == "__main__":
    test_image_match()
