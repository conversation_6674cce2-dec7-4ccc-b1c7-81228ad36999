#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拆分大型.note文件工具
作者: Claude 4.0 sonnet
功能: 将大型.note文件拆分为多个小的.note文件，然后可以用项目自带的转换功能转换
"""

import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def split_note_file(input_path, lines_per_file=500):
    """按行数拆分.note文件"""
    if not os.path.exists(input_path):
        logging.error(f"输入文件不存在: {input_path}")
        return False
    
    try:
        # 读取文件
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查文件是否是有效的XML
        if not content.strip().startswith('<?xml'):
            logging.error("文件不是有效的XML格式")
            return False
        
        # 找到XML的主要结构
        lines = content.split('\n')
        total_lines = len(lines)
        
        # 找到<body>标签的位置
        body_start = -1
        body_end = -1
        
        for i, line in enumerate(lines):
            if '<body>' in line:
                body_start = i
            elif '</body>' in line:
                body_end = i
                break
        
        if body_start == -1 or body_end == -1:
            logging.error("无法找到<body>标签")
            return False
        
        # 提取头部和尾部
        header_lines = lines[:body_start + 1]  # 包含<body>
        footer_lines = lines[body_end:]  # 包含</body>
        body_lines = lines[body_start + 1:body_end]  # 不包含<body>和</body>
        
        # 计算需要拆分的文件数量
        body_line_count = len(body_lines)
        total_parts = (body_line_count + lines_per_file - 1) // lines_per_file
        
        logging.info(f"文件包含 {total_lines} 行，body部分 {body_line_count} 行")
        logging.info(f"将拆分为 {total_parts} 个文件")
        
        # 获取文件信息
        base_dir = os.path.dirname(input_path)
        filename = os.path.basename(input_path)
        base_name = os.path.splitext(filename)[0]
        
        # 拆分文件
        for part_num in range(1, total_parts + 1):
            start_idx = (part_num - 1) * lines_per_file
            end_idx = min(start_idx + lines_per_file, body_line_count)
            
            # 获取这部分的body内容
            part_body_lines = body_lines[start_idx:end_idx]
            
            # 组合完整的XML内容
            part_lines = header_lines + part_body_lines + footer_lines
            part_content = '\n'.join(part_lines)
            
            # 创建输出文件名
            output_filename = f"{base_name}_第{part_num}部分.note"
            output_path = os.path.join(base_dir, output_filename)
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(part_content)
            
            logging.info(f"✅ 创建文件: {output_filename} (body行 {start_idx+1}-{end_idx})")
        
        logging.info(f"🎉 拆分完成！共创建 {total_parts} 个.note文件")
        return True
        
    except Exception as e:
        logging.error(f"拆分文件失败: {e}")
        return False

def main():
    """主函数"""
    input_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).note"
    
    print("开始拆分大型.note文件...")
    if split_note_file(input_file, lines_per_file=300):
        print("✅ .note文件拆分完成！")
        print("\n接下来您可以：")
        print("1. 使用项目自带的转换功能转换这些小文件")
        print("2. 运行: python pull.py 来转换所有.note文件为.md")
    else:
        print("❌ .note文件拆分失败！")

if __name__ == "__main__":
    main()
