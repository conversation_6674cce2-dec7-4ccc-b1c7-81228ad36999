#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的有道云笔记转Markdown工具
作者: Claude 4.0 sonnet
功能: 直接处理大文件，分段转换为可读的Markdown
"""

import html
import logging
import os
import re
import xml.etree.ElementTree as ET
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class CompleteConverter:
    """完整的有道云笔记转换器"""
    
    def __init__(self):
        pass
    
    def parse_note_file(self, file_path):
        """解析.note文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析XML
            root = ET.fromstring(content)
            return root
        except Exception as e:
            logging.error(f"解析文件失败: {e}")
            return None
    
    def extract_text_from_element(self, element):
        """从元素中提取纯文本内容"""
        text_elem = element.find('.//text')
        if text_elem is not None and text_elem.text:
            # 解码HTML实体
            text = html.unescape(text_elem.text)
            # 清理多余的空白字符，但保留必要的换行
            text = re.sub(r'[ \t]+', ' ', text)  # 合并空格和制表符
            text = re.sub(r'\n\s*\n', '\n\n', text)  # 合并多个换行为双换行
            return text.strip()
        return ""
    
    def get_heading_level(self, element):
        """获取标题级别"""
        level_attr = element.get('level')
        if level_attr:
            return min(int(level_attr), 6)  # Markdown最多支持6级标题
        return 2  # 默认二级标题
    
    def process_image(self, element):
        """处理图片元素"""
        source_elem = element.find('.//source')
        if source_elem is not None and source_elem.text:
            image_url = source_elem.text
            # 提取图片文件名作为alt文本
            alt_text = "图片"
            if "WEBRESOURCE" in image_url:
                alt_text = "有道云图片"
            return f"![{alt_text}]({image_url})\n\n"
        return ""
    
    def is_code_like(self, text):
        """判断文本是否像代码"""
        if not text or len(text) < 3:
            return False
            
        code_indicators = [
            'function', 'var ', 'let ', 'const ', 'return',
            'console.log', 'document.', 'window.',
            '=>', '===', '!==', '&&', '||',
            'Array.', 'Object.', '.push(', '.map(',
            '{', '}', '()', ';', '//'
        ]
        
        # 计算代码特征
        code_count = sum(1 for indicator in code_indicators if indicator in text)
        
        # 如果包含多个代码特征，或者是短文本但有明显代码特征
        if code_count >= 2:
            return True
        if len(text) < 100 and code_count >= 1 and any(char in text for char in ['{', '}', '()', ';']):
            return True
            
        return False
    
    def process_styles(self, element):
        """处理样式信息，转换为Markdown格式"""
        text = self.extract_text_from_element(element)
        if not text:
            return ""
        
        # 检查是否有样式信息
        inline_styles = element.find('.//inline-styles')
        if inline_styles is None:
            return text
        
        # 处理粗体
        bold_elements = inline_styles.findall('.//bold')
        if bold_elements:
            for bold in bold_elements:
                if bold.find('value') is not None and bold.find('value').text == 'true':
                    # 如果文本较短且是重点内容，加粗
                    if len(text) < 200 and not self.is_code_like(text):
                        text = f"**{text}**"
                    break
        
        # 处理颜色和背景色（可能是重要内容或代码）
        color_elements = inline_styles.findall('.//color')
        back_color_elements = inline_styles.findall('.//back-color')
        
        if color_elements or back_color_elements:
            # 检查是否是代码
            if self.is_code_like(text):
                # 多行代码用代码块，单行用内联代码
                if '\n' in text or len(text) > 100:
                    return f"```javascript\n{text}\n```"
                else:
                    return f"`{text}`"
            elif len(text) < 100:
                # 短文本可能是重要标记
                return f"**{text}**"
        
        return text
    
    def convert_to_markdown(self, root):
        """将XML转换为真正的Markdown"""
        markdown_lines = []
        
        # 查找body元素
        body = root.find('.//body')
        if body is None:
            logging.error("未找到body元素")
            return ""
        
        element_count = 0
        for element in body:
            element_count += 1
            
            if element.tag == 'heading':
                # 处理标题
                text = self.process_styles(element)
                if text.strip():
                    level = self.get_heading_level(element)
                    # 清理标题文本
                    clean_text = re.sub(r'[*`]', '', text).strip()
                    if clean_text:
                        markdown_lines.append(f"{'#' * level} {clean_text}\n\n")
                else:
                    # 空标题，添加分隔线
                    markdown_lines.append("---\n\n")
            
            elif element.tag == 'para':
                # 处理段落
                text = self.process_styles(element)
                if text.strip():
                    markdown_lines.append(f"{text}\n\n")
                else:
                    # 空段落，添加空行
                    markdown_lines.append("\n")
            
            elif element.tag == 'image':
                # 处理图片
                image_md = self.process_image(element)
                if image_md:
                    markdown_lines.append(image_md)
        
        logging.info(f"处理了 {element_count} 个元素")
        return ''.join(markdown_lines)
    
    def add_timestamp_header(self, markdown_content, original_filename):
        """添加时间戳头部"""
        # 从文件名中提取时间信息
        time_match = re.search(r'\((\d{4}-\d{2}-\d{2})\s+(\d{2})(\d{2})\)', original_filename)
        if time_match:
            date_str = time_match.group(1)
            hour_str = time_match.group(2)
            minute_str = time_match.group(3)
            full_time_str = f"{date_str} {hour_str}:{minute_str}:00"
        else:
            full_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        header = f"""<!--
📝 笔记信息
创建时间: {full_time_str}
修改时间: {full_time_str}
原始文件: {original_filename}
数据来源: 有道云笔记导出
-->

# 知识点大杂烩

> 这是从有道云笔记导出的JavaScript知识点整理，包含了2021年8月记录的各种前端开发技巧和知识点。

"""
        return header + markdown_content
    
    def convert_file(self, input_path, output_path=None):
        """转换单个文件"""
        if not os.path.exists(input_path):
            logging.error(f"输入文件不存在: {input_path}")
            return False
        
        # 解析文件
        logging.info(f"开始解析文件: {input_path}")
        root = self.parse_note_file(input_path)
        if root is None:
            return False
        
        # 转换为Markdown
        logging.info("开始转换为Markdown...")
        markdown_content = self.convert_to_markdown(root)
        
        if not markdown_content.strip():
            logging.error("转换后的内容为空")
            return False
        
        # 添加时间戳头部
        original_filename = os.path.basename(input_path)
        markdown_content = self.add_timestamp_header(markdown_content, original_filename)
        
        # 确定输出路径
        if output_path is None:
            base_name = os.path.splitext(input_path)[0]
            output_path = f"{base_name}_完整转换.md"
        
        # 写入Markdown文件
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            logging.info(f"转换成功: {input_path} -> {output_path}")
            return True
            
        except Exception as e:
            logging.error(f"写入文件失败: {e}")
            return False

def main():
    """主函数"""
    converter = CompleteConverter()
    
    # 直接转换原始大文件
    input_file = "youdaonote/我的资源/笔记总结/知识点大杂烩 (2021-08-06 1313)(1).note"
    output_file = "youdaonote/我的资源/笔记总结/知识点大杂烩_完整版.md"
    
    print(f"开始转换大文件: {input_file}")
    
    if converter.convert_file(input_file, output_file):
        print(f"✅ 完整转换成功: {output_file}")
        
        # 显示文件信息
        if os.path.exists(output_file):
            size = os.path.getsize(output_file)
            print(f"📊 转换后文件大小: {size} 字节")
            
            # 统计行数
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"📄 总行数: {len(lines)} 行")
                
                # 显示前30行内容
                print("\n📄 文件预览（前30行）:")
                print("".join(lines[:30]))
                
                if len(lines) > 30:
                    print(f"\n... 还有 {len(lines) - 30} 行内容")
    else:
        print("❌ 转换失败！")

if __name__ == "__main__":
    main()
